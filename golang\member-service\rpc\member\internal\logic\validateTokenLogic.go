package logic

import (
	"context"
	"member/middleware/JWT"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type ValidateTokenLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewValidateTokenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ValidateTokenLogic {
	return &ValidateTokenLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ValidateTokenLogic) ValidateToken(in *member.ValidateTokenReq) (*member.ValidateTokenResp, error) {
	if in.Token == "" {
		return &member.ValidateTokenResp{
			Code:    400,
			Message: "Token不能为空",
		}, nil
	}

	// 解析Token获取用户信息
	userInfo, err := JWT.ParseToken(in.Token, l.svcCtx.Config.JwtAuth.AccessSecret)
	if err != nil {
		return &member.ValidateTokenResp{
			Code:    401,
			Message: "Token无效或已过期",
		}, nil
	}

	// 验证用户是否存在
	memberData, err := l.svcCtx.MemberModel.FindByID(userInfo.MemberID)
	if err != nil {
		return &member.ValidateTokenResp{
			Code:    404,
			Message: "用户不存在",
		}, nil
	}

	// 检查用户状态
	if memberData.Status != 1 {
		return &member.ValidateTokenResp{
			Code:    403,
			Message: "用户已被禁用",
		}, nil
	}

	// 构造返回的用户信息
	memberInfo := &member.Member{
		Id:           memberData.ID,
		Username:     memberData.Username,
		Mobile:       memberData.Mobile,
		Email:        memberData.Email,
		Nickname:     memberData.Nickname,
		HeadPortrait: memberData.HeadPortrait,
		Type:         memberData.Type,
		Status:       memberData.Status,
		CreatedAt:    memberData.CreatedAt,
		UpdatedAt:    memberData.UpdatedAt,
	}

	return &member.ValidateTokenResp{
		Code:       200,
		Message:    "Token验证成功",
		MemberId:   userInfo.MemberID,
		MemberInfo: memberInfo,
	}, nil
}
