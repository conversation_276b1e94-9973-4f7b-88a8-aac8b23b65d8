# Git相关
.git
.gitignore
.gitattributes

# 文档和README
*.md
README.*
CHANGELOG.*
LICENSE*

# 临时文件和缓存
*.tmp
*.log
*.cache
.DS_Store
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 构建产物和二进制文件
*.exe
*.out
*.so
*.dll
*.dylib

# 测试相关
*_test.go
test/
tests/
coverage.*

# Node.js (如果有前端项目)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 配置和环境文件(保留示例文件)
.env.local
.env.development
.env.production

# Docker相关
Dockerfile.dev
docker-compose.dev.yml
docker-compose.override.yml

# 日志文件
logs/
*.log

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 临时目录
tmp/
temp/ 