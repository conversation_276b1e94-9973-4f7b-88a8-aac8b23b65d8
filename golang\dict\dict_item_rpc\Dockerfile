# 使用多阶段构建优化镜像大小
FROM golang:alpine AS builder

LABEL stage=gobuilder

ENV CGO_ENABLED 0
ENV GOPROXY=https://goproxy.cn,https://goproxy.io,https://proxy.golang.org,direct
ENV GOSUMDB=sum.golang.google.cn
ENV GO111MODULE=on

# 配置Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    echo "http://mirrors.ustc.edu.cn/alpine/v3.18/main" >> /etc/apk/repositories && \
    echo "http://mirrors.ustc.edu.cn/alpine/v3.18/community" >> /etc/apk/repositories

# 安装必要的系统依赖，使用缓存优化
RUN apk update --no-cache && \
    apk add --no-cache tzdata ca-certificates git && \
    update-ca-certificates

WORKDIR /build

# 复制整个golang目录（context是./golang）
COPY . .

# 切换到dict/dict_item_rpc目录
WORKDIR /build/dict/dict_item_rpc

# 整理依赖关系
RUN go mod tidy

# 下载依赖，增加重试机制和并行下载
RUN go mod download -x || go mod download -x || go mod download -x

# 构建应用，添加编译优化
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-s -w -extldflags '-static'" -o /app/dictitem dictitem.go

# 最终运行镜像使用scratch减小镜像大小
FROM scratch

# 复制时区和证书信息
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app
COPY --from=builder /app/dictitem /app/dictitem
COPY --from=builder /build/dict/dict_item_rpc/etc /app/etc

# 暴露端口
EXPOSE 9008

CMD ["./dictitem", "-f", "etc/dictitem.yaml"]
