package logic

import (
	"context"
	"member/model"
	"time"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterLogic {
	return &RegisterLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 用户认证相关
func (l *RegisterLogic) Register(in *member.RegisterReq) (*member.RegisterResp, error) {

	// 参数校验
	if in.Username == "" {
		return &member.RegisterResp{
			Code:    400,
			Message: "用户名/手机号不能为空",
		}, nil
	}

	if in.Password == "" {
		return &member.RegisterResp{
			Code:    400,
			Message: "密码不能为空",
		}, nil
	}
	// 在注册之前先检查该用户是否已经注册过
	// 校验用户名
	if existUser, _ := l.svcCtx.MemberModel.FindByUsername(in.Username); existUser != nil {
		return &member.RegisterResp{
			Code:    400,
			Message: "该账号已存在,请登录",
		}, nil
	}
	// 校验手机号
	if existPhone, _ := l.svcCtx.MemberModel.FindByMobile(in.Mobile); existPhone != nil {
		return &member.RegisterResp{
			Code:    400,
			Message: "该账号已存在,请登录",
		}, nil
	}

	// 创建用户记录
	newMember := &model.Member{
		Username:     in.Username,
		Mobile:       in.Mobile,
		PasswordHash: in.Password,
		Email:        in.Email,
		Nickname:     in.Nickname,
		Source:       in.Source,
		Type:         1, // 默认为普通会员
		Status:       1, // 启用状态
	}

	err := l.svcCtx.MemberModel.Create(newMember, l.svcCtx.Config.JwtAuth.AccessSecret)
	if err != nil {
		return &member.RegisterResp{
			Code:    500,
			Message: "注册失败",
		}, nil
	}

	// 创建对应的账户记录
	now := time.Now().Unix()
	newAccount := &model.MemberAccount{
		MemberID:           newMember.ID,
		MerchantID:         in.MerchantId,
		StoreID:            in.StoreId,
		MemberType:         1, // 普通会员
		UserMoney:          0.0,
		AccumulateMoney:    0.0,
		GiveMoney:          0.0,
		ConsumeMoney:       0.0,
		FrozenMoney:        0.0,
		UserIntegral:       0,
		AccumulateIntegral: 0,
		GiveIntegral:       0,
		ConsumeIntegral:    0.0,
		FrozenIntegral:     0,
		UserGrowth:         0,
		AccumulateGrowth:   0,
		ConsumeGrowth:      0,
		FrozenGrowth:       0,
		EconomizeMoney:     0.0,
		AccumulateDrawnMoney: 0.0,
		Status:             1, // 启用状态
		CreatedAt:          now,
		UpdatedAt:          now,
	}
	
	err = l.svcCtx.MemberAccountModel.Create(newAccount)
	if err != nil {
		// 如果账户创建失败，理论上应该回滚用户创建，但这里简化处理
		logx.Errorf("创建用户账户失败: %v", err)
		return &member.RegisterResp{
			Code:    500,
			Message: "注册失败，请重试",
		}, nil
	}

	return &member.RegisterResp{
		Code:     200,
		Message:  "注册成功",
		MemberId: newMember.ID,
	}, nil
}
