syntax = "v1"

info (
	title:   "字典系统API"
	desc:    "字典系统的HTTP接口定义"
	author:  "系统开发者"
	email:   "<EMAIL>"
	version: "v1.0"
)

// ===== 基础数据结构 =====
type Dict {
	Id          int64  `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Remark      string `json:"remark"`
	Status      int32  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

type DictCategory {
	Id          int64  `json:"id"`
	DictId      int64  `json:"dict_id"`
	Name        string `json:"name"`
	Status      int32  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

type DictItem {
	Id          int64  `json:"id"`
	DictId      int64  `json:"dict_id"`
	CategoryId  int64  `json:"category_id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Status      int32  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

// ===== 字典相关请求响应 =====
type CreateDictReq {
	Code   string `json:"code"`
	Name   string `json:"name"`
	Remark string `json:"remark"`
	Status int32  `json:"status"`
}

type CreateDictResp {
	Id      int64  `json:"id"`
	Message string `json:"message"`
}

type UpdateDictReq {
	Id     int64  `path:"id"`
	Code   string `json:"code"`
	Name   string `json:"name"`
	Remark string `json:"remark"`
	Status int32  `json:"status"`
}

type UpdateDictResp {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type DeleteDictReq {
	Id int64 `path:"id"`
}

type DeleteDictResp {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type GetDictReq {
	Id int64 `path:"id"`
}

type GetDictResp {
	Dict    Dict   `json:"dict"`
	Message string `json:"message"`
}

type ListDictReq {
	Page     int32  `form:"page,default=1"`
	PageSize int32  `form:"page_size,default=10"`
	Code     string `form:"code,optional"`
	Name     string `form:"name,optional"`
	Status   int32  `form:"status,default=-1"`
}

type ListDictResp {
	Total   int64  `json:"total"`
	List    []Dict `json:"list"`
	Message string `json:"message"`
}

// ===== 字典分类相关请求响应 =====
type CreateDictCategoryReq {
	DictId int64  `json:"dict_id"`
	Name   string `json:"name"`
	Status int32  `json:"status"`
}

type CreateDictCategoryResp {
	Id      int64  `json:"id"`
	Message string `json:"message"`
}

type UpdateDictCategoryReq {
	Id     int64  `path:"id"`
	DictId int64  `json:"dict_id"`
	Name   string `json:"name"`
	Status int32  `json:"status"`
}

type UpdateDictCategoryResp {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type DeleteDictCategoryReq {
	Id int64 `path:"id"`
}

type DeleteDictCategoryResp {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type GetDictCategoryReq {
	Id int64 `path:"id"`
}

type GetDictCategoryResp {
	Category DictCategory `json:"category"`
	Message  string       `json:"message"`
}

type ListDictCategoryReq {
	Page     int32  `form:"page,default=1"`
	PageSize int32  `form:"page_size,default=10"`
	Name     string `form:"name,optional"`
	Status   int32  `form:"status,default=-1"`
	DictId   int64  `form:"dict_id,optional"`
}

type ListDictCategoryResp {
	Total   int64          `json:"total"`
	List    []DictCategory `json:"list"`
	Message string         `json:"message"`
}

// ===== 字典项相关请求响应 =====
type CreateDictItemReq {
	DictId     int64  `json:"dict_id"`
	CategoryId int64  `json:"category_id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Status     int32  `json:"status"`
}

type CreateDictItemResp {
	Id      int64  `json:"id"`
	Message string `json:"message"`
}

type UpdateDictItemReq {
	Id         int64  `path:"id"`
	DictId     int64  `json:"dict_id"`
	CategoryId int64  `json:"category_id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Status     int32  `json:"status"`
}

type UpdateDictItemResp {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type DeleteDictItemReq {
	Id int64 `path:"id"`
}

type DeleteDictItemResp {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type GetDictItemReq {
	Id int64 `path:"id"`
}

type GetDictItemResp {
	Item    DictItem `json:"item"`
	Message string   `json:"message"`
}

type ListDictItemReq {
	Page       int32  `form:"page,default=1"`
	PageSize   int32  `form:"page_size,default=10"`
	DictId     int64  `form:"dict_id,optional"`
	CategoryId int64  `form:"category_id,optional"`
	Code       string `form:"code,optional"`
	Name       string `form:"name,optional"`
	Status     int32  `form:"status,default=-1"`
}

type ListDictItemResp {
	Total   int64      `json:"total"`
	List    []DictItem `json:"list"`
	Message string     `json:"message"`
}

// ===== 服务定义 =====
@server (
	group:  dict
	prefix: /api/v1/dict
)
service DictSystemApi {
	// ===== 字典管理 =====
	@doc "创建字典"
	@handler CreateDict
	post /create (CreateDictReq) returns (CreateDictResp)

	@doc "更新字典"
	@handler UpdateDict
	put /update/:id (UpdateDictReq) returns (UpdateDictResp)

	@doc "删除字典"
	@handler DeleteDict
	delete /delete/:id (DeleteDictReq) returns (DeleteDictResp)

	@doc "获取字典详情"
	@handler GetDict
	get /detail/:id (GetDictReq) returns (GetDictResp)

	@doc "字典列表"
	@handler ListDict
	get /list (ListDictReq) returns (ListDictResp)
}

@server (
	group:  dict_category
	prefix: /api/v1/dict/category
)
service DictSystemApi {
	// ===== 字典分类管理 =====
	@doc "创建字典分类"
	@handler CreateDictCategory
	post /create (CreateDictCategoryReq) returns (CreateDictCategoryResp)

	@doc "更新字典分类"
	@handler UpdateDictCategory
	put /update/:id (UpdateDictCategoryReq) returns (UpdateDictCategoryResp)

	@doc "删除字典分类"
	@handler DeleteDictCategory
	delete /delete/:id (DeleteDictCategoryReq) returns (DeleteDictCategoryResp)

	@doc "获取字典分类详情"
	@handler GetDictCategory
	get /detail/:id (GetDictCategoryReq) returns (GetDictCategoryResp)

	@doc "字典分类列表"
	@handler ListDictCategory
	get /list (ListDictCategoryReq) returns (ListDictCategoryResp)
}

@server (
	group:  dict_item
	prefix: /api/v1/dict/item
)
service DictSystemApi {
	// ===== 字典项管理 =====
	@doc "创建字典项"
	@handler CreateDictItem
	post /create (CreateDictItemReq) returns (CreateDictItemResp)

	@doc "更新字典项"
	@handler UpdateDictItem
	put /update/:id (UpdateDictItemReq) returns (UpdateDictItemResp)

	@doc "删除字典项"
	@handler DeleteDictItem
	delete /delete/:id (DeleteDictItemReq) returns (DeleteDictItemResp)

	@doc "获取字典项详情"
	@handler GetDictItem
	get /detail/:id (GetDictItemReq) returns (GetDictItemResp)

	@doc "字典项列表"
	@handler ListDictItem
	get /list (ListDictItemReq) returns (ListDictItemResp)
}

