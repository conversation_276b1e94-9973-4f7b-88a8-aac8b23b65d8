﻿package dict

import (
	"context"
	"fmt"

	"api/internal/svc"
	"api/internal/types"
	"dict_rpc/dict"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除字典项列表
func NewDeleteDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictLogic {
	return &DeleteDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictLogic) DeleteDict(req *types.DeleteDictReq) (resp *types.DeleteDictResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_rpc服务
	respRpc,err:=l.svcCtx.DictRpc.DeleteDict(l.ctx, &dict.DeleteDictReq{
		Id: req.Id,
	})

	if err!=nil{
		return nil,fmt.Errorf("调用dict_rpc服务的DeleteDict方法失败: %v",err)
	}


	return &types.DeleteDictResp{
		Success: respRpc.Success,
		Message: respRpc.Message,
	},nil
}

