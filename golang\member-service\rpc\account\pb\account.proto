syntax = "proto3";

package account;

option go_package = "./account";

// ===== Account服务定义 =====
service AccountService {
  // 账户信息管理
  rpc CreateAccount(CreateAccountReq) returns (CreateAccountResp);
  rpc GetAccountInfo(GetAccountInfoReq) returns (GetAccountInfoResp);
  rpc GetAccountList(GetAccountListReq) returns (GetAccountListResp);

  // 余额操作
  rpc GetBalance(GetBalanceReq) returns (GetBalanceResp);
  rpc Recharge(RechargeReq) returns (RechargeResp);
  rpc Withdraw(WithdrawReq) returns (WithdrawResp);
  rpc FreezeMoney(FreezeMoneyReq) returns (CommonResp);
  rpc UnfreezeMoney(UnfreezeMoneyReq) returns (CommonResp);

  // 积分操作
  rpc GetIntegralInfo(GetIntegralInfoReq) returns (GetIntegralInfoResp);
  rpc AddIntegral(AddIntegralReq) returns (CommonResp);
  rpc ConsumeIntegral(ConsumeIntegralReq) returns (CommonResp);

  // 成长值操作
  rpc GetGrowthInfo(GetGrowthInfoReq) returns (GetGrowthInfoResp);
  rpc AddGrowth(AddGrowthReq) returns (CommonResp);

  // 账户日志
  rpc AddMoneyLog(AddMoneyLogReq) returns (CommonResp);
  rpc AddIntegralLog(AddIntegralLogReq) returns (CommonResp);
  rpc GetAccountLog(GetAccountLogReq) returns (GetAccountLogResp);
}

// ===== 通用响应 =====
message CommonResp {
  int32 code = 1;
  string message = 2;
}

// ===== 分页信息 =====
message PageInfo {
  int32 page = 1;
  int32 page_size = 2;
  int64 total = 3;
}

// ===== 账户信息（基于member_account表结构） =====
message MemberAccount {
  int64 id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  int64 member_id = 4;
  int32 member_type = 5; // 1:会员 2:后台管理员 3:商家管理员
  
  // 余额相关
  double user_money = 6;        // 当前余额
  double accumulate_money = 7;  // 累计余额
  double give_money = 8;        // 累计赠送余额
  double consume_money = 9;     // 累计消费金额
  double frozen_money = 10;     // 冻结金额
  
  // 积分相关
  int64 user_integral = 11;        // 当前积分
  int64 accumulate_integral = 12;  // 累计积分
  int64 give_integral = 13;        // 累计赠送积分
  double consume_integral = 14;    // 累计消费积分
  int64 frozen_integral = 15;      // 冻结积分
  
  // 成长值相关
  int64 user_growth = 16;        // 当前成长值
  int64 accumulate_growth = 17;  // 累计成长值
  int64 consume_growth = 18;     // 累计消费成长值
  int64 frozen_growth = 19;      // 冻结成长值
  
  // 其他
  double economize_money = 20;         // 已节约金额
  double accumulate_drawn_money = 21;  // 累计提现
  int32 status = 22;                   // 状态 -1:删除 0:禁用 1:启用
  int64 created_at = 23;               // 创建时间
  int64 updated_at = 24;               // 更新时间
}

// ===== 账户日志信息 =====
message AccountLog {
  int64 id = 1;
  int64 member_id = 2;
  int32 type = 3;           // 操作类型 1:余额 2:积分 3:成长值
  int32 operation = 4;      // 操作 1:增加 2:减少 3:冻结 4:解冻
  double amount = 5;        // 操作金额/数量
  double balance_before = 6; // 操作前余额
  double balance_after = 7;  // 操作后余额
  string reason = 8;        // 操作原因
  string remark = 9;        // 备注
  int64 created_at = 10;
}

// ===== 创建账户 =====
message CreateAccountReq {
  int64 member_id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  int32 member_type = 4;
}

message CreateAccountResp {
  int32 code = 1;
  string message = 2;
  int64 account_id = 3;
}

// ===== 获取账户信息 =====
message GetAccountInfoReq {
  int64 member_id = 1;
}

message GetAccountInfoResp {
  int32 code = 1;
  string message = 2;
  MemberAccount data = 3;
}

// ===== 获取账户列表 =====
message GetAccountListReq {
  int32 page = 1;
  int32 page_size = 2;
  int64 member_id = 3;
  int32 member_type = 4;
}

message GetAccountListResp {
  int32 code = 1;
  string message = 2;
  repeated MemberAccount list = 3;
  PageInfo page_info = 4;
}

// ===== 获取余额信息 =====
message GetBalanceReq {
  int64 member_id = 1;
}

message GetBalanceResp {
  int32 code = 1;
  string message = 2;
  double user_money = 3;        // 当前余额
  double accumulate_money = 4;  // 累计余额
  double frozen_money = 5;      // 冻结金额
  double consume_money = 6;     // 累计消费
}

// ===== 充值 =====
message RechargeReq {
  int64 member_id = 1;
  double amount = 2;
  string payment_method = 3; // 支付方式
  string order_no = 4;       // 订单号
  string remark = 5;         // 备注
}

message RechargeResp {
  int32 code = 1;
  string message = 2;
  string order_no = 3;
  double balance_after = 4; // 充值后余额
}

// ===== 提现 =====
message WithdrawReq {
  int64 member_id = 1;
  double amount = 2;
  string withdraw_account = 3; // 提现账户
  string remark = 4;           // 备注
}

message WithdrawResp {
  int32 code = 1;
  string message = 2;
  string withdraw_no = 3;   // 提现单号
  double balance_after = 4; // 提现后余额
}

// ===== 冻结资金 =====
message FreezeMoneyReq {
  int64 member_id = 1;
  double amount = 2;
  string reason = 3;
  int64 operator_id = 4;
}

// ===== 解冻资金 =====
message UnfreezeMoneyReq {
  int64 member_id = 1;
  double amount = 2;
  string reason = 3;
  int64 operator_id = 4;
}

// ===== 获取积分信息 =====
message GetIntegralInfoReq {
  int64 member_id = 1;
}

message GetIntegralInfoResp {
  int32 code = 1;
  string message = 2;
  int64 user_integral = 3;        // 当前积分
  int64 accumulate_integral = 4;  // 累计积分
  int64 frozen_integral = 5;      // 冻结积分
  double consume_integral = 6;    // 累计消费积分
}

// ===== 添加积分 =====
message AddIntegralReq {
  int64 member_id = 1;
  int64 amount = 2;
  string reason = 3;
  string remark = 4;
}

// ===== 消费积分 =====
message ConsumeIntegralReq {
  int64 member_id = 1;
  int64 amount = 2;
  string reason = 3;
  string remark = 4;
}

// ===== 获取成长值信息 =====
message GetGrowthInfoReq {
  int64 member_id = 1;
}

message GetGrowthInfoResp {
  int32 code = 1;
  string message = 2;
  int64 user_growth = 3;        // 当前成长值
  int64 accumulate_growth = 4;  // 累计成长值
  int64 consume_growth = 5;     // 累计消费成长值
  int64 frozen_growth = 6;      // 冻结成长值
}

// ===== 添加成长值 =====
message AddGrowthReq {
  int64 member_id = 1;
  int64 amount = 2;
  string reason = 3;
  string remark = 4;
}

// ===== 添加资金日志 =====
message AddMoneyLogReq {
  int64 member_id = 1;
  int32 operation = 2;      // 操作类型 1:增加 2:减少
  double amount = 3;        // 金额
  double balance_before = 4; // 操作前余额
  double balance_after = 5;  // 操作后余额
  string reason = 6;        // 原因
  string remark = 7;        // 备注
}

// ===== 添加积分日志 =====
message AddIntegralLogReq {
  int64 member_id = 1;
  int32 operation = 2;      // 操作类型 1:增加 2:减少
  int64 amount = 3;         // 积分数量
  int64 balance_before = 4; // 操作前积分
  int64 balance_after = 5;  // 操作后积分
  string reason = 6;        // 原因
  string remark = 7;        // 备注
}

// ===== 获取账户日志 =====
message GetAccountLogReq {
  int64 member_id = 1;
  int32 type = 2;     // 日志类型 1:余额 2:积分 3:成长值
  int32 page = 3;
  int32 page_size = 4;
}

message GetAccountLogResp {
  int32 code = 1;
  string message = 2;
  repeated AccountLog list = 3;
  PageInfo page_info = 4;
} 