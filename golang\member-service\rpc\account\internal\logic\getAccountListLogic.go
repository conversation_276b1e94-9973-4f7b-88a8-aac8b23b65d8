package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAccountListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetAccountListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAccountListLogic {
	return &GetAccountListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetAccountListLogic) GetAccountList(in *account.GetAccountListReq) (*account.GetAccountListResp, error) {
	// todo: add your logic here and delete this line

	return &account.GetAccountListResp{}, nil
}
