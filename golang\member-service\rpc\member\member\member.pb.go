// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.0
// source: pb/member.proto

package member

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ===== 通用响应 =====
type CommonResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonResp) Reset() {
	*x = CommonResp{}
	mi := &file_pb_member_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResp) ProtoMessage() {}

func (x *CommonResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResp.ProtoReflect.Descriptor instead.
func (*CommonResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{0}
}

func (x *CommonResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// ===== 分页信息 =====
type PageInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total         int64                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PageInfo) Reset() {
	*x = PageInfo{}
	mi := &file_pb_member_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo) ProtoMessage() {}

func (x *PageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo.ProtoReflect.Descriptor instead.
func (*PageInfo) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{1}
}

func (x *PageInfo) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageInfo) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageInfo) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// ===== 用户信息（基于member表结构） =====
type Member struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MerchantId          int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId             int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Username            string                 `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	PasswordHash        string                 `protobuf:"bytes,5,opt,name=password_hash,json=passwordHash,proto3" json:"password_hash,omitempty"`                     // 密码哈希
	AuthKey             string                 `protobuf:"bytes,6,opt,name=auth_key,json=authKey,proto3" json:"auth_key,omitempty"`                                    // 授权令牌
	PasswordResetToken  string                 `protobuf:"bytes,7,opt,name=password_reset_token,json=passwordResetToken,proto3" json:"password_reset_token,omitempty"` // 密码重置令牌
	MobileResetToken    string                 `protobuf:"bytes,8,opt,name=mobile_reset_token,json=mobileResetToken,proto3" json:"mobile_reset_token,omitempty"`       // 手机重置令牌
	Type                int32                  `protobuf:"varint,9,opt,name=type,proto3" json:"type,omitempty"`                                                        // 1:会员 2:后台管理员 3:商家管理员
	Realname            string                 `protobuf:"bytes,10,opt,name=realname,proto3" json:"realname,omitempty"`
	Nickname            string                 `protobuf:"bytes,11,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadPortrait        string                 `protobuf:"bytes,12,opt,name=head_portrait,json=headPortrait,proto3" json:"head_portrait,omitempty"`
	Gender              int32                  `protobuf:"varint,13,opt,name=gender,proto3" json:"gender,omitempty"` // 0:未知 1:男 2:女
	Qq                  string                 `protobuf:"bytes,14,opt,name=qq,proto3" json:"qq,omitempty"`
	Email               string                 `protobuf:"bytes,15,opt,name=email,proto3" json:"email,omitempty"`
	Birthday            string                 `protobuf:"bytes,16,opt,name=birthday,proto3" json:"birthday,omitempty"`
	ProvinceId          int64                  `protobuf:"varint,17,opt,name=province_id,json=provinceId,proto3" json:"province_id,omitempty"`
	CityId              int64                  `protobuf:"varint,18,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	AreaId              int64                  `protobuf:"varint,19,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	Address             string                 `protobuf:"bytes,20,opt,name=address,proto3" json:"address,omitempty"`
	Mobile              string                 `protobuf:"bytes,21,opt,name=mobile,proto3" json:"mobile,omitempty"`
	TelNo               string                 `protobuf:"bytes,22,opt,name=tel_no,json=telNo,proto3" json:"tel_no,omitempty"`
	BgImage             string                 `protobuf:"bytes,23,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	Description         string                 `protobuf:"bytes,24,opt,name=description,proto3" json:"description,omitempty"`
	VisitCount          int32                  `protobuf:"varint,25,opt,name=visit_count,json=visitCount,proto3" json:"visit_count,omitempty"`
	LastTime            int64                  `protobuf:"varint,26,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	LastIp              string                 `protobuf:"bytes,27,opt,name=last_ip,json=lastIp,proto3" json:"last_ip,omitempty"`
	Role                int32                  `protobuf:"varint,28,opt,name=role,proto3" json:"role,omitempty"`
	CurrentLevel        int32                  `protobuf:"varint,29,opt,name=current_level,json=currentLevel,proto3" json:"current_level,omitempty"`
	LevelExpirationTime int64                  `protobuf:"varint,30,opt,name=level_expiration_time,json=levelExpirationTime,proto3" json:"level_expiration_time,omitempty"`
	LevelBuyType        int32                  `protobuf:"varint,31,opt,name=level_buy_type,json=levelBuyType,proto3" json:"level_buy_type,omitempty"`
	Pid                 int64                  `protobuf:"varint,32,opt,name=pid,proto3" json:"pid,omitempty"`
	Level               int32                  `protobuf:"varint,33,opt,name=level,proto3" json:"level,omitempty"`
	Tree                string                 `protobuf:"bytes,34,opt,name=tree,proto3" json:"tree,omitempty"`
	PromoterCode        string                 `protobuf:"bytes,35,opt,name=promoter_code,json=promoterCode,proto3" json:"promoter_code,omitempty"`
	CertificationType   int32                  `protobuf:"varint,36,opt,name=certification_type,json=certificationType,proto3" json:"certification_type,omitempty"`
	Source              string                 `protobuf:"bytes,37,opt,name=source,proto3" json:"source,omitempty"`
	Status              int32                  `protobuf:"varint,38,opt,name=status,proto3" json:"status,omitempty"` // -1:删除 0:禁用 1:启用
	CreatedAt           int64                  `protobuf:"varint,39,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt           int64                  `protobuf:"varint,40,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	RegionId            int64                  `protobuf:"varint,41,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Member) Reset() {
	*x = Member{}
	mi := &file_pb_member_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Member) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Member) ProtoMessage() {}

func (x *Member) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Member.ProtoReflect.Descriptor instead.
func (*Member) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{2}
}

func (x *Member) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Member) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *Member) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *Member) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Member) GetPasswordHash() string {
	if x != nil {
		return x.PasswordHash
	}
	return ""
}

func (x *Member) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *Member) GetPasswordResetToken() string {
	if x != nil {
		return x.PasswordResetToken
	}
	return ""
}

func (x *Member) GetMobileResetToken() string {
	if x != nil {
		return x.MobileResetToken
	}
	return ""
}

func (x *Member) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Member) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

func (x *Member) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *Member) GetHeadPortrait() string {
	if x != nil {
		return x.HeadPortrait
	}
	return ""
}

func (x *Member) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *Member) GetQq() string {
	if x != nil {
		return x.Qq
	}
	return ""
}

func (x *Member) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Member) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *Member) GetProvinceId() int64 {
	if x != nil {
		return x.ProvinceId
	}
	return 0
}

func (x *Member) GetCityId() int64 {
	if x != nil {
		return x.CityId
	}
	return 0
}

func (x *Member) GetAreaId() int64 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *Member) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Member) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *Member) GetTelNo() string {
	if x != nil {
		return x.TelNo
	}
	return ""
}

func (x *Member) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *Member) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Member) GetVisitCount() int32 {
	if x != nil {
		return x.VisitCount
	}
	return 0
}

func (x *Member) GetLastTime() int64 {
	if x != nil {
		return x.LastTime
	}
	return 0
}

func (x *Member) GetLastIp() string {
	if x != nil {
		return x.LastIp
	}
	return ""
}

func (x *Member) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *Member) GetCurrentLevel() int32 {
	if x != nil {
		return x.CurrentLevel
	}
	return 0
}

func (x *Member) GetLevelExpirationTime() int64 {
	if x != nil {
		return x.LevelExpirationTime
	}
	return 0
}

func (x *Member) GetLevelBuyType() int32 {
	if x != nil {
		return x.LevelBuyType
	}
	return 0
}

func (x *Member) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *Member) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Member) GetTree() string {
	if x != nil {
		return x.Tree
	}
	return ""
}

func (x *Member) GetPromoterCode() string {
	if x != nil {
		return x.PromoterCode
	}
	return ""
}

func (x *Member) GetCertificationType() int32 {
	if x != nil {
		return x.CertificationType
	}
	return 0
}

func (x *Member) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Member) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Member) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Member) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *Member) GetRegionId() int64 {
	if x != nil {
		return x.RegionId
	}
	return 0
}

// ===== 用户公开信息（不包含敏感字段） =====
type MemberPublic struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MerchantId          int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId             int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Username            string                 `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Type                int32                  `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"` // 1:会员 2:后台管理员 3:商家管理员
	Realname            string                 `protobuf:"bytes,6,opt,name=realname,proto3" json:"realname,omitempty"`
	Nickname            string                 `protobuf:"bytes,7,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadPortrait        string                 `protobuf:"bytes,8,opt,name=head_portrait,json=headPortrait,proto3" json:"head_portrait,omitempty"`
	Gender              int32                  `protobuf:"varint,9,opt,name=gender,proto3" json:"gender,omitempty"` // 0:未知 1:男 2:女
	Qq                  string                 `protobuf:"bytes,10,opt,name=qq,proto3" json:"qq,omitempty"`
	Email               string                 `protobuf:"bytes,11,opt,name=email,proto3" json:"email,omitempty"`
	Birthday            string                 `protobuf:"bytes,12,opt,name=birthday,proto3" json:"birthday,omitempty"`
	ProvinceId          int64                  `protobuf:"varint,13,opt,name=province_id,json=provinceId,proto3" json:"province_id,omitempty"`
	CityId              int64                  `protobuf:"varint,14,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	AreaId              int64                  `protobuf:"varint,15,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	Address             string                 `protobuf:"bytes,16,opt,name=address,proto3" json:"address,omitempty"`
	Mobile              string                 `protobuf:"bytes,17,opt,name=mobile,proto3" json:"mobile,omitempty"`
	TelNo               string                 `protobuf:"bytes,18,opt,name=tel_no,json=telNo,proto3" json:"tel_no,omitempty"`
	BgImage             string                 `protobuf:"bytes,19,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	Description         string                 `protobuf:"bytes,20,opt,name=description,proto3" json:"description,omitempty"`
	VisitCount          int32                  `protobuf:"varint,21,opt,name=visit_count,json=visitCount,proto3" json:"visit_count,omitempty"`
	LastTime            int64                  `protobuf:"varint,22,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	LastIp              string                 `protobuf:"bytes,23,opt,name=last_ip,json=lastIp,proto3" json:"last_ip,omitempty"`
	Role                int32                  `protobuf:"varint,24,opt,name=role,proto3" json:"role,omitempty"`
	CurrentLevel        int32                  `protobuf:"varint,25,opt,name=current_level,json=currentLevel,proto3" json:"current_level,omitempty"`
	LevelExpirationTime int64                  `protobuf:"varint,26,opt,name=level_expiration_time,json=levelExpirationTime,proto3" json:"level_expiration_time,omitempty"`
	LevelBuyType        int32                  `protobuf:"varint,27,opt,name=level_buy_type,json=levelBuyType,proto3" json:"level_buy_type,omitempty"`
	Pid                 int64                  `protobuf:"varint,28,opt,name=pid,proto3" json:"pid,omitempty"`
	Level               int32                  `protobuf:"varint,29,opt,name=level,proto3" json:"level,omitempty"`
	Tree                string                 `protobuf:"bytes,30,opt,name=tree,proto3" json:"tree,omitempty"`
	PromoterCode        string                 `protobuf:"bytes,31,opt,name=promoter_code,json=promoterCode,proto3" json:"promoter_code,omitempty"`
	CertificationType   int32                  `protobuf:"varint,32,opt,name=certification_type,json=certificationType,proto3" json:"certification_type,omitempty"`
	Source              string                 `protobuf:"bytes,33,opt,name=source,proto3" json:"source,omitempty"`
	Status              int32                  `protobuf:"varint,34,opt,name=status,proto3" json:"status,omitempty"` // -1:删除 0:禁用 1:启用
	CreatedAt           int64                  `protobuf:"varint,35,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt           int64                  `protobuf:"varint,36,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	RegionId            int64                  `protobuf:"varint,37,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MemberPublic) Reset() {
	*x = MemberPublic{}
	mi := &file_pb_member_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemberPublic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberPublic) ProtoMessage() {}

func (x *MemberPublic) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberPublic.ProtoReflect.Descriptor instead.
func (*MemberPublic) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{3}
}

func (x *MemberPublic) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MemberPublic) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *MemberPublic) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *MemberPublic) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *MemberPublic) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *MemberPublic) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

func (x *MemberPublic) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *MemberPublic) GetHeadPortrait() string {
	if x != nil {
		return x.HeadPortrait
	}
	return ""
}

func (x *MemberPublic) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *MemberPublic) GetQq() string {
	if x != nil {
		return x.Qq
	}
	return ""
}

func (x *MemberPublic) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MemberPublic) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *MemberPublic) GetProvinceId() int64 {
	if x != nil {
		return x.ProvinceId
	}
	return 0
}

func (x *MemberPublic) GetCityId() int64 {
	if x != nil {
		return x.CityId
	}
	return 0
}

func (x *MemberPublic) GetAreaId() int64 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *MemberPublic) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *MemberPublic) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *MemberPublic) GetTelNo() string {
	if x != nil {
		return x.TelNo
	}
	return ""
}

func (x *MemberPublic) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *MemberPublic) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MemberPublic) GetVisitCount() int32 {
	if x != nil {
		return x.VisitCount
	}
	return 0
}

func (x *MemberPublic) GetLastTime() int64 {
	if x != nil {
		return x.LastTime
	}
	return 0
}

func (x *MemberPublic) GetLastIp() string {
	if x != nil {
		return x.LastIp
	}
	return ""
}

func (x *MemberPublic) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *MemberPublic) GetCurrentLevel() int32 {
	if x != nil {
		return x.CurrentLevel
	}
	return 0
}

func (x *MemberPublic) GetLevelExpirationTime() int64 {
	if x != nil {
		return x.LevelExpirationTime
	}
	return 0
}

func (x *MemberPublic) GetLevelBuyType() int32 {
	if x != nil {
		return x.LevelBuyType
	}
	return 0
}

func (x *MemberPublic) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *MemberPublic) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *MemberPublic) GetTree() string {
	if x != nil {
		return x.Tree
	}
	return ""
}

func (x *MemberPublic) GetPromoterCode() string {
	if x != nil {
		return x.PromoterCode
	}
	return ""
}

func (x *MemberPublic) GetCertificationType() int32 {
	if x != nil {
		return x.CertificationType
	}
	return 0
}

func (x *MemberPublic) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *MemberPublic) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MemberPublic) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MemberPublic) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *MemberPublic) GetRegionId() int64 {
	if x != nil {
		return x.RegionId
	}
	return 0
}

// ===== 实名认证信息（基于member_certification表） =====
type MemberCertification struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MerchantId        int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId           int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	MemberId          int64                  `protobuf:"varint,4,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	MemberType        int32                  `protobuf:"varint,5,opt,name=member_type,json=memberType,proto3" json:"member_type,omitempty"`
	Realname          string                 `protobuf:"bytes,6,opt,name=realname,proto3" json:"realname,omitempty"`
	IdentityCard      string                 `protobuf:"bytes,7,opt,name=identity_card,json=identityCard,proto3" json:"identity_card,omitempty"`
	IdentityCardFront string                 `protobuf:"bytes,8,opt,name=identity_card_front,json=identityCardFront,proto3" json:"identity_card_front,omitempty"`
	IdentityCardBack  string                 `protobuf:"bytes,9,opt,name=identity_card_back,json=identityCardBack,proto3" json:"identity_card_back,omitempty"`
	Gender            string                 `protobuf:"bytes,10,opt,name=gender,proto3" json:"gender,omitempty"`
	Birthday          string                 `protobuf:"bytes,11,opt,name=birthday,proto3" json:"birthday,omitempty"`
	FrontIsFake       int32                  `protobuf:"varint,12,opt,name=front_is_fake,json=frontIsFake,proto3" json:"front_is_fake,omitempty"`
	BackIsFake        int32                  `protobuf:"varint,13,opt,name=back_is_fake,json=backIsFake,proto3" json:"back_is_fake,omitempty"`
	Nationality       string                 `protobuf:"bytes,14,opt,name=nationality,proto3" json:"nationality,omitempty"`
	Address           string                 `protobuf:"bytes,15,opt,name=address,proto3" json:"address,omitempty"`
	StartDate         string                 `protobuf:"bytes,16,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate           string                 `protobuf:"bytes,17,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Issue             string                 `protobuf:"bytes,18,opt,name=issue,proto3" json:"issue,omitempty"`
	IsSelf            int32                  `protobuf:"varint,19,opt,name=is_self,json=isSelf,proto3" json:"is_self,omitempty"`
	Status            int32                  `protobuf:"varint,20,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt         int64                  `protobuf:"varint,21,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         int64                  `protobuf:"varint,22,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MemberCertification) Reset() {
	*x = MemberCertification{}
	mi := &file_pb_member_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemberCertification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberCertification) ProtoMessage() {}

func (x *MemberCertification) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberCertification.ProtoReflect.Descriptor instead.
func (*MemberCertification) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{4}
}

func (x *MemberCertification) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MemberCertification) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *MemberCertification) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *MemberCertification) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *MemberCertification) GetMemberType() int32 {
	if x != nil {
		return x.MemberType
	}
	return 0
}

func (x *MemberCertification) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

func (x *MemberCertification) GetIdentityCard() string {
	if x != nil {
		return x.IdentityCard
	}
	return ""
}

func (x *MemberCertification) GetIdentityCardFront() string {
	if x != nil {
		return x.IdentityCardFront
	}
	return ""
}

func (x *MemberCertification) GetIdentityCardBack() string {
	if x != nil {
		return x.IdentityCardBack
	}
	return ""
}

func (x *MemberCertification) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *MemberCertification) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *MemberCertification) GetFrontIsFake() int32 {
	if x != nil {
		return x.FrontIsFake
	}
	return 0
}

func (x *MemberCertification) GetBackIsFake() int32 {
	if x != nil {
		return x.BackIsFake
	}
	return 0
}

func (x *MemberCertification) GetNationality() string {
	if x != nil {
		return x.Nationality
	}
	return ""
}

func (x *MemberCertification) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *MemberCertification) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *MemberCertification) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *MemberCertification) GetIssue() string {
	if x != nil {
		return x.Issue
	}
	return ""
}

func (x *MemberCertification) GetIsSelf() int32 {
	if x != nil {
		return x.IsSelf
	}
	return 0
}

func (x *MemberCertification) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MemberCertification) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MemberCertification) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// ===== 用户统计信息（基于member_stat表） =====
type MemberStat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MerchantId    int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	MemberId      int64                  `protobuf:"varint,4,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	MemberType    int32                  `protobuf:"varint,5,opt,name=member_type,json=memberType,proto3" json:"member_type,omitempty"`
	NiceNum       int64                  `protobuf:"varint,6,opt,name=nice_num,json=niceNum,proto3" json:"nice_num,omitempty"`                 // 点赞数量
	DisagreeNum   int64                  `protobuf:"varint,7,opt,name=disagree_num,json=disagreeNum,proto3" json:"disagree_num,omitempty"`     // 不赞同数量
	TransmitNum   int64                  `protobuf:"varint,8,opt,name=transmit_num,json=transmitNum,proto3" json:"transmit_num,omitempty"`     // 转发数量
	CommentNum    int64                  `protobuf:"varint,9,opt,name=comment_num,json=commentNum,proto3" json:"comment_num,omitempty"`        // 评论数量
	CollectNum    int64                  `protobuf:"varint,10,opt,name=collect_num,json=collectNum,proto3" json:"collect_num,omitempty"`       // 收藏数量
	ReportNum     int64                  `protobuf:"varint,11,opt,name=report_num,json=reportNum,proto3" json:"report_num,omitempty"`          // 举报数量
	RecommendNum  int64                  `protobuf:"varint,12,opt,name=recommend_num,json=recommendNum,proto3" json:"recommend_num,omitempty"` // 推荐数量
	FollowNum     int64                  `protobuf:"varint,13,opt,name=follow_num,json=followNum,proto3" json:"follow_num,omitempty"`          // 关注人数
	AllowedNum    int64                  `protobuf:"varint,14,opt,name=allowed_num,json=allowedNum,proto3" json:"allowed_num,omitempty"`       // 被关注人数
	View          int64                  `protobuf:"varint,15,opt,name=view,proto3" json:"view,omitempty"`                                     // 浏览量
	Status        int32                  `protobuf:"varint,16,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemberStat) Reset() {
	*x = MemberStat{}
	mi := &file_pb_member_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemberStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberStat) ProtoMessage() {}

func (x *MemberStat) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberStat.ProtoReflect.Descriptor instead.
func (*MemberStat) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{5}
}

func (x *MemberStat) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MemberStat) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *MemberStat) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *MemberStat) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *MemberStat) GetMemberType() int32 {
	if x != nil {
		return x.MemberType
	}
	return 0
}

func (x *MemberStat) GetNiceNum() int64 {
	if x != nil {
		return x.NiceNum
	}
	return 0
}

func (x *MemberStat) GetDisagreeNum() int64 {
	if x != nil {
		return x.DisagreeNum
	}
	return 0
}

func (x *MemberStat) GetTransmitNum() int64 {
	if x != nil {
		return x.TransmitNum
	}
	return 0
}

func (x *MemberStat) GetCommentNum() int64 {
	if x != nil {
		return x.CommentNum
	}
	return 0
}

func (x *MemberStat) GetCollectNum() int64 {
	if x != nil {
		return x.CollectNum
	}
	return 0
}

func (x *MemberStat) GetReportNum() int64 {
	if x != nil {
		return x.ReportNum
	}
	return 0
}

func (x *MemberStat) GetRecommendNum() int64 {
	if x != nil {
		return x.RecommendNum
	}
	return 0
}

func (x *MemberStat) GetFollowNum() int64 {
	if x != nil {
		return x.FollowNum
	}
	return 0
}

func (x *MemberStat) GetAllowedNum() int64 {
	if x != nil {
		return x.AllowedNum
	}
	return 0
}

func (x *MemberStat) GetView() int64 {
	if x != nil {
		return x.View
	}
	return 0
}

func (x *MemberStat) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MemberStat) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MemberStat) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// ===== 注销申请信息（基于member_cancel表） =====
type MemberCancel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MerchantId    int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	MemberId      int64                  `protobuf:"varint,4,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Content       string                 `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`                               // 申请内容
	AuditStatus   int32                  `protobuf:"varint,6,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`   // 审核状态 0:申请 1:通过 -1:失败
	AuditTime     int64                  `protobuf:"varint,7,opt,name=audit_time,json=auditTime,proto3" json:"audit_time,omitempty"`         // 审核时间
	RefusalCause  string                 `protobuf:"bytes,8,opt,name=refusal_cause,json=refusalCause,proto3" json:"refusal_cause,omitempty"` // 拒绝原因
	IsAddon       int32                  `protobuf:"varint,9,opt,name=is_addon,json=isAddon,proto3" json:"is_addon,omitempty"`
	AddonName     string                 `protobuf:"bytes,10,opt,name=addon_name,json=addonName,proto3" json:"addon_name,omitempty"`
	Status        int32                  `protobuf:"varint,11,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemberCancel) Reset() {
	*x = MemberCancel{}
	mi := &file_pb_member_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemberCancel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberCancel) ProtoMessage() {}

func (x *MemberCancel) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberCancel.ProtoReflect.Descriptor instead.
func (*MemberCancel) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{6}
}

func (x *MemberCancel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MemberCancel) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *MemberCancel) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *MemberCancel) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *MemberCancel) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MemberCancel) GetAuditStatus() int32 {
	if x != nil {
		return x.AuditStatus
	}
	return 0
}

func (x *MemberCancel) GetAuditTime() int64 {
	if x != nil {
		return x.AuditTime
	}
	return 0
}

func (x *MemberCancel) GetRefusalCause() string {
	if x != nil {
		return x.RefusalCause
	}
	return ""
}

func (x *MemberCancel) GetIsAddon() int32 {
	if x != nil {
		return x.IsAddon
	}
	return 0
}

func (x *MemberCancel) GetAddonName() string {
	if x != nil {
		return x.AddonName
	}
	return ""
}

func (x *MemberCancel) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MemberCancel) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MemberCancel) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// ===== 用户注册 =====
type RegisterReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Mobile        string                 `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Nickname      string                 `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Source        string                 `protobuf:"bytes,6,opt,name=source,proto3" json:"source,omitempty"`
	MerchantId    int64                  `protobuf:"varint,7,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,8,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterReq) Reset() {
	*x = RegisterReq{}
	mi := &file_pb_member_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterReq) ProtoMessage() {}

func (x *RegisterReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterReq.ProtoReflect.Descriptor instead.
func (*RegisterReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{7}
}

func (x *RegisterReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *RegisterReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RegisterReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *RegisterReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *RegisterReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *RegisterReq) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *RegisterReq) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *RegisterReq) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

type RegisterResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	MemberId      int64                  `protobuf:"varint,3,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	AccessToken   string                 `protobuf:"bytes,4,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,5,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterResp) Reset() {
	*x = RegisterResp{}
	mi := &file_pb_member_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterResp) ProtoMessage() {}

func (x *RegisterResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterResp.ProtoReflect.Descriptor instead.
func (*RegisterResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{8}
}

func (x *RegisterResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RegisterResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RegisterResp) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *RegisterResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *RegisterResp) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// ===== 用户登录 =====
type LoginReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Mobile        string                 `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"`
	LoginType     int32                  `protobuf:"varint,4,opt,name=login_type,json=loginType,proto3" json:"login_type,omitempty"` // 1:用户名 2:手机号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginReq) Reset() {
	*x = LoginReq{}
	mi := &file_pb_member_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReq) ProtoMessage() {}

func (x *LoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReq.ProtoReflect.Descriptor instead.
func (*LoginReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{9}
}

func (x *LoginReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *LoginReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *LoginReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *LoginReq) GetLoginType() int32 {
	if x != nil {
		return x.LoginType
	}
	return 0
}

type LoginResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	MemberInfo    *Member                `protobuf:"bytes,3,opt,name=member_info,json=memberInfo,proto3" json:"member_info,omitempty"`
	AccessToken   string                 `protobuf:"bytes,4,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,5,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginResp) Reset() {
	*x = LoginResp{}
	mi := &file_pb_member_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResp) ProtoMessage() {}

func (x *LoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResp.ProtoReflect.Descriptor instead.
func (*LoginResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{10}
}

func (x *LoginResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LoginResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LoginResp) GetMemberInfo() *Member {
	if x != nil {
		return x.MemberInfo
	}
	return nil
}

func (x *LoginResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *LoginResp) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// ===== 用户登出 =====
type LogoutReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutReq) Reset() {
	*x = LogoutReq{}
	mi := &file_pb_member_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutReq) ProtoMessage() {}

func (x *LogoutReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutReq.ProtoReflect.Descriptor instead.
func (*LogoutReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{11}
}

func (x *LogoutReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *LogoutReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type LogoutResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutResp) Reset() {
	*x = LogoutResp{}
	mi := &file_pb_member_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutResp) ProtoMessage() {}

func (x *LogoutResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutResp.ProtoReflect.Descriptor instead.
func (*LogoutResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{12}
}

func (x *LogoutResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LogoutResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// ===== 刷新Token =====
type RefreshTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefreshToken  string                 `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenReq) Reset() {
	*x = RefreshTokenReq{}
	mi := &file_pb_member_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenReq) ProtoMessage() {}

func (x *RefreshTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenReq.ProtoReflect.Descriptor instead.
func (*RefreshTokenReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{13}
}

func (x *RefreshTokenReq) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

type RefreshTokenResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	AccessToken   string                 `protobuf:"bytes,3,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,4,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenResp) Reset() {
	*x = RefreshTokenResp{}
	mi := &file_pb_member_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResp) ProtoMessage() {}

func (x *RefreshTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResp.ProtoReflect.Descriptor instead.
func (*RefreshTokenResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{14}
}

func (x *RefreshTokenResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RefreshTokenResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RefreshTokenResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *RefreshTokenResp) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// ===== 重置密码 =====
type ResetPasswordReq struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Mobile           string                 `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	NewPassword      string                 `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	VerificationCode string                 `protobuf:"bytes,3,opt,name=verification_code,json=verificationCode,proto3" json:"verification_code,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ResetPasswordReq) Reset() {
	*x = ResetPasswordReq{}
	mi := &file_pb_member_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetPasswordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetPasswordReq) ProtoMessage() {}

func (x *ResetPasswordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetPasswordReq.ProtoReflect.Descriptor instead.
func (*ResetPasswordReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{15}
}

func (x *ResetPasswordReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *ResetPasswordReq) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

func (x *ResetPasswordReq) GetVerificationCode() string {
	if x != nil {
		return x.VerificationCode
	}
	return ""
}

// ===== Token验证 =====
type ValidateTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateTokenReq) Reset() {
	*x = ValidateTokenReq{}
	mi := &file_pb_member_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateTokenReq) ProtoMessage() {}

func (x *ValidateTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateTokenReq.ProtoReflect.Descriptor instead.
func (*ValidateTokenReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{16}
}

func (x *ValidateTokenReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type ValidateTokenResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	MemberId      int64                  `protobuf:"varint,3,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	MemberInfo    *Member                `protobuf:"bytes,4,opt,name=member_info,json=memberInfo,proto3" json:"member_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateTokenResp) Reset() {
	*x = ValidateTokenResp{}
	mi := &file_pb_member_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateTokenResp) ProtoMessage() {}

func (x *ValidateTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateTokenResp.ProtoReflect.Descriptor instead.
func (*ValidateTokenResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{17}
}

func (x *ValidateTokenResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ValidateTokenResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ValidateTokenResp) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *ValidateTokenResp) GetMemberInfo() *Member {
	if x != nil {
		return x.MemberInfo
	}
	return nil
}

// ===== 获取用户信息 =====
type GetMemberInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemberInfoReq) Reset() {
	*x = GetMemberInfoReq{}
	mi := &file_pb_member_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemberInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemberInfoReq) ProtoMessage() {}

func (x *GetMemberInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemberInfoReq.ProtoReflect.Descriptor instead.
func (*GetMemberInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{18}
}

func (x *GetMemberInfoReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type GetMemberInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *Member                `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemberInfoResp) Reset() {
	*x = GetMemberInfoResp{}
	mi := &file_pb_member_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemberInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemberInfoResp) ProtoMessage() {}

func (x *GetMemberInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemberInfoResp.ProtoReflect.Descriptor instead.
func (*GetMemberInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{19}
}

func (x *GetMemberInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMemberInfoResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetMemberInfoResp) GetData() *Member {
	if x != nil {
		return x.Data
	}
	return nil
}

// ===== 更新用户信息 =====
type UpdateMemberInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Realname      string                 `protobuf:"bytes,2,opt,name=realname,proto3" json:"realname,omitempty"`
	Nickname      string                 `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	HeadPortrait  string                 `protobuf:"bytes,4,opt,name=head_portrait,json=headPortrait,proto3" json:"head_portrait,omitempty"`
	Gender        int32                  `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	Qq            string                 `protobuf:"bytes,6,opt,name=qq,proto3" json:"qq,omitempty"`
	Email         string                 `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	Birthday      string                 `protobuf:"bytes,8,opt,name=birthday,proto3" json:"birthday,omitempty"`
	ProvinceId    int64                  `protobuf:"varint,9,opt,name=province_id,json=provinceId,proto3" json:"province_id,omitempty"`
	CityId        int64                  `protobuf:"varint,10,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	AreaId        int64                  `protobuf:"varint,11,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	Address       string                 `protobuf:"bytes,12,opt,name=address,proto3" json:"address,omitempty"`
	TelNo         string                 `protobuf:"bytes,13,opt,name=tel_no,json=telNo,proto3" json:"tel_no,omitempty"`
	BgImage       string                 `protobuf:"bytes,14,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	Description   string                 `protobuf:"bytes,15,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMemberInfoReq) Reset() {
	*x = UpdateMemberInfoReq{}
	mi := &file_pb_member_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMemberInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMemberInfoReq) ProtoMessage() {}

func (x *UpdateMemberInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMemberInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateMemberInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateMemberInfoReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *UpdateMemberInfoReq) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetHeadPortrait() string {
	if x != nil {
		return x.HeadPortrait
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UpdateMemberInfoReq) GetQq() string {
	if x != nil {
		return x.Qq
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetProvinceId() int64 {
	if x != nil {
		return x.ProvinceId
	}
	return 0
}

func (x *UpdateMemberInfoReq) GetCityId() int64 {
	if x != nil {
		return x.CityId
	}
	return 0
}

func (x *UpdateMemberInfoReq) GetAreaId() int64 {
	if x != nil {
		return x.AreaId
	}
	return 0
}

func (x *UpdateMemberInfoReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetTelNo() string {
	if x != nil {
		return x.TelNo
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *UpdateMemberInfoReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// ===== 获取用户列表 =====
type GetMemberListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Keyword       string                 `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	Type          int32                  `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemberListReq) Reset() {
	*x = GetMemberListReq{}
	mi := &file_pb_member_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemberListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemberListReq) ProtoMessage() {}

func (x *GetMemberListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemberListReq.ProtoReflect.Descriptor instead.
func (*GetMemberListReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{21}
}

func (x *GetMemberListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetMemberListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetMemberListReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *GetMemberListReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetMemberListReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type GetMemberListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	List          []*Member              `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	PageInfo      *PageInfo              `protobuf:"bytes,4,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemberListResp) Reset() {
	*x = GetMemberListResp{}
	mi := &file_pb_member_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemberListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemberListResp) ProtoMessage() {}

func (x *GetMemberListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemberListResp.ProtoReflect.Descriptor instead.
func (*GetMemberListResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{22}
}

func (x *GetMemberListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMemberListResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetMemberListResp) GetList() []*Member {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetMemberListResp) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

// ===== 删除用户 =====
type DeleteMemberReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMemberReq) Reset() {
	*x = DeleteMemberReq{}
	mi := &file_pb_member_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMemberReq) ProtoMessage() {}

func (x *DeleteMemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMemberReq.ProtoReflect.Descriptor instead.
func (*DeleteMemberReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteMemberReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

// ===== 创建用户 =====
type CreateMemberReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    int64                  `protobuf:"varint,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	Mobile        string                 `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Email         string                 `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	Nickname      string                 `protobuf:"bytes,7,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Type          int32                  `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	Source        string                 `protobuf:"bytes,9,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMemberReq) Reset() {
	*x = CreateMemberReq{}
	mi := &file_pb_member_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMemberReq) ProtoMessage() {}

func (x *CreateMemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMemberReq.ProtoReflect.Descriptor instead.
func (*CreateMemberReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{24}
}

func (x *CreateMemberReq) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CreateMemberReq) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *CreateMemberReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateMemberReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateMemberReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *CreateMemberReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateMemberReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *CreateMemberReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CreateMemberReq) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type CreateMemberResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	MemberId      int64                  `protobuf:"varint,3,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMemberResp) Reset() {
	*x = CreateMemberResp{}
	mi := &file_pb_member_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMemberResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMemberResp) ProtoMessage() {}

func (x *CreateMemberResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMemberResp.ProtoReflect.Descriptor instead.
func (*CreateMemberResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{25}
}

func (x *CreateMemberResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateMemberResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateMemberResp) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

// ===== 实名认证相关 =====
type GetCertificationInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCertificationInfoReq) Reset() {
	*x = GetCertificationInfoReq{}
	mi := &file_pb_member_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCertificationInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCertificationInfoReq) ProtoMessage() {}

func (x *GetCertificationInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCertificationInfoReq.ProtoReflect.Descriptor instead.
func (*GetCertificationInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{26}
}

func (x *GetCertificationInfoReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type GetCertificationInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *MemberCertification   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCertificationInfoResp) Reset() {
	*x = GetCertificationInfoResp{}
	mi := &file_pb_member_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCertificationInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCertificationInfoResp) ProtoMessage() {}

func (x *GetCertificationInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCertificationInfoResp.ProtoReflect.Descriptor instead.
func (*GetCertificationInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{27}
}

func (x *GetCertificationInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCertificationInfoResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetCertificationInfoResp) GetData() *MemberCertification {
	if x != nil {
		return x.Data
	}
	return nil
}

type SubmitCertificationReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	MemberId          int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Realname          string                 `protobuf:"bytes,2,opt,name=realname,proto3" json:"realname,omitempty"`
	IdentityCard      string                 `protobuf:"bytes,3,opt,name=identity_card,json=identityCard,proto3" json:"identity_card,omitempty"`
	IdentityCardFront string                 `protobuf:"bytes,4,opt,name=identity_card_front,json=identityCardFront,proto3" json:"identity_card_front,omitempty"`
	IdentityCardBack  string                 `protobuf:"bytes,5,opt,name=identity_card_back,json=identityCardBack,proto3" json:"identity_card_back,omitempty"`
	Address           string                 `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SubmitCertificationReq) Reset() {
	*x = SubmitCertificationReq{}
	mi := &file_pb_member_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitCertificationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitCertificationReq) ProtoMessage() {}

func (x *SubmitCertificationReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitCertificationReq.ProtoReflect.Descriptor instead.
func (*SubmitCertificationReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{28}
}

func (x *SubmitCertificationReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *SubmitCertificationReq) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

func (x *SubmitCertificationReq) GetIdentityCard() string {
	if x != nil {
		return x.IdentityCard
	}
	return ""
}

func (x *SubmitCertificationReq) GetIdentityCardFront() string {
	if x != nil {
		return x.IdentityCardFront
	}
	return ""
}

func (x *SubmitCertificationReq) GetIdentityCardBack() string {
	if x != nil {
		return x.IdentityCardBack
	}
	return ""
}

func (x *SubmitCertificationReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type AuditCertificationReq struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	CertificationId int64                  `protobuf:"varint,1,opt,name=certification_id,json=certificationId,proto3" json:"certification_id,omitempty"`
	AuditResult     int32                  `protobuf:"varint,2,opt,name=audit_result,json=auditResult,proto3" json:"audit_result,omitempty"` // 1:通过 -1:拒绝
	RefusalReason   string                 `protobuf:"bytes,3,opt,name=refusal_reason,json=refusalReason,proto3" json:"refusal_reason,omitempty"`
	AuditorId       int64                  `protobuf:"varint,4,opt,name=auditor_id,json=auditorId,proto3" json:"auditor_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AuditCertificationReq) Reset() {
	*x = AuditCertificationReq{}
	mi := &file_pb_member_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditCertificationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditCertificationReq) ProtoMessage() {}

func (x *AuditCertificationReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditCertificationReq.ProtoReflect.Descriptor instead.
func (*AuditCertificationReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{29}
}

func (x *AuditCertificationReq) GetCertificationId() int64 {
	if x != nil {
		return x.CertificationId
	}
	return 0
}

func (x *AuditCertificationReq) GetAuditResult() int32 {
	if x != nil {
		return x.AuditResult
	}
	return 0
}

func (x *AuditCertificationReq) GetRefusalReason() string {
	if x != nil {
		return x.RefusalReason
	}
	return ""
}

func (x *AuditCertificationReq) GetAuditorId() int64 {
	if x != nil {
		return x.AuditorId
	}
	return 0
}

type GetPendingCertificationListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPendingCertificationListReq) Reset() {
	*x = GetPendingCertificationListReq{}
	mi := &file_pb_member_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPendingCertificationListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingCertificationListReq) ProtoMessage() {}

func (x *GetPendingCertificationListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingCertificationListReq.ProtoReflect.Descriptor instead.
func (*GetPendingCertificationListReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{30}
}

func (x *GetPendingCertificationListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetPendingCertificationListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetPendingCertificationListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	List          []*MemberCertification `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	PageInfo      *PageInfo              `protobuf:"bytes,4,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPendingCertificationListResp) Reset() {
	*x = GetPendingCertificationListResp{}
	mi := &file_pb_member_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPendingCertificationListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingCertificationListResp) ProtoMessage() {}

func (x *GetPendingCertificationListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingCertificationListResp.ProtoReflect.Descriptor instead.
func (*GetPendingCertificationListResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{31}
}

func (x *GetPendingCertificationListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPendingCertificationListResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPendingCertificationListResp) GetList() []*MemberCertification {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetPendingCertificationListResp) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

// ===== 用户统计相关 =====
type GetMemberStatReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemberStatReq) Reset() {
	*x = GetMemberStatReq{}
	mi := &file_pb_member_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemberStatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemberStatReq) ProtoMessage() {}

func (x *GetMemberStatReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemberStatReq.ProtoReflect.Descriptor instead.
func (*GetMemberStatReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{32}
}

func (x *GetMemberStatReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type GetMemberStatResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *MemberStat            `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMemberStatResp) Reset() {
	*x = GetMemberStatResp{}
	mi := &file_pb_member_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMemberStatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMemberStatResp) ProtoMessage() {}

func (x *GetMemberStatResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMemberStatResp.ProtoReflect.Descriptor instead.
func (*GetMemberStatResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{33}
}

func (x *GetMemberStatResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMemberStatResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetMemberStatResp) GetData() *MemberStat {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateMemberStatReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	NiceNum       int64                  `protobuf:"varint,2,opt,name=nice_num,json=niceNum,proto3" json:"nice_num,omitempty"`
	CollectNum    int64                  `protobuf:"varint,3,opt,name=collect_num,json=collectNum,proto3" json:"collect_num,omitempty"`
	FollowNum     int64                  `protobuf:"varint,4,opt,name=follow_num,json=followNum,proto3" json:"follow_num,omitempty"`
	View          int64                  `protobuf:"varint,5,opt,name=view,proto3" json:"view,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMemberStatReq) Reset() {
	*x = UpdateMemberStatReq{}
	mi := &file_pb_member_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMemberStatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMemberStatReq) ProtoMessage() {}

func (x *UpdateMemberStatReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMemberStatReq.ProtoReflect.Descriptor instead.
func (*UpdateMemberStatReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{34}
}

func (x *UpdateMemberStatReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *UpdateMemberStatReq) GetNiceNum() int64 {
	if x != nil {
		return x.NiceNum
	}
	return 0
}

func (x *UpdateMemberStatReq) GetCollectNum() int64 {
	if x != nil {
		return x.CollectNum
	}
	return 0
}

func (x *UpdateMemberStatReq) GetFollowNum() int64 {
	if x != nil {
		return x.FollowNum
	}
	return 0
}

func (x *UpdateMemberStatReq) GetView() int64 {
	if x != nil {
		return x.View
	}
	return 0
}

// ===== 注销申请相关 =====
type SubmitCancelApplicationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubmitCancelApplicationReq) Reset() {
	*x = SubmitCancelApplicationReq{}
	mi := &file_pb_member_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubmitCancelApplicationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitCancelApplicationReq) ProtoMessage() {}

func (x *SubmitCancelApplicationReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitCancelApplicationReq.ProtoReflect.Descriptor instead.
func (*SubmitCancelApplicationReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{35}
}

func (x *SubmitCancelApplicationReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *SubmitCancelApplicationReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type GetCancelStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCancelStatusReq) Reset() {
	*x = GetCancelStatusReq{}
	mi := &file_pb_member_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCancelStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCancelStatusReq) ProtoMessage() {}

func (x *GetCancelStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCancelStatusReq.ProtoReflect.Descriptor instead.
func (*GetCancelStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{36}
}

func (x *GetCancelStatusReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type GetCancelStatusResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *MemberCancel          `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCancelStatusResp) Reset() {
	*x = GetCancelStatusResp{}
	mi := &file_pb_member_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCancelStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCancelStatusResp) ProtoMessage() {}

func (x *GetCancelStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCancelStatusResp.ProtoReflect.Descriptor instead.
func (*GetCancelStatusResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{37}
}

func (x *GetCancelStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCancelStatusResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetCancelStatusResp) GetData() *MemberCancel {
	if x != nil {
		return x.Data
	}
	return nil
}

type AuditCancelApplicationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CancelId      int64                  `protobuf:"varint,1,opt,name=cancel_id,json=cancelId,proto3" json:"cancel_id,omitempty"`
	AuditResult   int32                  `protobuf:"varint,2,opt,name=audit_result,json=auditResult,proto3" json:"audit_result,omitempty"` // 1:通过 -1:拒绝
	RefusalReason string                 `protobuf:"bytes,3,opt,name=refusal_reason,json=refusalReason,proto3" json:"refusal_reason,omitempty"`
	AuditorId     int64                  `protobuf:"varint,4,opt,name=auditor_id,json=auditorId,proto3" json:"auditor_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuditCancelApplicationReq) Reset() {
	*x = AuditCancelApplicationReq{}
	mi := &file_pb_member_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditCancelApplicationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditCancelApplicationReq) ProtoMessage() {}

func (x *AuditCancelApplicationReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditCancelApplicationReq.ProtoReflect.Descriptor instead.
func (*AuditCancelApplicationReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{38}
}

func (x *AuditCancelApplicationReq) GetCancelId() int64 {
	if x != nil {
		return x.CancelId
	}
	return 0
}

func (x *AuditCancelApplicationReq) GetAuditResult() int32 {
	if x != nil {
		return x.AuditResult
	}
	return 0
}

func (x *AuditCancelApplicationReq) GetRefusalReason() string {
	if x != nil {
		return x.RefusalReason
	}
	return ""
}

func (x *AuditCancelApplicationReq) GetAuditorId() int64 {
	if x != nil {
		return x.AuditorId
	}
	return 0
}

type GetPendingCancelListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPendingCancelListReq) Reset() {
	*x = GetPendingCancelListReq{}
	mi := &file_pb_member_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPendingCancelListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingCancelListReq) ProtoMessage() {}

func (x *GetPendingCancelListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingCancelListReq.ProtoReflect.Descriptor instead.
func (*GetPendingCancelListReq) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{39}
}

func (x *GetPendingCancelListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetPendingCancelListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetPendingCancelListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	List          []*MemberCancel        `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	PageInfo      *PageInfo              `protobuf:"bytes,4,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPendingCancelListResp) Reset() {
	*x = GetPendingCancelListResp{}
	mi := &file_pb_member_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPendingCancelListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingCancelListResp) ProtoMessage() {}

func (x *GetPendingCancelListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_member_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingCancelListResp.ProtoReflect.Descriptor instead.
func (*GetPendingCancelListResp) Descriptor() ([]byte, []int) {
	return file_pb_member_proto_rawDescGZIP(), []int{40}
}

func (x *GetPendingCancelListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPendingCancelListResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPendingCancelListResp) GetList() []*MemberCancel {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetPendingCancelListResp) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

var File_pb_member_proto protoreflect.FileDescriptor

const file_pb_member_proto_rawDesc = "" +
	"\n" +
	"\x0fpb/member.proto\x12\x06member\":\n" +
	"\n" +
	"CommonResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"Q\n" +
	"\bPageInfo\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x03R\x05total\"\xb9\t\n" +
	"\x06Member\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x1a\n" +
	"\busername\x18\x04 \x01(\tR\busername\x12#\n" +
	"\rpassword_hash\x18\x05 \x01(\tR\fpasswordHash\x12\x19\n" +
	"\bauth_key\x18\x06 \x01(\tR\aauthKey\x120\n" +
	"\x14password_reset_token\x18\a \x01(\tR\x12passwordResetToken\x12,\n" +
	"\x12mobile_reset_token\x18\b \x01(\tR\x10mobileResetToken\x12\x12\n" +
	"\x04type\x18\t \x01(\x05R\x04type\x12\x1a\n" +
	"\brealname\x18\n" +
	" \x01(\tR\brealname\x12\x1a\n" +
	"\bnickname\x18\v \x01(\tR\bnickname\x12#\n" +
	"\rhead_portrait\x18\f \x01(\tR\fheadPortrait\x12\x16\n" +
	"\x06gender\x18\r \x01(\x05R\x06gender\x12\x0e\n" +
	"\x02qq\x18\x0e \x01(\tR\x02qq\x12\x14\n" +
	"\x05email\x18\x0f \x01(\tR\x05email\x12\x1a\n" +
	"\bbirthday\x18\x10 \x01(\tR\bbirthday\x12\x1f\n" +
	"\vprovince_id\x18\x11 \x01(\x03R\n" +
	"provinceId\x12\x17\n" +
	"\acity_id\x18\x12 \x01(\x03R\x06cityId\x12\x17\n" +
	"\aarea_id\x18\x13 \x01(\x03R\x06areaId\x12\x18\n" +
	"\aaddress\x18\x14 \x01(\tR\aaddress\x12\x16\n" +
	"\x06mobile\x18\x15 \x01(\tR\x06mobile\x12\x15\n" +
	"\x06tel_no\x18\x16 \x01(\tR\x05telNo\x12\x19\n" +
	"\bbg_image\x18\x17 \x01(\tR\abgImage\x12 \n" +
	"\vdescription\x18\x18 \x01(\tR\vdescription\x12\x1f\n" +
	"\vvisit_count\x18\x19 \x01(\x05R\n" +
	"visitCount\x12\x1b\n" +
	"\tlast_time\x18\x1a \x01(\x03R\blastTime\x12\x17\n" +
	"\alast_ip\x18\x1b \x01(\tR\x06lastIp\x12\x12\n" +
	"\x04role\x18\x1c \x01(\x05R\x04role\x12#\n" +
	"\rcurrent_level\x18\x1d \x01(\x05R\fcurrentLevel\x122\n" +
	"\x15level_expiration_time\x18\x1e \x01(\x03R\x13levelExpirationTime\x12$\n" +
	"\x0elevel_buy_type\x18\x1f \x01(\x05R\flevelBuyType\x12\x10\n" +
	"\x03pid\x18  \x01(\x03R\x03pid\x12\x14\n" +
	"\x05level\x18! \x01(\x05R\x05level\x12\x12\n" +
	"\x04tree\x18\" \x01(\tR\x04tree\x12#\n" +
	"\rpromoter_code\x18# \x01(\tR\fpromoterCode\x12-\n" +
	"\x12certification_type\x18$ \x01(\x05R\x11certificationType\x12\x16\n" +
	"\x06source\x18% \x01(\tR\x06source\x12\x16\n" +
	"\x06status\x18& \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18' \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18( \x01(\x03R\tupdatedAt\x12\x1b\n" +
	"\tregion_id\x18) \x01(\x03R\bregionId\"\x9f\b\n" +
	"\fMemberPublic\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x1a\n" +
	"\busername\x18\x04 \x01(\tR\busername\x12\x12\n" +
	"\x04type\x18\x05 \x01(\x05R\x04type\x12\x1a\n" +
	"\brealname\x18\x06 \x01(\tR\brealname\x12\x1a\n" +
	"\bnickname\x18\a \x01(\tR\bnickname\x12#\n" +
	"\rhead_portrait\x18\b \x01(\tR\fheadPortrait\x12\x16\n" +
	"\x06gender\x18\t \x01(\x05R\x06gender\x12\x0e\n" +
	"\x02qq\x18\n" +
	" \x01(\tR\x02qq\x12\x14\n" +
	"\x05email\x18\v \x01(\tR\x05email\x12\x1a\n" +
	"\bbirthday\x18\f \x01(\tR\bbirthday\x12\x1f\n" +
	"\vprovince_id\x18\r \x01(\x03R\n" +
	"provinceId\x12\x17\n" +
	"\acity_id\x18\x0e \x01(\x03R\x06cityId\x12\x17\n" +
	"\aarea_id\x18\x0f \x01(\x03R\x06areaId\x12\x18\n" +
	"\aaddress\x18\x10 \x01(\tR\aaddress\x12\x16\n" +
	"\x06mobile\x18\x11 \x01(\tR\x06mobile\x12\x15\n" +
	"\x06tel_no\x18\x12 \x01(\tR\x05telNo\x12\x19\n" +
	"\bbg_image\x18\x13 \x01(\tR\abgImage\x12 \n" +
	"\vdescription\x18\x14 \x01(\tR\vdescription\x12\x1f\n" +
	"\vvisit_count\x18\x15 \x01(\x05R\n" +
	"visitCount\x12\x1b\n" +
	"\tlast_time\x18\x16 \x01(\x03R\blastTime\x12\x17\n" +
	"\alast_ip\x18\x17 \x01(\tR\x06lastIp\x12\x12\n" +
	"\x04role\x18\x18 \x01(\x05R\x04role\x12#\n" +
	"\rcurrent_level\x18\x19 \x01(\x05R\fcurrentLevel\x122\n" +
	"\x15level_expiration_time\x18\x1a \x01(\x03R\x13levelExpirationTime\x12$\n" +
	"\x0elevel_buy_type\x18\x1b \x01(\x05R\flevelBuyType\x12\x10\n" +
	"\x03pid\x18\x1c \x01(\x03R\x03pid\x12\x14\n" +
	"\x05level\x18\x1d \x01(\x05R\x05level\x12\x12\n" +
	"\x04tree\x18\x1e \x01(\tR\x04tree\x12#\n" +
	"\rpromoter_code\x18\x1f \x01(\tR\fpromoterCode\x12-\n" +
	"\x12certification_type\x18  \x01(\x05R\x11certificationType\x12\x16\n" +
	"\x06source\x18! \x01(\tR\x06source\x12\x16\n" +
	"\x06status\x18\" \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18# \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18$ \x01(\x03R\tupdatedAt\x12\x1b\n" +
	"\tregion_id\x18% \x01(\x03R\bregionId\"\xb3\x05\n" +
	"\x13MemberCertification\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x1b\n" +
	"\tmember_id\x18\x04 \x01(\x03R\bmemberId\x12\x1f\n" +
	"\vmember_type\x18\x05 \x01(\x05R\n" +
	"memberType\x12\x1a\n" +
	"\brealname\x18\x06 \x01(\tR\brealname\x12#\n" +
	"\ridentity_card\x18\a \x01(\tR\fidentityCard\x12.\n" +
	"\x13identity_card_front\x18\b \x01(\tR\x11identityCardFront\x12,\n" +
	"\x12identity_card_back\x18\t \x01(\tR\x10identityCardBack\x12\x16\n" +
	"\x06gender\x18\n" +
	" \x01(\tR\x06gender\x12\x1a\n" +
	"\bbirthday\x18\v \x01(\tR\bbirthday\x12\"\n" +
	"\rfront_is_fake\x18\f \x01(\x05R\vfrontIsFake\x12 \n" +
	"\fback_is_fake\x18\r \x01(\x05R\n" +
	"backIsFake\x12 \n" +
	"\vnationality\x18\x0e \x01(\tR\vnationality\x12\x18\n" +
	"\aaddress\x18\x0f \x01(\tR\aaddress\x12\x1d\n" +
	"\n" +
	"start_date\x18\x10 \x01(\tR\tstartDate\x12\x19\n" +
	"\bend_date\x18\x11 \x01(\tR\aendDate\x12\x14\n" +
	"\x05issue\x18\x12 \x01(\tR\x05issue\x12\x17\n" +
	"\ais_self\x18\x13 \x01(\x05R\x06isSelf\x12\x16\n" +
	"\x06status\x18\x14 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\x15 \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x16 \x01(\x03R\tupdatedAt\"\xa7\x04\n" +
	"\n" +
	"MemberStat\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x1b\n" +
	"\tmember_id\x18\x04 \x01(\x03R\bmemberId\x12\x1f\n" +
	"\vmember_type\x18\x05 \x01(\x05R\n" +
	"memberType\x12\x19\n" +
	"\bnice_num\x18\x06 \x01(\x03R\aniceNum\x12!\n" +
	"\fdisagree_num\x18\a \x01(\x03R\vdisagreeNum\x12!\n" +
	"\ftransmit_num\x18\b \x01(\x03R\vtransmitNum\x12\x1f\n" +
	"\vcomment_num\x18\t \x01(\x03R\n" +
	"commentNum\x12\x1f\n" +
	"\vcollect_num\x18\n" +
	" \x01(\x03R\n" +
	"collectNum\x12\x1d\n" +
	"\n" +
	"report_num\x18\v \x01(\x03R\treportNum\x12#\n" +
	"\rrecommend_num\x18\f \x01(\x03R\frecommendNum\x12\x1d\n" +
	"\n" +
	"follow_num\x18\r \x01(\x03R\tfollowNum\x12\x1f\n" +
	"\vallowed_num\x18\x0e \x01(\x03R\n" +
	"allowedNum\x12\x12\n" +
	"\x04view\x18\x0f \x01(\x03R\x04view\x12\x16\n" +
	"\x06status\x18\x10 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\x11 \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x12 \x01(\x03R\tupdatedAt\"\x88\x03\n" +
	"\fMemberCancel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x1b\n" +
	"\tmember_id\x18\x04 \x01(\x03R\bmemberId\x12\x18\n" +
	"\acontent\x18\x05 \x01(\tR\acontent\x12!\n" +
	"\faudit_status\x18\x06 \x01(\x05R\vauditStatus\x12\x1d\n" +
	"\n" +
	"audit_time\x18\a \x01(\x03R\tauditTime\x12#\n" +
	"\rrefusal_cause\x18\b \x01(\tR\frefusalCause\x12\x19\n" +
	"\bis_addon\x18\t \x01(\x05R\aisAddon\x12\x1d\n" +
	"\n" +
	"addon_name\x18\n" +
	" \x01(\tR\taddonName\x12\x16\n" +
	"\x06status\x18\v \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\f \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\r \x01(\x03R\tupdatedAt\"\xe3\x01\n" +
	"\vRegisterReq\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x16\n" +
	"\x06mobile\x18\x03 \x01(\tR\x06mobile\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x1a\n" +
	"\bnickname\x18\x05 \x01(\tR\bnickname\x12\x16\n" +
	"\x06source\x18\x06 \x01(\tR\x06source\x12\x1f\n" +
	"\vmerchant_id\x18\a \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\b \x01(\x03R\astoreId\"\xa1\x01\n" +
	"\fRegisterResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1b\n" +
	"\tmember_id\x18\x03 \x01(\x03R\bmemberId\x12!\n" +
	"\faccess_token\x18\x04 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x05 \x01(\tR\frefreshToken\"y\n" +
	"\bLoginReq\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x16\n" +
	"\x06mobile\x18\x03 \x01(\tR\x06mobile\x12\x1d\n" +
	"\n" +
	"login_type\x18\x04 \x01(\x05R\tloginType\"\xb2\x01\n" +
	"\tLoginResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\vmember_info\x18\x03 \x01(\v2\x0e.member.MemberR\n" +
	"memberInfo\x12!\n" +
	"\faccess_token\x18\x04 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x05 \x01(\tR\frefreshToken\">\n" +
	"\tLogoutReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\":\n" +
	"\n" +
	"LogoutResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"6\n" +
	"\x0fRefreshTokenReq\x12#\n" +
	"\rrefresh_token\x18\x01 \x01(\tR\frefreshToken\"\x88\x01\n" +
	"\x10RefreshTokenResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12!\n" +
	"\faccess_token\x18\x03 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x04 \x01(\tR\frefreshToken\"z\n" +
	"\x10ResetPasswordReq\x12\x16\n" +
	"\x06mobile\x18\x01 \x01(\tR\x06mobile\x12!\n" +
	"\fnew_password\x18\x02 \x01(\tR\vnewPassword\x12+\n" +
	"\x11verification_code\x18\x03 \x01(\tR\x10verificationCode\"(\n" +
	"\x10ValidateTokenReq\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\x8f\x01\n" +
	"\x11ValidateTokenResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1b\n" +
	"\tmember_id\x18\x03 \x01(\x03R\bmemberId\x12/\n" +
	"\vmember_info\x18\x04 \x01(\v2\x0e.member.MemberR\n" +
	"memberInfo\"/\n" +
	"\x10GetMemberInfoReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"e\n" +
	"\x11GetMemberInfoResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04data\x18\x03 \x01(\v2\x0e.member.MemberR\x04data\"\xaa\x03\n" +
	"\x13UpdateMemberInfoReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x1a\n" +
	"\brealname\x18\x02 \x01(\tR\brealname\x12\x1a\n" +
	"\bnickname\x18\x03 \x01(\tR\bnickname\x12#\n" +
	"\rhead_portrait\x18\x04 \x01(\tR\fheadPortrait\x12\x16\n" +
	"\x06gender\x18\x05 \x01(\x05R\x06gender\x12\x0e\n" +
	"\x02qq\x18\x06 \x01(\tR\x02qq\x12\x14\n" +
	"\x05email\x18\a \x01(\tR\x05email\x12\x1a\n" +
	"\bbirthday\x18\b \x01(\tR\bbirthday\x12\x1f\n" +
	"\vprovince_id\x18\t \x01(\x03R\n" +
	"provinceId\x12\x17\n" +
	"\acity_id\x18\n" +
	" \x01(\x03R\x06cityId\x12\x17\n" +
	"\aarea_id\x18\v \x01(\x03R\x06areaId\x12\x18\n" +
	"\aaddress\x18\f \x01(\tR\aaddress\x12\x15\n" +
	"\x06tel_no\x18\r \x01(\tR\x05telNo\x12\x19\n" +
	"\bbg_image\x18\x0e \x01(\tR\abgImage\x12 \n" +
	"\vdescription\x18\x0f \x01(\tR\vdescription\"\x89\x01\n" +
	"\x10GetMemberListReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x18\n" +
	"\akeyword\x18\x03 \x01(\tR\akeyword\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\x12\x12\n" +
	"\x04type\x18\x05 \x01(\x05R\x04type\"\x94\x01\n" +
	"\x11GetMemberListResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04list\x18\x03 \x03(\v2\x0e.member.MemberR\x04list\x12-\n" +
	"\tpage_info\x18\x04 \x01(\v2\x10.member.PageInfoR\bpageInfo\".\n" +
	"\x0fDeleteMemberReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"\xfb\x01\n" +
	"\x0fCreateMemberReq\x12\x1f\n" +
	"\vmerchant_id\x18\x01 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x02 \x01(\x03R\astoreId\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x16\n" +
	"\x06mobile\x18\x05 \x01(\tR\x06mobile\x12\x14\n" +
	"\x05email\x18\x06 \x01(\tR\x05email\x12\x1a\n" +
	"\bnickname\x18\a \x01(\tR\bnickname\x12\x12\n" +
	"\x04type\x18\b \x01(\x05R\x04type\x12\x16\n" +
	"\x06source\x18\t \x01(\tR\x06source\"]\n" +
	"\x10CreateMemberResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1b\n" +
	"\tmember_id\x18\x03 \x01(\x03R\bmemberId\"6\n" +
	"\x17GetCertificationInfoReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"y\n" +
	"\x18GetCertificationInfoResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x04data\x18\x03 \x01(\v2\x1b.member.MemberCertificationR\x04data\"\xee\x01\n" +
	"\x16SubmitCertificationReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x1a\n" +
	"\brealname\x18\x02 \x01(\tR\brealname\x12#\n" +
	"\ridentity_card\x18\x03 \x01(\tR\fidentityCard\x12.\n" +
	"\x13identity_card_front\x18\x04 \x01(\tR\x11identityCardFront\x12,\n" +
	"\x12identity_card_back\x18\x05 \x01(\tR\x10identityCardBack\x12\x18\n" +
	"\aaddress\x18\x06 \x01(\tR\aaddress\"\xab\x01\n" +
	"\x15AuditCertificationReq\x12)\n" +
	"\x10certification_id\x18\x01 \x01(\x03R\x0fcertificationId\x12!\n" +
	"\faudit_result\x18\x02 \x01(\x05R\vauditResult\x12%\n" +
	"\x0erefusal_reason\x18\x03 \x01(\tR\rrefusalReason\x12\x1d\n" +
	"\n" +
	"auditor_id\x18\x04 \x01(\x03R\tauditorId\"Q\n" +
	"\x1eGetPendingCertificationListReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\"\xaf\x01\n" +
	"\x1fGetPendingCertificationListResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x04list\x18\x03 \x03(\v2\x1b.member.MemberCertificationR\x04list\x12-\n" +
	"\tpage_info\x18\x04 \x01(\v2\x10.member.PageInfoR\bpageInfo\"/\n" +
	"\x10GetMemberStatReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"i\n" +
	"\x11GetMemberStatResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x04data\x18\x03 \x01(\v2\x12.member.MemberStatR\x04data\"\xa1\x01\n" +
	"\x13UpdateMemberStatReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x19\n" +
	"\bnice_num\x18\x02 \x01(\x03R\aniceNum\x12\x1f\n" +
	"\vcollect_num\x18\x03 \x01(\x03R\n" +
	"collectNum\x12\x1d\n" +
	"\n" +
	"follow_num\x18\x04 \x01(\x03R\tfollowNum\x12\x12\n" +
	"\x04view\x18\x05 \x01(\x03R\x04view\"S\n" +
	"\x1aSubmitCancelApplicationReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\"1\n" +
	"\x12GetCancelStatusReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"m\n" +
	"\x13GetCancelStatusResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12(\n" +
	"\x04data\x18\x03 \x01(\v2\x14.member.MemberCancelR\x04data\"\xa1\x01\n" +
	"\x19AuditCancelApplicationReq\x12\x1b\n" +
	"\tcancel_id\x18\x01 \x01(\x03R\bcancelId\x12!\n" +
	"\faudit_result\x18\x02 \x01(\x05R\vauditResult\x12%\n" +
	"\x0erefusal_reason\x18\x03 \x01(\tR\rrefusalReason\x12\x1d\n" +
	"\n" +
	"auditor_id\x18\x04 \x01(\x03R\tauditorId\"J\n" +
	"\x17GetPendingCancelListReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\"\xa1\x01\n" +
	"\x18GetPendingCancelListResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12(\n" +
	"\x04list\x18\x03 \x03(\v2\x14.member.MemberCancelR\x04list\x12-\n" +
	"\tpage_info\x18\x04 \x01(\v2\x10.member.PageInfoR\bpageInfo2\xf3\v\n" +
	"\rMemberService\x125\n" +
	"\bRegister\x12\x13.member.RegisterReq\x1a\x14.member.RegisterResp\x12,\n" +
	"\x05Login\x12\x10.member.LoginReq\x1a\x11.member.LoginResp\x12/\n" +
	"\x06Logout\x12\x11.member.LogoutReq\x1a\x12.member.LogoutResp\x12A\n" +
	"\fRefreshToken\x12\x17.member.RefreshTokenReq\x1a\x18.member.RefreshTokenResp\x12=\n" +
	"\rResetPassword\x12\x18.member.ResetPasswordReq\x1a\x12.member.CommonResp\x12D\n" +
	"\rValidateToken\x12\x18.member.ValidateTokenReq\x1a\x19.member.ValidateTokenResp\x12D\n" +
	"\rGetMemberInfo\x12\x18.member.GetMemberInfoReq\x1a\x19.member.GetMemberInfoResp\x12C\n" +
	"\x10UpdateMemberInfo\x12\x1b.member.UpdateMemberInfoReq\x1a\x12.member.CommonResp\x12D\n" +
	"\rGetMemberList\x12\x18.member.GetMemberListReq\x1a\x19.member.GetMemberListResp\x12;\n" +
	"\fDeleteMember\x12\x17.member.DeleteMemberReq\x1a\x12.member.CommonResp\x12A\n" +
	"\fCreateMember\x12\x17.member.CreateMemberReq\x1a\x18.member.CreateMemberResp\x12Y\n" +
	"\x14GetCertificationInfo\x12\x1f.member.GetCertificationInfoReq\x1a .member.GetCertificationInfoResp\x12I\n" +
	"\x13SubmitCertification\x12\x1e.member.SubmitCertificationReq\x1a\x12.member.CommonResp\x12G\n" +
	"\x12AuditCertification\x12\x1d.member.AuditCertificationReq\x1a\x12.member.CommonResp\x12n\n" +
	"\x1bGetPendingCertificationList\x12&.member.GetPendingCertificationListReq\x1a'.member.GetPendingCertificationListResp\x12D\n" +
	"\rGetMemberStat\x12\x18.member.GetMemberStatReq\x1a\x19.member.GetMemberStatResp\x12C\n" +
	"\x10UpdateMemberStat\x12\x1b.member.UpdateMemberStatReq\x1a\x12.member.CommonResp\x12Q\n" +
	"\x17SubmitCancelApplication\x12\".member.SubmitCancelApplicationReq\x1a\x12.member.CommonResp\x12J\n" +
	"\x0fGetCancelStatus\x12\x1a.member.GetCancelStatusReq\x1a\x1b.member.GetCancelStatusResp\x12O\n" +
	"\x16AuditCancelApplication\x12!.member.AuditCancelApplicationReq\x1a\x12.member.CommonResp\x12Y\n" +
	"\x14GetPendingCancelList\x12\x1f.member.GetPendingCancelListReq\x1a .member.GetPendingCancelListRespB\n" +
	"Z\b./memberb\x06proto3"

var (
	file_pb_member_proto_rawDescOnce sync.Once
	file_pb_member_proto_rawDescData []byte
)

func file_pb_member_proto_rawDescGZIP() []byte {
	file_pb_member_proto_rawDescOnce.Do(func() {
		file_pb_member_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_member_proto_rawDesc), len(file_pb_member_proto_rawDesc)))
	})
	return file_pb_member_proto_rawDescData
}

var file_pb_member_proto_msgTypes = make([]protoimpl.MessageInfo, 41)
var file_pb_member_proto_goTypes = []any{
	(*CommonResp)(nil),                      // 0: member.CommonResp
	(*PageInfo)(nil),                        // 1: member.PageInfo
	(*Member)(nil),                          // 2: member.Member
	(*MemberPublic)(nil),                    // 3: member.MemberPublic
	(*MemberCertification)(nil),             // 4: member.MemberCertification
	(*MemberStat)(nil),                      // 5: member.MemberStat
	(*MemberCancel)(nil),                    // 6: member.MemberCancel
	(*RegisterReq)(nil),                     // 7: member.RegisterReq
	(*RegisterResp)(nil),                    // 8: member.RegisterResp
	(*LoginReq)(nil),                        // 9: member.LoginReq
	(*LoginResp)(nil),                       // 10: member.LoginResp
	(*LogoutReq)(nil),                       // 11: member.LogoutReq
	(*LogoutResp)(nil),                      // 12: member.LogoutResp
	(*RefreshTokenReq)(nil),                 // 13: member.RefreshTokenReq
	(*RefreshTokenResp)(nil),                // 14: member.RefreshTokenResp
	(*ResetPasswordReq)(nil),                // 15: member.ResetPasswordReq
	(*ValidateTokenReq)(nil),                // 16: member.ValidateTokenReq
	(*ValidateTokenResp)(nil),               // 17: member.ValidateTokenResp
	(*GetMemberInfoReq)(nil),                // 18: member.GetMemberInfoReq
	(*GetMemberInfoResp)(nil),               // 19: member.GetMemberInfoResp
	(*UpdateMemberInfoReq)(nil),             // 20: member.UpdateMemberInfoReq
	(*GetMemberListReq)(nil),                // 21: member.GetMemberListReq
	(*GetMemberListResp)(nil),               // 22: member.GetMemberListResp
	(*DeleteMemberReq)(nil),                 // 23: member.DeleteMemberReq
	(*CreateMemberReq)(nil),                 // 24: member.CreateMemberReq
	(*CreateMemberResp)(nil),                // 25: member.CreateMemberResp
	(*GetCertificationInfoReq)(nil),         // 26: member.GetCertificationInfoReq
	(*GetCertificationInfoResp)(nil),        // 27: member.GetCertificationInfoResp
	(*SubmitCertificationReq)(nil),          // 28: member.SubmitCertificationReq
	(*AuditCertificationReq)(nil),           // 29: member.AuditCertificationReq
	(*GetPendingCertificationListReq)(nil),  // 30: member.GetPendingCertificationListReq
	(*GetPendingCertificationListResp)(nil), // 31: member.GetPendingCertificationListResp
	(*GetMemberStatReq)(nil),                // 32: member.GetMemberStatReq
	(*GetMemberStatResp)(nil),               // 33: member.GetMemberStatResp
	(*UpdateMemberStatReq)(nil),             // 34: member.UpdateMemberStatReq
	(*SubmitCancelApplicationReq)(nil),      // 35: member.SubmitCancelApplicationReq
	(*GetCancelStatusReq)(nil),              // 36: member.GetCancelStatusReq
	(*GetCancelStatusResp)(nil),             // 37: member.GetCancelStatusResp
	(*AuditCancelApplicationReq)(nil),       // 38: member.AuditCancelApplicationReq
	(*GetPendingCancelListReq)(nil),         // 39: member.GetPendingCancelListReq
	(*GetPendingCancelListResp)(nil),        // 40: member.GetPendingCancelListResp
}
var file_pb_member_proto_depIdxs = []int32{
	2,  // 0: member.LoginResp.member_info:type_name -> member.Member
	2,  // 1: member.ValidateTokenResp.member_info:type_name -> member.Member
	2,  // 2: member.GetMemberInfoResp.data:type_name -> member.Member
	2,  // 3: member.GetMemberListResp.list:type_name -> member.Member
	1,  // 4: member.GetMemberListResp.page_info:type_name -> member.PageInfo
	4,  // 5: member.GetCertificationInfoResp.data:type_name -> member.MemberCertification
	4,  // 6: member.GetPendingCertificationListResp.list:type_name -> member.MemberCertification
	1,  // 7: member.GetPendingCertificationListResp.page_info:type_name -> member.PageInfo
	5,  // 8: member.GetMemberStatResp.data:type_name -> member.MemberStat
	6,  // 9: member.GetCancelStatusResp.data:type_name -> member.MemberCancel
	6,  // 10: member.GetPendingCancelListResp.list:type_name -> member.MemberCancel
	1,  // 11: member.GetPendingCancelListResp.page_info:type_name -> member.PageInfo
	7,  // 12: member.MemberService.Register:input_type -> member.RegisterReq
	9,  // 13: member.MemberService.Login:input_type -> member.LoginReq
	11, // 14: member.MemberService.Logout:input_type -> member.LogoutReq
	13, // 15: member.MemberService.RefreshToken:input_type -> member.RefreshTokenReq
	15, // 16: member.MemberService.ResetPassword:input_type -> member.ResetPasswordReq
	16, // 17: member.MemberService.ValidateToken:input_type -> member.ValidateTokenReq
	18, // 18: member.MemberService.GetMemberInfo:input_type -> member.GetMemberInfoReq
	20, // 19: member.MemberService.UpdateMemberInfo:input_type -> member.UpdateMemberInfoReq
	21, // 20: member.MemberService.GetMemberList:input_type -> member.GetMemberListReq
	23, // 21: member.MemberService.DeleteMember:input_type -> member.DeleteMemberReq
	24, // 22: member.MemberService.CreateMember:input_type -> member.CreateMemberReq
	26, // 23: member.MemberService.GetCertificationInfo:input_type -> member.GetCertificationInfoReq
	28, // 24: member.MemberService.SubmitCertification:input_type -> member.SubmitCertificationReq
	29, // 25: member.MemberService.AuditCertification:input_type -> member.AuditCertificationReq
	30, // 26: member.MemberService.GetPendingCertificationList:input_type -> member.GetPendingCertificationListReq
	32, // 27: member.MemberService.GetMemberStat:input_type -> member.GetMemberStatReq
	34, // 28: member.MemberService.UpdateMemberStat:input_type -> member.UpdateMemberStatReq
	35, // 29: member.MemberService.SubmitCancelApplication:input_type -> member.SubmitCancelApplicationReq
	36, // 30: member.MemberService.GetCancelStatus:input_type -> member.GetCancelStatusReq
	38, // 31: member.MemberService.AuditCancelApplication:input_type -> member.AuditCancelApplicationReq
	39, // 32: member.MemberService.GetPendingCancelList:input_type -> member.GetPendingCancelListReq
	8,  // 33: member.MemberService.Register:output_type -> member.RegisterResp
	10, // 34: member.MemberService.Login:output_type -> member.LoginResp
	12, // 35: member.MemberService.Logout:output_type -> member.LogoutResp
	14, // 36: member.MemberService.RefreshToken:output_type -> member.RefreshTokenResp
	0,  // 37: member.MemberService.ResetPassword:output_type -> member.CommonResp
	17, // 38: member.MemberService.ValidateToken:output_type -> member.ValidateTokenResp
	19, // 39: member.MemberService.GetMemberInfo:output_type -> member.GetMemberInfoResp
	0,  // 40: member.MemberService.UpdateMemberInfo:output_type -> member.CommonResp
	22, // 41: member.MemberService.GetMemberList:output_type -> member.GetMemberListResp
	0,  // 42: member.MemberService.DeleteMember:output_type -> member.CommonResp
	25, // 43: member.MemberService.CreateMember:output_type -> member.CreateMemberResp
	27, // 44: member.MemberService.GetCertificationInfo:output_type -> member.GetCertificationInfoResp
	0,  // 45: member.MemberService.SubmitCertification:output_type -> member.CommonResp
	0,  // 46: member.MemberService.AuditCertification:output_type -> member.CommonResp
	31, // 47: member.MemberService.GetPendingCertificationList:output_type -> member.GetPendingCertificationListResp
	33, // 48: member.MemberService.GetMemberStat:output_type -> member.GetMemberStatResp
	0,  // 49: member.MemberService.UpdateMemberStat:output_type -> member.CommonResp
	0,  // 50: member.MemberService.SubmitCancelApplication:output_type -> member.CommonResp
	37, // 51: member.MemberService.GetCancelStatus:output_type -> member.GetCancelStatusResp
	0,  // 52: member.MemberService.AuditCancelApplication:output_type -> member.CommonResp
	40, // 53: member.MemberService.GetPendingCancelList:output_type -> member.GetPendingCancelListResp
	33, // [33:54] is the sub-list for method output_type
	12, // [12:33] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_pb_member_proto_init() }
func file_pb_member_proto_init() {
	if File_pb_member_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_member_proto_rawDesc), len(file_pb_member_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   41,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_member_proto_goTypes,
		DependencyIndexes: file_pb_member_proto_depIdxs,
		MessageInfos:      file_pb_member_proto_msgTypes,
	}.Build()
	File_pb_member_proto = out.File
	file_pb_member_proto_goTypes = nil
	file_pb_member_proto_depIdxs = nil
}
