package auth

import (
	"context"
	"fmt"
	memberpb "member/member"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type LoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户登录
func NewLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginLogic {
	return &LoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LoginLogic) Login(req *types.LoginReq) (resp *types.LoginResp, err error) {

	// 组装RPC登录请求，支持用户名/手机号
	rpcReq := &memberpb.LoginReq{
		Password: req.Password,
	}
	if req.LoginType == "mobile" {
		rpcReq.Mobile = req.Username
		rpcReq.LoginType = 2
	} else {
		rpcReq.Username = req.Username
		rpcReq.LoginType = 1
	}

	// 调用RPC
	rpcResp, err := l.svcCtx.MemberRpc.Login(l.ctx, rpcReq)
	if err != nil {
		return nil, fmt.Errorf("调用rpc的Login方法失败: %v", err)
	}

	return &types.LoginResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
		Data: types.LoginData{
			AccessToken:  rpcResp.AccessToken,
			RefreshToken: rpcResp.RefreshToken,
			ExpiresIn:    l.svcCtx.Config.JwtAuth.AccessExpire,
		},
	}, nil
}
