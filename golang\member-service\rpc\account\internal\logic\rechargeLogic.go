package logic

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"account/account"
	"account/internal/svc"
)

type RechargeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRechargeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RechargeLogic {
	return &RechargeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *RechargeLogic) Recharge(in *account.RechargeReq) (*account.RechargeResp, error) {
	// 参数验证
	if in.MemberId <= 0 {
		return &account.RechargeResp{
			Code:    400,
			Message: "会员ID不能为空",
		}, nil
	}

	if in.Amount <= 0 {
		return &account.RechargeResp{
			Code:    400,
			Message: "充值金额必须大于0",
		}, nil
	}
	if in.PaymentMethod == "" {
		return &account.RechargeResp{
			Code:    400,
			Message: "请选择支付方式",
		}, nil
	}
	// 执行充值操作
	l.Logger.Infof("开始执行充值操作: member_id=%d, amount=%.2f", in.MemberId, in.Amount)
	err := l.svcCtx.MemberAccountModel.AddMoney(in.MemberId, in.Amount, false)
	if err != nil {
		l.Logger.Errorf("充值失败: member_id=%d, amount=%.2f, error=%v", in.MemberId, in.Amount, err)
		return &account.RechargeResp{
			Code:    500,
			Message: fmt.Sprintf("充值失败：%v", err),
		}, nil
	}
	l.Logger.Infof("充值操作成功: member_id=%d, amount=%.2f", in.MemberId, in.Amount)

	// 查询充值后的余额
	total, _, _, err := l.svcCtx.MemberAccountModel.GetBalance(in.MemberId)
	if err != nil {
		l.Logger.Errorf("获取余额失败: member_id=%d, error=%v", in.MemberId, err)
		total = 0 // 如果获取失败，设为0
	} else {
		l.Logger.Infof("获取余额成功: member_id=%d, balance=%.2f", in.MemberId, total)
	}

	// 生成订单号
	orderNo := in.OrderNo
	if orderNo == "" {
		orderNo = fmt.Sprintf("RC-%d-%d", in.MemberId, time.Now().Unix())
	}

	return &account.RechargeResp{
		Code:         200,
		Message:      "充值订单创建成功",
		OrderNo:      orderNo,
		BalanceAfter: total,
	}, nil
}
