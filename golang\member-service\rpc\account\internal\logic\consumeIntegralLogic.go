package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConsumeIntegralLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewConsumeIntegralLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConsumeIntegralLogic {
	return &ConsumeIntegralLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ConsumeIntegralLogic) ConsumeIntegral(in *account.ConsumeIntegralReq) (*account.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &account.CommonResp{}, nil
}
