package member

import (
	"context"
	"encoding/json"
	"fmt"
	"member/member"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type GetMemberStatLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取统计信息
func NewGetMemberStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberStatLogic {
	return &GetMemberStatLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMemberStatLogic) GetMemberStat(req *types.GetMemberStatReq) (resp *types.GetMemberStatResp, err error) {
	var memberId int64
	// 从JWT token 中获取当前用户ID
	memberIdvalue := l.ctx.Value("member_id")
	if memberIdvalue == nil {
		return nil, fmt.Errorf("用户身份信息验证失败: %v", err)
	}

	switch v := memberIdvalue.(type) {
	case int64:
		memberId = v
	case float64:
		memberId = int64(v)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	case json.Number:
		if parsed, parseErr := v.Int64(); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	default:
		return nil, fmt.Errorf("用户身份信息类型错误：%T", v)
	}

	if memberId <= 0 {
		return nil, fmt.Errorf("从token中解析的用户ID无效")
	}

	// 开始调用RPC服务处理
	rpcResp, err := l.svcCtx.MemberRpc.GetMemberStat(l.ctx, &member.GetMemberStatReq{
		MemberId: memberId,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC获取用户统计信息失败: %v", err)
	}

	if rpcResp.Code != 200 {
		return &types.GetMemberStatResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}

	return &types.GetMemberStatResp{
		Code:    200,
		Message: "获取用户统计信息成功",
		Data: types.MemberStat{
			Id:          rpcResp.Data.Id,
			MerchantId:  rpcResp.Data.MerchantId,
			StoreId:     rpcResp.Data.StoreId,
			MemberId:    rpcResp.Data.MemberId,
			MemberType:  int(rpcResp.Data.MemberType),
			NiceNum:     rpcResp.Data.NiceNum,
			DisagreeNum: rpcResp.Data.DisagreeNum,
			TransmitNum: rpcResp.Data.TransmitNum,
			CommentNum:  rpcResp.Data.CommentNum,
			CollectNum:  rpcResp.Data.CollectNum,
			ReportNum:   rpcResp.Data.ReportNum,
		},
	}, nil
}
