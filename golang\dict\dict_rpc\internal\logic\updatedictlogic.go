﻿package logic

import (
	"context"
	"errors"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"
	"dict_rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type UpdateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictLogic {
	return &UpdateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典项
func (l *UpdateDictLogic) UpdateDict(in *dict.UpdateDictReq) (*dict.UpdateDictResp, error) {
	// todo: add your logic here and delete this line

	// 参数验证
	if in.Id <= 0 {
		return &dict.UpdateDictResp{
			Success: false,
			Message: "字典ID不能为空",
		}, nil
	}

	// 检查字典是否存在
	_, err := l.svcCtx.DictModel.FindByID(in.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dict.UpdateDictResp{
				Success: false,
				Message: "字典不存在",
			}, nil
		}
		return &dict.UpdateDictResp{
			Success: false,
			Message: "查询字典失败",
		}, nil
	}

	// 更新字典项
	err = l.svcCtx.DictModel.Update(&model.Dict{
		ID:     in.Id,
		Code:   in.Code,
		Name:   in.Name,
		Remark: in.Remark,
		Status: in.Status,
	})

	if err != nil {
		return &dict.UpdateDictResp{
			Success: false,
			Message: "更新字典项失败",
		}, nil
	}
	return &dict.UpdateDictResp{
		Success: true,
		Message: "更新字典项成功",
	}, nil
}

