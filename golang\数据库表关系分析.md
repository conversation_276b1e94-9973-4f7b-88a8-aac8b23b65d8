# MP_DB 数据库表关系分析文档

## 数据库概述

**数据库名称**: `mp_db`  
**创建时间**: 2025-08-11 17:46:05  
**MySQL版本**: 5.7.44  
**字符集**: utf8mb4  
**总表数**: 59个表  

这是一个农田项目管理系统数据库，主要用于高标准农田建设项目的全流程管理。

## 核心业务关系图

### 1. 项目管理核心关系

```
region (地区表)
    ↓ (1:N)
project (项目主表)
    ↓ (1:N)
    ├── mp_list (模排清单表)
    ├── mp_question (摸排问题表)  
    ├── project_build (项目建设信息表)
    ├── project_file (项目附件表)
    ├── project_pifu (项目批复表)
    └── project_check (工程造价审核表)
```

**关键外键关系**:
- `mp_list.region_id` → `region.id`
- `mp_list.project_id` → `project.id` 
- `project.region_id` → `region.id`
- `mp_question.project_id` → `project.id`
- `project_build.project_id` → `project.id`
- `project_file.project_id` → `project.id`
- `project_pifu.project_id` → `project.id`
- `project_check.project_id` → `project.id`

### 2. 用户管理体系关系

```
member (用户主表)
    ↓ (1:1)
    ├── member_account (账户信息)
    ├── member_certification (实名认证)
    ├── member_stat (用户统计)
    └── member_cancel (注销申请)
    ↓ (1:N)
    ├── member_address (收货地址)
    ├── member_auth (第三方授权)
    ├── member_bank_account (银行账户)
    ├── member_credits_log (积分记录)
    ├── member_invoice (发票信息)
    └── member_withdraw_deposit (提现记录)
```

**关键字段关系**:
- 所有member相关表通过 `member_id` 关联到 `member.id`
- `member_type` 字段区分用户类型：1-会员，2-后台管理员，3-商家管理员

### 3. 权限管理(RBAC)关系

```
rbac_auth_role (角色表)
    ↓ (N:N) via rbac_auth_assignment
member (用户表)

rbac_auth_item (权限表)
    ↓ (N:N) via rbac_auth_item_child  
rbac_auth_role (角色表)
```

**权限分配流程**:
1. `rbac_auth_assignment` 表实现用户-角色多对多关系
2. `rbac_auth_item_child` 表实现角色-权限多对多关系
3. 通过 `role_check` 表处理权限申请工单

### 4. 字典配置系统关系

```
dict (字典主表)
    ↓ (1:N)
dict_item (字典项表)
```

**关系说明**:
- `dict_item.dict_id` → `dict.id` (外键约束)
- 支持树形结构: `dict_item.pid` 指向父级字典项

## 详细表关系分析

### A. 多租户架构关系

几乎所有业务表都包含以下字段，形成多租户架构：
- `merchant_id`: 商户ID，关联到 `merchant.id`
- `store_id`: 店铺ID  
- `member_id`: 操作用户ID，关联到 `member.id`

### B. 地理信息关系

```
common_provinces (省市区表)
    ↓ (树形结构)
region (地区表)
    ↓ (1:N)
project (项目表)
    ↓ (1:N)
mp_live (现场模排表)
```

### C. 文件管理关系

```
common_attachment_cate (附件分类)
    ↓ (1:N)
common_attachment (附件表)
    ↑ (被引用)
project_file (项目附件)
```

### D. 消息通知关系

```
common_notify (消息主表)
    ↓ (1:N)
common_notify_member (用户消息关联表)

common_notify_config (通知配置)
    ↓ (配置驱动)
消息发送流程

common_notify_pull_time (消息拉取时间记录)
```

### E. 支付系统关系

```
extend_pay_log (支付日志)
    ↓ (1:N)
extend_pay_refund (退款记录)
```

**关联字段**:
- `extend_pay_refund.pay_id` → `extend_pay_log.id`
- 通过 `out_trade_no` 和 `order_sn` 关联业务订单

### F. OAuth2认证关系

```
oauth2_client (客户端表)
    ↓ (1:N)
    ├── oauth2_access_token (访问令牌)
    ├── oauth2_refresh_token (刷新令牌)  
    └── oauth2_authorization_code (授权码)
```

**关系说明**:
- 所有OAuth2相关表通过 `client_id` 关联到客户端
- `member_id` 关联到具体用户

## 业务流程中的表关系

### 1. 项目创建流程

```
1. region (选择地区)
2. project (创建项目基础信息)
3. project_pifu (上传批复文件)
4. project_build (填写建设信息)
5. project_file (上传相关附件)
6. project_check (提交审核)
```

### 2. 模排检查流程

```
1. project (基于已有项目)
2. mp_question (填报问题)
3. mp_live (现场检查记录)
4. mp_list (生成问题清单)
```

### 3. 用户注册登录流程

```
1. member (创建用户基础信息)
2. member_account (初始化账户)
3. member_auth (第三方授权绑定)
4. oauth2_* (生成认证令牌)
```

## 数据完整性约束

### 实际外键约束（数据库级别）
SQL文件中实际定义的外键约束只有4个：
1. `dict_item.dict_id` → `dict.id`
2. `mp_list.project_id` → `project.id` (CASCADE删除和更新)
3. `mp_list.region_id` → `region.id` (CASCADE删除和更新)  
4. `project.region_id` → `region.id`

### 逻辑外键关系（应用层面）
以下关系通过字段命名约定建立，但未在数据库层面定义约束：
- `mp_question.project_id` → `project.id`
- `project_build.project_id` → `project.id`
- `project_file.project_id` → `project.id`
- `project_pifu.project_id` → `project.id`
- `project_check.project_id` → `project.id`
- 所有member相关表通过 `member_id` 关联到 `member.id`
- 多租户相关表通过 `merchant_id` 关联到 `merchant.id`

### 索引关系
- 主键索引：所有表的 `id` 字段
- 外键索引：`member_id`, `merchant_id`, `region_id`, `project_id` 等
- 业务索引：`access_token`, `refresh_token`, `mobile`, `email` 等
- 复合索引：`(merchant_id, member_id)`, `(status, created_at)` 等

## 表分类汇总

### 核心业务表 (10个)
- `project`, `project_build`, `project_file`, `project_pifu`, `project_check`
- `mp_list`, `mp_live`, `mp_question`
- `xmjs`, `xmjx` (项目建设相关)

### 用户管理表 (15个)  
- `member` 及其相关的14个扩展表：
- `member_account`, `member_address`, `member_auth`, `member_bank_account`
- `member_cancel`, `member_certification`, `member_credits_log`, `member_invoice`
- `member_level`, `member_level_config`, `member_stat`, `member_tag`
- `member_tag_map`, `member_withdraw_deposit`

### 权限认证表 (8个)
- RBAC权限表(4个): `rbac_auth_assignment`, `rbac_auth_item`, `rbac_auth_item_child`, `rbac_auth_role`
- OAuth2认证表(4个): `oauth2_access_token`, `oauth2_authorization_code`, `oauth2_client`, `oauth2_refresh_token`

### 系统配置表 (8个)
- `region`, `region_pf`, `dict`, `dict_item`, `common_provinces`, `extend_config`, `download_task`, `role_check`

### 通用功能表 (17个)
- 日志类(3个): `common_log`, `common_report_log`, `extend_sms_log`
- 通知类(5个): `common_notify`, `common_notify_announce`, `common_notify_config`, `common_notify_member`, `common_notify_pull_time`
- 文件类(2个): `common_attachment`, `common_attachment_cate`
- 菜单类(2个): `common_menu`, `common_menu_cate`
- 支付类(2个): `extend_pay_log`, `extend_pay_refund`
- 其他(3个): `common_theme`, `merchant`, `api_access_token`

### 系统表 (1个)
- `migration` (数据库迁移记录)

## 设计特点总结

1. **松耦合设计**: 通过ID关联而非硬编码，便于扩展
2. **多租户支持**: 统一的 `merchant_id` 实现数据隔离  
3. **软删除策略**: 通过 `status` 字段标记删除状态
4. **时间戳追踪**: `created_at`, `updated_at` 记录数据变更
5. **树形结构支持**: 通过 `pid`, `level`, `tree` 字段实现层级关系
6. **插件化架构**: `is_addon`, `addon_name` 支持功能扩展
7. **审计日志完整**: 操作日志、支付日志、风控日志全覆盖

这种设计确保了系统的可扩展性、可维护性和数据完整性，适合复杂的农业项目管理需求。

## 表关系类型详细分析

### 一对一关系 (1:1)

用户基础信息的扩展表，通常每个用户只有一条记录：

1. **member ↔ member_account** - 用户账户信息
2. **member ↔ member_certification** - 实名认证信息  
3. **member ↔ member_stat** - 用户统计信息
4. **member ↔ member_cancel** - 注销申请记录

### 一对多关系 (1:N)

#### 核心业务关系
- **region → project** - 地区包含多个项目
- **project → mp_list/mp_question/project_build/project_file/project_pifu/project_check** - 项目相关的各种信息

#### 用户管理关系  
- **member → member_address/member_auth/member_bank_account/member_credits_log/member_invoice/member_withdraw_deposit** - 用户扩展信息

#### 字典配置关系
- **dict → dict_item** - 字典包含多个选项
- **dict_item → dict_item** (自关联) - 支持树形结构

#### OAuth2认证关系
- **oauth2_client → oauth2_access_token/oauth2_refresh_token/oauth2_authorization_code** - 客户端认证相关

### 多对多关系 (N:N)

通过中间表实现：

1. **member ↔ rbac_auth_role** (通过 `rbac_auth_assignment`) - 用户角色分配
2. **rbac_auth_role ↔ rbac_auth_item** (通过 `rbac_auth_item_child`) - 角色权限分配  
3. **member ↔ member_tag** (通过 `member_tag_map`) - 用户标签关系

### 关系统计总结

#### 数据库级别的约束
- **实际外键约束**: 4个
- **索引关系**: 主键索引 + 外键索引 + 业务索引 + 复合索引

#### 应用层面的逻辑关系
- **一对一关系**: 4个
- **一对多关系**: 35个
- **多对多关系**: 3个
- **总计逻辑关系**: 42个主要表关系

### 关系设计特点

1. **混合约束模式**: 少量核心关系使用数据库外键约束，大部分关系依赖应用层维护
2. **主要采用一对多关系**: 占总关系的83%，符合业务逻辑
3. **合理的一对一扩展**: 用于用户信息的模块化管理
4. **标准的多对多实现**: 通过中间表实现权限管理和标签系统
5. **树形结构支持**: 通过自关联实现层级管理
6. **多租户隔离**: 通过merchant_id实现数据隔离
7. **软删除和审计**: 通过status字段和时间戳字段实现数据追踪

## 重要说明

### 数据库约束策略分析

本数据库采用了**最小外键约束**的设计策略：

#### 优点：
1. **性能优化**: 减少外键检查开销，提高插入、更新、删除操作性能
2. **灵活性**: 便于数据迁移、测试数据生成和系统重构
3. **避免级联问题**: 防止误操作导致的大量数据被级联删除
4. **分布式友好**: 适合微服务架构和分布式数据库部署

#### 风险：
1. **数据完整性**: 依赖应用层保证引用完整性，存在数据不一致风险
2. **开发复杂度**: 需要在应用代码中维护数据关系和完整性检查
3. **调试困难**: 数据异常时需要应用层排查，不如数据库约束直观

#### 建议：
- 对于核心业务关系（如项目-地区），考虑添加外键约束
- 在应用层实现完善的数据验证和事务管理
- 定期进行数据完整性检查和清理
- 建立数据质量监控机制 