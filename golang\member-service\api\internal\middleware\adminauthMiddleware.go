package middleware

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/golang-jwt/jwt/v4"
)

type AdminAuthMiddleware struct {
	jwtSecret string
}

func NewAdminAuthMiddleware(jwtSecret string) *AdminAuthMiddleware {
	return &AdminAuthMiddleware{
		jwtSecret: jwtSecret,
	}
}

func (m *AdminAuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从请求头获取 JWT token
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			m.writeErrorResponse(w, http.StatusUnauthorized, "缺少认证令牌")
			return
		}

		// 解析 Bearer token
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			m.writeErrorResponse(w, http.StatusUnauthorized, "认证令牌格式错误")
			return
		}

		// 解析 JWT token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			return []byte(m.jwtSecret), nil
		})

		if err != nil || !token.Valid {
			m.writeErrorResponse(w, http.StatusUnauthorized, "认证令牌无效")
			return
		}

		// 检查用户角色
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			m.writeErrorResponse(w, http.StatusUnauthorized, "认证令牌格式错误")
			return
		}

			userType, exists := claims["type"]
			if !exists {
				m.writeErrorResponse(w, http.StatusForbidden, "用户类型信息缺失")
				return
			}

			// 检查是否为管理员（type = 2）
			if userTypeFloat, ok := userType.(float64); !ok || int(userTypeFloat) != 2 {
				m.writeErrorResponse(w, http.StatusForbidden, "权限不足，需要管理员权限")
			return
		}

		// 权限验证通过，继续处理请求
		next(w, r)
	}
}

// writeErrorResponse 写入错误响应
func (m *AdminAuthMiddleware) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"code":    statusCode,
		"message": message,
		"data":    nil,
	}

	json.NewEncoder(w).Encode(response)
}
