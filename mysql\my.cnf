[mysqld]
# 基本配置
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
lc-messages-dir = /usr/share/mysql

# 兼容性配置
lower_case_table_names = 1
default_authentication_plugin = mysql_native_password
skip-host-cache
skip-name-resolve
explicit_defaults_for_timestamp = ON

# InnoDB 配置优化
innodb_use_native_aio = 0
innodb_flush_method = fsync
innodb_force_recovery = 0
innodb_buffer_pool_size = 128M
innodb_log_file_size = 64M
innodb_log_buffer_size = 8M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 50

# 字符集配置
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 连接配置
max_connections = 200
connect_timeout = 60
wait_timeout = 28800
interactive_timeout = 28800

# 日志配置
log_error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 其他优化
sql_mode = NO_ENGINE_SUBSTITUTION
max_allowed_packet = 64M
table_open_cache = 256
sort_buffer_size = 2M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
myisam_sort_buffer_size = 64M 