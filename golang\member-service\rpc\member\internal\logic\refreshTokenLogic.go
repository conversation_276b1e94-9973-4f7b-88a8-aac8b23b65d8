package logic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
	"member/middleware/JWT"
)

type RefreshTokenLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRefreshTokenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RefreshTokenLogic {
	return &RefreshTokenLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *RefreshTokenLogic) RefreshToken(in *member.RefreshTokenReq) (*member.RefreshTokenResp, error) {
	secret := l.svcCtx.Config.JwtAuth.AccessSecret
	if in.RefreshToken == "" {
		return &member.RefreshTokenResp{Code: 400, Message: "缺少刷新令牌"}, nil
	}

	// 解析refresh token获取用户信息
	userInfo, err := JWT.ParseToken(in.RefreshToken, secret)
	if err != nil {
		return &member.RefreshTokenResp{Code: 401, Message: "刷新令牌无效或已过期"}, nil
	}

	// 生成新的token对
	accessToken, err := JWT.GenerateToken(
		secret,
		l.svcCtx.Config.JwtAuth.AccessExpire,
		*userInfo,
	)
	if err != nil {
		return &member.RefreshTokenResp{Code: 500, Message: "签发访问令牌失败"}, nil
	}

	newRefreshToken, err := JWT.GenerateToken(
		secret,
		JWT.DefaultRefreshTokenExpire,
		*userInfo,
	)
	if err != nil {
		return &member.RefreshTokenResp{Code: 500, Message: "签发刷新令牌失败"}, nil
	}

	return &member.RefreshTokenResp{
		Code:         200,
		Message:      "刷新token成功",
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
	}, nil
}
