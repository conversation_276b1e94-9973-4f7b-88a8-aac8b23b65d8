package model

import (
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// MemberAccount 账户表
type MemberAccount struct {
	ID                   int64   `gorm:"primaryKey;autoIncrement;column:id"`
	MerchantID           int64   `gorm:"column:merchant_id"`
	StoreID              int64   `gorm:"column:store_id"`
	MemberID             int64   `gorm:"column:member_id;index"`
	MemberType           int32   `gorm:"column:member_type"`
	UserMoney            float64 `gorm:"column:user_money"`
	AccumulateMoney      float64 `gorm:"column:accumulate_money"`
	GiveMoney            float64 `gorm:"column:give_money"`
	ConsumeMoney         float64 `gorm:"column:consume_money"`
	FrozenMoney          float64 `gorm:"column:frozen_money"`
	UserIntegral         int64   `gorm:"column:user_integral"`
	AccumulateIntegral   int64   `gorm:"column:accumulate_integral"`
	GiveIntegral         int64   `gorm:"column:give_integral"`
	ConsumeIntegral      float64 `gorm:"column:consume_integral"`
	FrozenIntegral       int64   `gorm:"column:frozen_integral"`
	UserGrowth           int64   `gorm:"column:user_growth"`
	AccumulateGrowth     int64   `gorm:"column:accumulate_growth"`
	ConsumeGrowth        int64   `gorm:"column:consume_growth"`
	FrozenGrowth         int64   `gorm:"column:frozen_growth"`
	EconomizeMoney       float64 `gorm:"column:economize_money"`
	AccumulateDrawnMoney float64 `gorm:"column:accumulate_drawn_money"`
	Status               int32   `gorm:"column:status"`
	CreatedAt            int64   `gorm:"column:created_at"`
	UpdatedAt            int64   `gorm:"column:updated_at"`
}

func (MemberAccount) TableName() string { return "member_account" }

type MemberAccountModel struct{ db *gorm.DB }

func NewMemberAccountModel(db *gorm.DB) *MemberAccountModel { return &MemberAccountModel{db: db} }

func (m *MemberAccountModel) FindByMemberID(memberID int64) (*MemberAccount, error) {
	var data MemberAccount
	if err := m.db.Where("member_id=?", memberID).First(&data).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("会员ID=%d的账户不存在", memberID)
		}
		return nil, err
	}
	return &data, nil
}

func (m *MemberAccountModel) Create(data *MemberAccount) error { return m.db.Create(data).Error }

func (m *MemberAccountModel) Update(data *MemberAccount) error { return m.db.Save(data).Error }
