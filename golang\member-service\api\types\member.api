syntax = "v1"

// ===== 用户信息结构 =====
type Member {
    Id                    int64  `json:"id"`
    MerchantId           int64  `json:"merchant_id"`
    StoreId              int64  `json:"store_id"`
    Username             string `json:"username"`
    PasswordHash         string `json:"-"`                          // 密码哈希（不返回给前端）
    AuthKey              string `json:"-"`                          // 授权令牌（不返回给前端）
    PasswordResetToken   string `json:"-"`                          // 密码重置令牌（不返回给前端）
    MobileResetToken     string `json:"-"`                          // 手机重置令牌（不返回给前端）
    Type                 int    `json:"type"`
    Realname             string `json:"realname"`
    Nickname             string `json:"nickname"`
    HeadPortrait         string `json:"head_portrait"`
    Gender               int    `json:"gender"`
    Qq                   string `json:"qq"`
    Email                string `json:"email"`
    Birthday             string `json:"birthday"`
    ProvinceId           int64  `json:"province_id"`
    CityId               int64  `json:"city_id"`
    AreaId               int64  `json:"area_id"`
    Address              string `json:"address"`
    Mobile               string `json:"mobile"`
    TelNo                string `json:"tel_no"`
    BgImage              string `json:"bg_image"`
    Description          string `json:"description"`
    VisitCount           int    `json:"visit_count"`
    LastTime             int64  `json:"last_time"`
    LastIp               string `json:"last_ip"`
    Role                 int    `json:"role"`
    CurrentLevel         int    `json:"current_level"`
    LevelExpirationTime  int64  `json:"level_expiration_time"`
    LevelBuyType         int    `json:"level_buy_type"`
    Pid                  int64  `json:"pid"`
    Level                int    `json:"level"`
    Tree                 string `json:"tree"`
    PromoterCode         string `json:"promoter_code"`
    CertificationType    int    `json:"certification_type"`
    Source               string `json:"source"`
    Status               int    `json:"status"`
    CreatedAt            int64  `json:"created_at"`
    UpdatedAt            int64  `json:"updated_at"`
    RegionId             int64  `json:"region_id"`
}

// ===== 请求参数 =====
type GetMemberInfoReq {
    MemberId int64 `form:"member_id,optional"`
}

type GetMemberInfoResp {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    Member `json:"data"`
}

type UpdateMemberInfoReq {
    Realname     string `json:"realname,optional"`
    Nickname     string `json:"nickname,optional"`
    HeadPortrait string `json:"head_portrait,optional"`
    Gender       int    `json:"gender,optional"`
    Qq           string `json:"qq,optional"`
    Email        string `json:"email,optional"`
    Birthday     string `json:"birthday,optional"`
    ProvinceId   int64  `json:"province_id,optional"`
    CityId       int64  `json:"city_id,optional"`
    AreaId       int64  `json:"area_id,optional"`
    Address      string `json:"address,optional"`
    TelNo        string `json:"tel_no,optional"`
    BgImage      string `json:"bg_image,optional"`
    Description  string `json:"description,optional"`
}

type GetMemberListReq {
    BasePageReq
    Username string `form:"username,optional"`
    Mobile   string `form:"mobile,optional"`
    Status   int    `form:"status,optional"`
    Type     int    `form:"type,optional"`
}

type MemberListData {
    List     []Member `json:"list"`
    PageInfo PageInfo `json:"page_info"`
}

type GetMemberListResp {
    Code    int            `json:"code"`
    Message string         `json:"message"`
    Data    MemberListData `json:"data"`
}

type GetMemberDetailReq {
    Id int64 `path:"id"`
}

type GetMemberDetailResp {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    Member `json:"data"`
}

type DeleteMemberReq {
    Id int64 `path:"id"`
}

// ===== 认证相关 =====
type RegisterReq {
    Username     string `json:"username"`
    Password     string `json:"password"`
    Mobile       string `json:"mobile"`
    Email        string `json:"email,optional"`
    Nickname     string `json:"nickname,optional"`
    VerifyCode   string `json:"verify_code"`
    Source       string `json:"source,optional"`
}

type RegisterData {
    MemberId int64 `json:"member_id"`
}

type RegisterResp {
    Code    int          `json:"code"`
    Message string       `json:"message"`
    Data    RegisterData `json:"data"`
}

type LoginReq {
    Username   string `json:"username"`
    Password   string `json:"password"`
    LoginType  string `json:"login_type"` // username, mobile, email
    VerifyCode string `json:"verify_code,optional"`
}

type LoginData {
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
    ExpiresIn    int64  `json:"expires_in"`
    UserInfo     Member `json:"user_info,omitempty"`
}

type LoginResp {
    Code    int       `json:"code"`
    Message string    `json:"message"`
    Data    LoginData `json:"data"`
}

type LogoutReq {
    Token string `json:"token,optional"`
}

type RefreshTokenReq {
    RefreshToken string `json:"refresh_token"`
}

type RefreshTokenData {
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
    ExpiresIn    int64  `json:"expires_in"`
}

type RefreshTokenResp {
    Code    int              `json:"code"`
    Message string           `json:"message"`
    Data    RefreshTokenData `json:"data"`
}

type ResetPasswordReq {
    Mobile      string `json:"mobile"`
    NewPassword string `json:"new_password"`
    VerifyCode  string `json:"verify_code"`
} 