﻿// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: dict.proto

package dictservice

import (
	"context"

	"dict_rpc/dict"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CreateDictReq  = dict.CreateDictReq
	CreateDictResp = dict.CreateDictResp
	DeleteDictReq  = dict.DeleteDictReq
	DeleteDictResp = dict.DeleteDictResp
	Dict           = dict.Dict
	GetDictReq     = dict.GetDictReq
	GetDictResp    = dict.GetDictResp
	ListDictReq    = dict.ListDictReq
	ListDictResp   = dict.ListDictResp
	UpdateDictReq  = dict.UpdateDictReq
	UpdateDictResp = dict.UpdateDictResp

	DictService interface {
		// 创建字典
		CreateDict(ctx context.Context, in *CreateDictReq, opts ...grpc.CallOption) (*CreateDictResp, error)
		// 更新字典项
		UpdateDict(ctx context.Context, in *UpdateDictReq, opts ...grpc.CallOption) (*UpdateDictResp, error)
		// 删除字典
		DeleteDict(ctx context.Context, in *DeleteDictReq, opts ...grpc.CallOption) (*DeleteDictResp, error)
		// 获取字典详情
		GetDict(ctx context.Context, in *GetDictReq, opts ...grpc.CallOption) (*GetDictResp, error)
		// 字典列表
		ListDict(ctx context.Context, in *ListDictReq, opts ...grpc.CallOption) (*ListDictResp, error)
	}

	defaultDictService struct {
		cli zrpc.Client
	}
)

func NewDictService(cli zrpc.Client) DictService {
	return &defaultDictService{
		cli: cli,
	}
}

// 创建字典
func (m *defaultDictService) CreateDict(ctx context.Context, in *CreateDictReq, opts ...grpc.CallOption) (*CreateDictResp, error) {
	client := dict.NewDictServiceClient(m.cli.Conn())
	return client.CreateDict(ctx, in, opts...)
}

// 更新字典项
func (m *defaultDictService) UpdateDict(ctx context.Context, in *UpdateDictReq, opts ...grpc.CallOption) (*UpdateDictResp, error) {
	client := dict.NewDictServiceClient(m.cli.Conn())
	return client.UpdateDict(ctx, in, opts...)
}

// 删除字典
func (m *defaultDictService) DeleteDict(ctx context.Context, in *DeleteDictReq, opts ...grpc.CallOption) (*DeleteDictResp, error) {
	client := dict.NewDictServiceClient(m.cli.Conn())
	return client.DeleteDict(ctx, in, opts...)
}

// 获取字典详情
func (m *defaultDictService) GetDict(ctx context.Context, in *GetDictReq, opts ...grpc.CallOption) (*GetDictResp, error) {
	client := dict.NewDictServiceClient(m.cli.Conn())
	return client.GetDict(ctx, in, opts...)
}

// 字典列表
func (m *defaultDictService) ListDict(ctx context.Context, in *ListDictReq, opts ...grpc.CallOption) (*ListDictResp, error) {
	client := dict.NewDictServiceClient(m.cli.Conn())
	return client.ListDict(ctx, in, opts...)
}

