package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddMoneyLogLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddMoneyLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddMoneyLogLogic {
	return &AddMoneyLogLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 账户日志
func (l *AddMoneyLogLogic) AddMoneyLog(in *account.AddMoneyLogReq) (*account.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &account.CommonResp{}, nil
}
