FROM golang:1.23-alpine AS builder

LABEL stage=gobuilder

ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64
ENV GOPROXY=https://goproxy.cn,direct
ENV GOMAXPROCS=0

RUN apk update --no-cache && apk add --no-cache tzdata git

WORKDIR /build

# 先复制所有源代码（本地模块依赖需要源代码）
COPY . .

# 进入RPC目录并下载依赖
WORKDIR /build/member-service/rpc/member
RUN go mod download

# 构建应用
RUN go build -ldflags="-s -w -extldflags '-static'" -a -installsuffix cgo -o /app/member-rpc member.go

FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata && \
    mkdir -p /root/logs && \
    addgroup -g 1000 appgroup && \
    adduser -D -s /bin/sh -u 1000 -G appgroup appuser

WORKDIR /root/

COPY --from=builder /app/member-rpc .
COPY --from=builder /build/member-service/rpc/member/etc ./etc

RUN chmod +x member-rpc && \
    chown -R appuser:appgroup /root

USER appuser

EXPOSE 9002

CMD ["./member-rpc", "-f", "etc/member.yaml"]
