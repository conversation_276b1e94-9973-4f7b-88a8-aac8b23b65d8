package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetIntegralInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetIntegralInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetIntegralInfoLogic {
	return &GetIntegralInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 积分操作
func (l *GetIntegralInfoLogic) GetIntegralInfo(in *account.GetIntegralInfoReq) (*account.GetIntegralInfoResp, error) {
	// todo: add your logic here and delete this line

	return &account.GetIntegralInfoResp{}, nil
}
