syntax = "v1"

// ===== 实名认证信息结构 =====
type MemberCertification {
	Id                int64  `json:"id"`
	MerchantId        int64  `json:"merchant_id"`
	StoreId           int64  `json:"store_id"`
	MemberId          int64  `json:"member_id"`
	MemberType        int    `json:"member_type"`
	Realname          string `json:"realname"`
	IdentityCard      string `json:"identity_card"`
	IdentityCardFront string `json:"identity_card_front"`
	IdentityCardBack  string `json:"identity_card_back"`
	Gender            string `json:"gender"`
	Birthday          string `json:"birthday"`
	FrontIsFake       int    `json:"front_is_fake"`
	BackIsFake        int    `json:"back_is_fake"`
	Nationality       string `json:"nationality"`
	Address           string `json:"address"`
	StartDate         string `json:"start_date"`
	EndDate           string `json:"end_date"`
	Issue             string `json:"issue"`
	IsSelf            int    `json:"is_self"`
	Status            int    `json:"status"`
	CreatedAt         int64  `json:"created_at"`
	UpdatedAt         int64  `json:"updated_at"`
}

// ===== 认证状态枚举 =====
type CertificationStatus {
	Pending  int `json:"pending"` // 0:待审核
	Approved int `json:"approved"` // 1:已通过
	Rejected int `json:"rejected"` // -1:已拒绝
}

// ===== 获取认证信息 =====
type GetCertificationInfoReq {
	MemberId int64 `form:"member_id,optional"`
}

type GetCertificationInfoResp {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    MemberCertification `json:"data"`
}

// ===== 提交实名认证 =====
type SubmitCertificationReq {
	Realname          string `json:"realname"`
	IdentityCard      string `json:"identity_card"`
	IdentityCardFront string `json:"identity_card_front"`
	IdentityCardBack  string `json:"identity_card_back"`
	Gender            string `json:"gender,optional"`
	Birthday          string `json:"birthday,optional"`
	Nationality       string `json:"nationality,optional"`
	Address           string `json:"address,optional"`
	StartDate         string `json:"start_date,optional"`
	EndDate           string `json:"end_date,optional"`
	Issue             string `json:"issue,optional"`
	IsSelf            int    `json:"is_self,optional"`
}

// ===== 更新认证信息（暂未实现对应API接口） =====
// type UpdateCertificationReq 已移除，如需要请先在 desc/member-api.api 中添加对应路由
// ===== 获取认证状态 =====
type GetCertificationStatusReq {
	MemberId int64 `form:"member_id,optional"`
}

type CertificationStatusData {
	Status       int    `json:"status"` // 认证状态
	StatusText   string `json:"status_text"` // 状态描述
	RejectReason string `json:"reject_reason"` // 拒绝原因
	SubmitTime   int64  `json:"submit_time"` // 提交时间
	AuditTime    int64  `json:"audit_time"` // 审核时间
}

type GetCertificationStatusResp {
	Code    int                     `json:"code"`
	Message string                  `json:"message"`
	Data    CertificationStatusData `json:"data"`
}

// ===== 审核相关（管理员） =====
type GetPendingListReq {
	BasePageReq
	MemberId  int64  `form:"member_id,optional"`
	Realname  string `form:"realname,optional"`
	StartTime int64  `form:"start_time,optional"`
	EndTime   int64  `form:"end_time,optional"`
}

type PendingCertification {
	Id           int64  `json:"id"`
	MemberId     int64  `json:"member_id"`
	Username     string `json:"username"`
	Mobile       string `json:"mobile"`
	Realname     string `json:"realname"`
	IdentityCard string `json:"identity_card"`
	SubmitTime   int64  `json:"submit_time"`
	Status       int    `json:"status"`
}

type PendingListData {
	List     []PendingCertification `json:"list"`
	PageInfo PageInfo               `json:"page_info"`
}

type GetPendingListResp {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    PendingListData `json:"data"`
}

type AuditCertificationReq {
	Id           int64  `json:"id"`
	Status       int    `json:"status"` // 1:通过 -1:拒绝
	RejectReason string `json:"reject_reason,optional"` // 拒绝原因
	Remark       string `json:"remark,optional"`
}

type BatchAuditReq {
	Ids          []int64 `json:"ids"`
	Status       int     `json:"status"` // 1:通过 -1:拒绝
	RejectReason string  `json:"reject_reason,optional"` // 拒绝原因
	Remark       string  `json:"remark,optional"`
}

