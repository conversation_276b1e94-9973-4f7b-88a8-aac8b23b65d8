package admin

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPendingCertificationListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取待审核认证列表
func NewGetPendingCertificationListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPendingCertificationListLogic {
	return &GetPendingCertificationListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPendingCertificationListLogic) GetPendingCertificationList(req *types.GetPendingListReq) (resp *types.GetPendingListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
