package logic

import (
	"context"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPendingCertificationListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetPendingCertificationListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPendingCertificationListLogic {
	return &GetPendingCertificationListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetPendingCertificationListLogic) GetPendingCertificationList(in *member.GetPendingCertificationListReq) (*member.GetPendingCertificationListResp, error) {
	// todo: add your logic here and delete this line

	return &member.GetPendingCertificationListResp{}, nil
}
