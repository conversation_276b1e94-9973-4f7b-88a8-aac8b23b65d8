package logic

import (
	"context"
	"fmt"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBalanceLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetBalanceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBalanceLogic {
	return &GetBalanceLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetBalanceLogic) GetBalance(in *account.GetBalanceReq) (*account.GetBalanceResp, error) {
	// 参数验证
	if in.MemberId <= 0 {
		return &account.GetBalanceResp{
			Code:    400,
			Message: "会员ID不能为空",
		}, nil
	}

	// 获取余额信息
	total, _, frozen, err := l.svcCtx.MemberAccountModel.GetBalance(in.MemberId)
	if err != nil {
		l.Logger.Errorf("获取余额失败: member_id=%d, error=%v", in.MemberId, err)
		return &account.GetBalanceResp{
			Code:    404,
			Message: fmt.Sprintf("获取余额失败: %v", err),
		}, nil
	}

	// 获取累计消费
	accountInfo, err := l.svcCtx.MemberAccountModel.FindByMemberID(in.MemberId)
	var consumeMoney float64
	if err != nil {
		l.Logger.Errorf("获取账户信息失败: member_id=%d, error=%v", in.MemberId, err)
		consumeMoney = 0
	} else {
		consumeMoney = accountInfo.ConsumeMoney
	}

	return &account.GetBalanceResp{
		Code:            200,
		Message:         "获取余额成功",
		UserMoney:       total,
		AccumulateMoney: total + consumeMoney, // 累计余额 = 当前余额 + 累计消费
		FrozenMoney:     frozen,
		ConsumeMoney:    consumeMoney,
	}, nil
}
