package member

import (
	"account/account"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type WithdrawLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 提现
func NewWithdrawLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawLogic {
	return &WithdrawLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawLogic) Withdraw(req *types.WithdrawReq) (resp *types.WithdrawResp, err error) {
	var memberId int64
	// 从JWT token 中获取当前用户member_id
	memberIdvalue := l.ctx.Value("member_id")
	if memberIdvalue == nil {
		return nil, fmt.Errorf("无法获取用户身份信息")
	}

	// 开始处理不同的类型的member_id的值
	switch v := memberIdvalue.(type) {
	case int64:
		memberId = v
	case float64:
		memberId = int64(v)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	case json.Number:
		if parsed, parseErr := v.Int64(); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	default:
		return nil, fmt.Errorf("用户身份信息类型错误: %T", v)
	}
	if memberId <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}
	// 调用RPC服务开始处理业务
	rpcResp, err := l.svcCtx.AccountRpc.Withdraw(l.ctx, &account.WithdrawReq{
		MemberId:        memberId,
		Amount:          req.Amount,
		WithdrawAccount: req.AccountNo,
		Remark:          req.Remark,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC提现失败: %v", err)
	}
	return &types.WithdrawResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
		Data: types.WithdrawData{
			WithdrawNo:   rpcResp.WithdrawNo,
			Amount:       req.Amount,
			Fee:          0,
			ActualAmount: req.Amount,
		},
	}, nil
}
