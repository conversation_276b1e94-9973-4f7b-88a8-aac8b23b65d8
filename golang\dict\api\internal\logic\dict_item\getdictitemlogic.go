﻿package dict_item

import (
	"context"
	"dict_item_rpc/dict_item"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictItemLogic {
	return &GetDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictItemLogic) GetDictItem(req *types.GetDictItemReq) (resp *types.GetDictItemResp, err error) {
	// 调用dict_item RPC服务获取字典项详情
	respRpc, err := l.svcCtx.DictItemRpc.GetDictItem(l.ctx, &dict_item.GetDictItemReq{
		Id: req.Id,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_item的rpc服务GetDictItem失败: %v", err)
	}

	return &types.GetDictItemResp{
		Item: types.DictItem{
			Id:          respRpc.Item.Id,
			DictId:      respRpc.Item.DictId,
			CategoryId:  respRpc.Item.CategoryId,
			Code:        respRpc.Item.Code,
			Name:        respRpc.Item.Name,
			Status:      respRpc.Item.Status,
			CreatedTime: respRpc.Item.CreatedTime,
			UpdatedTime: respRpc.Item.UpdatedTime,
		},
		Message: "字典项详情成功",
	}, nil
}
