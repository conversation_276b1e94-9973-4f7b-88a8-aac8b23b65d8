﻿package dict

import (
	"context"
	"dict_rpc/dict"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取字典详情
func NewGetDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictLogic {
	return &GetDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictLogic) GetDict(req *types.GetDictReq) (resp *types.GetDictResp, err error) {
	// todo: add your logic here and delete this line

	// 调用dict_rpc服务
	respRpc, err := l.svcCtx.DictRpc.GetDict(l.ctx, &dict.GetDictReq{
		Id: req.Id,
	})

	if err != nil {
		return nil,fmt.Errorf("调用dict_rpc服务的GetDict方法失败: %v",err)
	}
	

	return &types.GetDictResp{
		Dict:   types.Dict{
			Id:          respRpc.Dict.Id,
			Code:        respRpc.Dict.Code,
			Name:        respRpc.Dict.Name,
			Remark:      respRpc.Dict.Remark,
			Status:      respRpc.Dict.Status,
			CreatedTime: respRpc.Dict.CreatedTime,
			UpdatedTime: respRpc.Dict.UpdatedTime,
		},
		Message: "获取字典详情成功",
	},nil
}

