﻿// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: dict_category.proto

package server

import (
	"context"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/logic"
	"dict_category_rpc/internal/svc"
)

type DictCategoryServiceServer struct {
	svcCtx *svc.ServiceContext
	dict_category.UnimplementedDictCategoryServiceServer
}

func NewDictCategoryServiceServer(svcCtx *svc.ServiceContext) *DictCategoryServiceServer {
	return &DictCategoryServiceServer{
		svcCtx: svcCtx,
	}
}

// 创建字典分类
func (s *DictCategoryServiceServer) CreateDictCategory(ctx context.Context, in *dict_category.CreateDictCategoryReq) (*dict_category.CreateDictCategoryResp, error) {
	l := logic.NewCreateDictCategoryLogic(ctx, s.svcCtx)
	return l.CreateDictCategory(in)
}

// 更新字典项分类
func (s *DictCategoryServiceServer) UpdateDictCategory(ctx context.Context, in *dict_category.UpdateDictCategoryReq) (*dict_category.UpdateDictCategoryResp, error) {
	l := logic.NewUpdateDictCategoryLogic(ctx, s.svcCtx)
	return l.UpdateDictCategory(in)
}

// 删除字典分类
func (s *DictCategoryServiceServer) DeleteDictCategory(ctx context.Context, in *dict_category.DeleteDictCategoryReq) (*dict_category.DeleteDictCategoryResp, error) {
	l := logic.NewDeleteDictCategoryLogic(ctx, s.svcCtx)
	return l.DeleteDictCategory(in)
}

// 获取字典分类详情
func (s *DictCategoryServiceServer) GetDictCategory(ctx context.Context, in *dict_category.GetDictCategoryReq) (*dict_category.GetDictCategoryResp, error) {
	l := logic.NewGetDictCategoryLogic(ctx, s.svcCtx)
	return l.GetDictCategory(in)
}

// 字典分类列表
func (s *DictCategoryServiceServer) ListDictCategory(ctx context.Context, in *dict_category.ListDictCategoryReq) (*dict_category.ListDictCategoryResp, error) {
	l := logic.NewListDictCategoryLogic(ctx, s.svcCtx)
	return l.ListDictCategory(in)
}

