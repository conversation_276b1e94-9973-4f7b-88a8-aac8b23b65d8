// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.0
// source: pb/account.proto

package account

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AccountService_CreateAccount_FullMethodName   = "/account.AccountService/CreateAccount"
	AccountService_GetAccountInfo_FullMethodName  = "/account.AccountService/GetAccountInfo"
	AccountService_GetAccountList_FullMethodName  = "/account.AccountService/GetAccountList"
	AccountService_GetBalance_FullMethodName      = "/account.AccountService/GetBalance"
	AccountService_Recharge_FullMethodName        = "/account.AccountService/Recharge"
	AccountService_Withdraw_FullMethodName        = "/account.AccountService/Withdraw"
	AccountService_FreezeMoney_FullMethodName     = "/account.AccountService/FreezeMoney"
	AccountService_UnfreezeMoney_FullMethodName   = "/account.AccountService/UnfreezeMoney"
	AccountService_GetIntegralInfo_FullMethodName = "/account.AccountService/GetIntegralInfo"
	AccountService_AddIntegral_FullMethodName     = "/account.AccountService/AddIntegral"
	AccountService_ConsumeIntegral_FullMethodName = "/account.AccountService/ConsumeIntegral"
	AccountService_GetGrowthInfo_FullMethodName   = "/account.AccountService/GetGrowthInfo"
	AccountService_AddGrowth_FullMethodName       = "/account.AccountService/AddGrowth"
	AccountService_AddMoneyLog_FullMethodName     = "/account.AccountService/AddMoneyLog"
	AccountService_AddIntegralLog_FullMethodName  = "/account.AccountService/AddIntegralLog"
	AccountService_GetAccountLog_FullMethodName   = "/account.AccountService/GetAccountLog"
)

// AccountServiceClient is the client API for AccountService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ===== Account服务定义 =====
type AccountServiceClient interface {
	// 账户信息管理
	CreateAccount(ctx context.Context, in *CreateAccountReq, opts ...grpc.CallOption) (*CreateAccountResp, error)
	GetAccountInfo(ctx context.Context, in *GetAccountInfoReq, opts ...grpc.CallOption) (*GetAccountInfoResp, error)
	GetAccountList(ctx context.Context, in *GetAccountListReq, opts ...grpc.CallOption) (*GetAccountListResp, error)
	// 余额操作
	GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceResp, error)
	Recharge(ctx context.Context, in *RechargeReq, opts ...grpc.CallOption) (*RechargeResp, error)
	Withdraw(ctx context.Context, in *WithdrawReq, opts ...grpc.CallOption) (*WithdrawResp, error)
	FreezeMoney(ctx context.Context, in *FreezeMoneyReq, opts ...grpc.CallOption) (*CommonResp, error)
	UnfreezeMoney(ctx context.Context, in *UnfreezeMoneyReq, opts ...grpc.CallOption) (*CommonResp, error)
	// 积分操作
	GetIntegralInfo(ctx context.Context, in *GetIntegralInfoReq, opts ...grpc.CallOption) (*GetIntegralInfoResp, error)
	AddIntegral(ctx context.Context, in *AddIntegralReq, opts ...grpc.CallOption) (*CommonResp, error)
	ConsumeIntegral(ctx context.Context, in *ConsumeIntegralReq, opts ...grpc.CallOption) (*CommonResp, error)
	// 成长值操作
	GetGrowthInfo(ctx context.Context, in *GetGrowthInfoReq, opts ...grpc.CallOption) (*GetGrowthInfoResp, error)
	AddGrowth(ctx context.Context, in *AddGrowthReq, opts ...grpc.CallOption) (*CommonResp, error)
	// 账户日志
	AddMoneyLog(ctx context.Context, in *AddMoneyLogReq, opts ...grpc.CallOption) (*CommonResp, error)
	AddIntegralLog(ctx context.Context, in *AddIntegralLogReq, opts ...grpc.CallOption) (*CommonResp, error)
	GetAccountLog(ctx context.Context, in *GetAccountLogReq, opts ...grpc.CallOption) (*GetAccountLogResp, error)
}

type accountServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountServiceClient(cc grpc.ClientConnInterface) AccountServiceClient {
	return &accountServiceClient{cc}
}

func (c *accountServiceClient) CreateAccount(ctx context.Context, in *CreateAccountReq, opts ...grpc.CallOption) (*CreateAccountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAccountResp)
	err := c.cc.Invoke(ctx, AccountService_CreateAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) GetAccountInfo(ctx context.Context, in *GetAccountInfoReq, opts ...grpc.CallOption) (*GetAccountInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountInfoResp)
	err := c.cc.Invoke(ctx, AccountService_GetAccountInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) GetAccountList(ctx context.Context, in *GetAccountListReq, opts ...grpc.CallOption) (*GetAccountListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountListResp)
	err := c.cc.Invoke(ctx, AccountService_GetAccountList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBalanceResp)
	err := c.cc.Invoke(ctx, AccountService_GetBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) Recharge(ctx context.Context, in *RechargeReq, opts ...grpc.CallOption) (*RechargeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RechargeResp)
	err := c.cc.Invoke(ctx, AccountService_Recharge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) Withdraw(ctx context.Context, in *WithdrawReq, opts ...grpc.CallOption) (*WithdrawResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WithdrawResp)
	err := c.cc.Invoke(ctx, AccountService_Withdraw_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) FreezeMoney(ctx context.Context, in *FreezeMoneyReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, AccountService_FreezeMoney_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) UnfreezeMoney(ctx context.Context, in *UnfreezeMoneyReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, AccountService_UnfreezeMoney_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) GetIntegralInfo(ctx context.Context, in *GetIntegralInfoReq, opts ...grpc.CallOption) (*GetIntegralInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIntegralInfoResp)
	err := c.cc.Invoke(ctx, AccountService_GetIntegralInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) AddIntegral(ctx context.Context, in *AddIntegralReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, AccountService_AddIntegral_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) ConsumeIntegral(ctx context.Context, in *ConsumeIntegralReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, AccountService_ConsumeIntegral_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) GetGrowthInfo(ctx context.Context, in *GetGrowthInfoReq, opts ...grpc.CallOption) (*GetGrowthInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGrowthInfoResp)
	err := c.cc.Invoke(ctx, AccountService_GetGrowthInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) AddGrowth(ctx context.Context, in *AddGrowthReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, AccountService_AddGrowth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) AddMoneyLog(ctx context.Context, in *AddMoneyLogReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, AccountService_AddMoneyLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) AddIntegralLog(ctx context.Context, in *AddIntegralLogReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, AccountService_AddIntegralLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) GetAccountLog(ctx context.Context, in *GetAccountLogReq, opts ...grpc.CallOption) (*GetAccountLogResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountLogResp)
	err := c.cc.Invoke(ctx, AccountService_GetAccountLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountServiceServer is the server API for AccountService service.
// All implementations must embed UnimplementedAccountServiceServer
// for forward compatibility.
//
// ===== Account服务定义 =====
type AccountServiceServer interface {
	// 账户信息管理
	CreateAccount(context.Context, *CreateAccountReq) (*CreateAccountResp, error)
	GetAccountInfo(context.Context, *GetAccountInfoReq) (*GetAccountInfoResp, error)
	GetAccountList(context.Context, *GetAccountListReq) (*GetAccountListResp, error)
	// 余额操作
	GetBalance(context.Context, *GetBalanceReq) (*GetBalanceResp, error)
	Recharge(context.Context, *RechargeReq) (*RechargeResp, error)
	Withdraw(context.Context, *WithdrawReq) (*WithdrawResp, error)
	FreezeMoney(context.Context, *FreezeMoneyReq) (*CommonResp, error)
	UnfreezeMoney(context.Context, *UnfreezeMoneyReq) (*CommonResp, error)
	// 积分操作
	GetIntegralInfo(context.Context, *GetIntegralInfoReq) (*GetIntegralInfoResp, error)
	AddIntegral(context.Context, *AddIntegralReq) (*CommonResp, error)
	ConsumeIntegral(context.Context, *ConsumeIntegralReq) (*CommonResp, error)
	// 成长值操作
	GetGrowthInfo(context.Context, *GetGrowthInfoReq) (*GetGrowthInfoResp, error)
	AddGrowth(context.Context, *AddGrowthReq) (*CommonResp, error)
	// 账户日志
	AddMoneyLog(context.Context, *AddMoneyLogReq) (*CommonResp, error)
	AddIntegralLog(context.Context, *AddIntegralLogReq) (*CommonResp, error)
	GetAccountLog(context.Context, *GetAccountLogReq) (*GetAccountLogResp, error)
	mustEmbedUnimplementedAccountServiceServer()
}

// UnimplementedAccountServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAccountServiceServer struct{}

func (UnimplementedAccountServiceServer) CreateAccount(context.Context, *CreateAccountReq) (*CreateAccountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccount not implemented")
}
func (UnimplementedAccountServiceServer) GetAccountInfo(context.Context, *GetAccountInfoReq) (*GetAccountInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountInfo not implemented")
}
func (UnimplementedAccountServiceServer) GetAccountList(context.Context, *GetAccountListReq) (*GetAccountListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountList not implemented")
}
func (UnimplementedAccountServiceServer) GetBalance(context.Context, *GetBalanceReq) (*GetBalanceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalance not implemented")
}
func (UnimplementedAccountServiceServer) Recharge(context.Context, *RechargeReq) (*RechargeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Recharge not implemented")
}
func (UnimplementedAccountServiceServer) Withdraw(context.Context, *WithdrawReq) (*WithdrawResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Withdraw not implemented")
}
func (UnimplementedAccountServiceServer) FreezeMoney(context.Context, *FreezeMoneyReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FreezeMoney not implemented")
}
func (UnimplementedAccountServiceServer) UnfreezeMoney(context.Context, *UnfreezeMoneyReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnfreezeMoney not implemented")
}
func (UnimplementedAccountServiceServer) GetIntegralInfo(context.Context, *GetIntegralInfoReq) (*GetIntegralInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIntegralInfo not implemented")
}
func (UnimplementedAccountServiceServer) AddIntegral(context.Context, *AddIntegralReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddIntegral not implemented")
}
func (UnimplementedAccountServiceServer) ConsumeIntegral(context.Context, *ConsumeIntegralReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeIntegral not implemented")
}
func (UnimplementedAccountServiceServer) GetGrowthInfo(context.Context, *GetGrowthInfoReq) (*GetGrowthInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGrowthInfo not implemented")
}
func (UnimplementedAccountServiceServer) AddGrowth(context.Context, *AddGrowthReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGrowth not implemented")
}
func (UnimplementedAccountServiceServer) AddMoneyLog(context.Context, *AddMoneyLogReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddMoneyLog not implemented")
}
func (UnimplementedAccountServiceServer) AddIntegralLog(context.Context, *AddIntegralLogReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddIntegralLog not implemented")
}
func (UnimplementedAccountServiceServer) GetAccountLog(context.Context, *GetAccountLogReq) (*GetAccountLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountLog not implemented")
}
func (UnimplementedAccountServiceServer) mustEmbedUnimplementedAccountServiceServer() {}
func (UnimplementedAccountServiceServer) testEmbeddedByValue()                        {}

// UnsafeAccountServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountServiceServer will
// result in compilation errors.
type UnsafeAccountServiceServer interface {
	mustEmbedUnimplementedAccountServiceServer()
}

func RegisterAccountServiceServer(s grpc.ServiceRegistrar, srv AccountServiceServer) {
	// If the following call pancis, it indicates UnimplementedAccountServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AccountService_ServiceDesc, srv)
}

func _AccountService_CreateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).CreateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_CreateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).CreateAccount(ctx, req.(*CreateAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_GetAccountInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).GetAccountInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_GetAccountInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).GetAccountInfo(ctx, req.(*GetAccountInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_GetAccountList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).GetAccountList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_GetAccountList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).GetAccountList(ctx, req.(*GetAccountListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_GetBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).GetBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_GetBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).GetBalance(ctx, req.(*GetBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_Recharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RechargeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).Recharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_Recharge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).Recharge(ctx, req.(*RechargeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_Withdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithdrawReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).Withdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_Withdraw_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).Withdraw(ctx, req.(*WithdrawReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_FreezeMoney_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeMoneyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).FreezeMoney(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_FreezeMoney_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).FreezeMoney(ctx, req.(*FreezeMoneyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_UnfreezeMoney_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfreezeMoneyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).UnfreezeMoney(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_UnfreezeMoney_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).UnfreezeMoney(ctx, req.(*UnfreezeMoneyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_GetIntegralInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIntegralInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).GetIntegralInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_GetIntegralInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).GetIntegralInfo(ctx, req.(*GetIntegralInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_AddIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).AddIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_AddIntegral_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).AddIntegral(ctx, req.(*AddIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_ConsumeIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).ConsumeIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_ConsumeIntegral_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).ConsumeIntegral(ctx, req.(*ConsumeIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_GetGrowthInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGrowthInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).GetGrowthInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_GetGrowthInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).GetGrowthInfo(ctx, req.(*GetGrowthInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_AddGrowth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGrowthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).AddGrowth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_AddGrowth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).AddGrowth(ctx, req.(*AddGrowthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_AddMoneyLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMoneyLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).AddMoneyLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_AddMoneyLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).AddMoneyLog(ctx, req.(*AddMoneyLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_AddIntegralLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddIntegralLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).AddIntegralLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_AddIntegralLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).AddIntegralLog(ctx, req.(*AddIntegralLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_GetAccountLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).GetAccountLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AccountService_GetAccountLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).GetAccountLog(ctx, req.(*GetAccountLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountService_ServiceDesc is the grpc.ServiceDesc for AccountService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "account.AccountService",
	HandlerType: (*AccountServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAccount",
			Handler:    _AccountService_CreateAccount_Handler,
		},
		{
			MethodName: "GetAccountInfo",
			Handler:    _AccountService_GetAccountInfo_Handler,
		},
		{
			MethodName: "GetAccountList",
			Handler:    _AccountService_GetAccountList_Handler,
		},
		{
			MethodName: "GetBalance",
			Handler:    _AccountService_GetBalance_Handler,
		},
		{
			MethodName: "Recharge",
			Handler:    _AccountService_Recharge_Handler,
		},
		{
			MethodName: "Withdraw",
			Handler:    _AccountService_Withdraw_Handler,
		},
		{
			MethodName: "FreezeMoney",
			Handler:    _AccountService_FreezeMoney_Handler,
		},
		{
			MethodName: "UnfreezeMoney",
			Handler:    _AccountService_UnfreezeMoney_Handler,
		},
		{
			MethodName: "GetIntegralInfo",
			Handler:    _AccountService_GetIntegralInfo_Handler,
		},
		{
			MethodName: "AddIntegral",
			Handler:    _AccountService_AddIntegral_Handler,
		},
		{
			MethodName: "ConsumeIntegral",
			Handler:    _AccountService_ConsumeIntegral_Handler,
		},
		{
			MethodName: "GetGrowthInfo",
			Handler:    _AccountService_GetGrowthInfo_Handler,
		},
		{
			MethodName: "AddGrowth",
			Handler:    _AccountService_AddGrowth_Handler,
		},
		{
			MethodName: "AddMoneyLog",
			Handler:    _AccountService_AddMoneyLog_Handler,
		},
		{
			MethodName: "AddIntegralLog",
			Handler:    _AccountService_AddIntegralLog_Handler,
		},
		{
			MethodName: "GetAccountLog",
			Handler:    _AccountService_GetAccountLog_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/account.proto",
}
