package member

import (
	accountpb "account/account"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type GetAccountInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取账户信息
func NewGetAccountInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAccountInfoLogic {
	return &GetAccountInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAccountInfoLogic) GetAccountInfo(req *types.GetAccountInfoReq) (resp *types.GetAccountInfoResp, err error) {
	var memberId int64

	// 如果请求中指定了member_id，使用指定的ID
	if req.MemberId > 0 {
		memberId = req.MemberId
	} else {
		// 否则从JWT token中获取当前用户member_id
		memberIdvalue := l.ctx.Value("member_id")
		if memberIdvalue == nil {
			return nil, fmt.Errorf("无法获取用户身份信息")
		}
		// 开始处理不同类型的member_id的值
		switch v := memberIdvalue.(type) {
		case int64:
			memberId = v
		case float64:
			memberId = int64(v)
		case string:
			if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
				memberId = parsed
			} else {
				return nil, fmt.Errorf("用户身份信息格式错误")
			}
		case json.Number:
			if parsed, parseErr := v.Int64(); parseErr == nil {
				memberId = parsed
			} else {
				return nil, fmt.Errorf("用户身份信息格式错误")
			}
		default:
			return nil, fmt.Errorf("用户身份信息类型错误: %T", v)
		}
	}

	if memberId <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}

	// 调用RPC服务获取账户信息
	rpcResp, err := l.svcCtx.AccountRpc.GetAccountInfo(l.ctx, &accountpb.GetAccountInfoReq{
		MemberId: memberId,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC获取账户信息失败: %v", err)
	}
	if rpcResp.Code != 200 {
		return &types.GetAccountInfoResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}

	return &types.GetAccountInfoResp{
		Code:    200,
		Message: "获取账户信息成功",
		Data: types.MemberAccount{
			Id:                   rpcResp.Data.Id,
			MerchantId:           rpcResp.Data.MerchantId,
			StoreId:              rpcResp.Data.StoreId,
			MemberId:             rpcResp.Data.MemberId,
			MemberType:           int(rpcResp.Data.MemberType),
			UserMoney:            rpcResp.Data.UserMoney,
			AccumulateMoney:      rpcResp.Data.AccumulateMoney,
			GiveMoney:            rpcResp.Data.GiveMoney,
			ConsumeMoney:         rpcResp.Data.ConsumeMoney,
			FrozenMoney:          rpcResp.Data.FrozenMoney,
			UserIntegral:         rpcResp.Data.UserIntegral,
			AccumulateIntegral:   rpcResp.Data.AccumulateIntegral,
			GiveIntegral:         rpcResp.Data.GiveIntegral,
			ConsumeIntegral:      rpcResp.Data.ConsumeIntegral,
			FrozenIntegral:       rpcResp.Data.FrozenIntegral,
			UserGrowth:           rpcResp.Data.UserGrowth,
			AccumulateGrowth:     rpcResp.Data.AccumulateGrowth,
			ConsumeGrowth:        rpcResp.Data.ConsumeGrowth,
			FrozenGrowth:         rpcResp.Data.FrozenGrowth,
			EconomizeMoney:       rpcResp.Data.EconomizeMoney,
			AccumulateDrawnMoney: rpcResp.Data.AccumulateDrawnMoney,
			Status:               int(rpcResp.Data.Status),
			CreatedAt:            rpcResp.Data.CreatedAt,
			UpdatedAt:            rpcResp.Data.UpdatedAt,
		},
	}, nil
}
