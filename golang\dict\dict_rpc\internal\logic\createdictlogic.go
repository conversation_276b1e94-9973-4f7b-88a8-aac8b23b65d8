package logic

import (
	"context"
	"fmt"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"
	"dict_rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictLogic {
	return &CreateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典
func (l *CreateDictLogic) CreateDict(in *dict.CreateDictReq) (*dict.CreateDictResp, error) {
	// 创建字典业务逻辑

	// 参数验证
	if in.Name == "" {
		return &dict.CreateDictResp{
			Message: "字典名称不能为空",
		}, nil
	}

	// 检查字典名称是否已存在
	exists, err := l.svcCtx.DictModel.CheckNameExists(in.Name, 0)
	if err != nil {
		return nil, fmt.Errorf("检查字典名称失败: %v", err)
	}

	if exists {
		return nil, fmt.Errorf("字典名称已存在")
	}

	// 检查字典编码是否已存在
	codeExists, err := l.svcCtx.DictModel.CheckCodeExists(in.Code, 0)
	if err != nil {
		return nil, fmt.Errorf("检查字典编码失败: %v", err)
	}

	if codeExists {
		return nil, fmt.Errorf("字典编码已存在")
	}

	// 插入数据
	err = l.svcCtx.DictModel.Create(&model.Dict{
		Code:   in.Code,
		Name:   in.Name,
		Remark: in.Remark,
		Status: in.Status,
	})

	if err != nil {
		return nil, fmt.Errorf("插入数据失败: %v", err)
	}

	// 查询数据库 返回数据
	data, err := l.svcCtx.DictModel.FindByName(in.Name)

	if err != nil {
		return nil, fmt.Errorf("查询数据失败: %v", err)
	}

	return &dict.CreateDictResp{
		Id:      data.ID,
		Message: "创建字典成功",
	}, nil
}
