package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddIntegralLogLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddIntegralLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddIntegralLogLogic {
	return &AddIntegralLogLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *AddIntegralLogLogic) AddIntegralLog(in *account.AddIntegralLogReq) (*account.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &account.CommonResp{}, nil
}
