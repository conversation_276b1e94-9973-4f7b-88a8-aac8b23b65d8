package logic

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"account/account"
	"account/internal/svc"
)

type WithdrawLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewWithdrawLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawLogic {
	return &WithdrawLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *WithdrawLogic) Withdraw(in *account.WithdrawReq) (*account.WithdrawResp, error) {
	// 参数验证
	if in.MemberId <= 0 {
		return &account.WithdrawResp{
			Code:    400,
			Message: "会员ID不能为空",
		}, nil
	}

	if in.Amount <= 0 {
		return &account.WithdrawResp{
			Code:    400,
			Message: "提现金额必须大于0",
		}, nil
	}

	if in.WithdrawAccount == "" {
		return &account.WithdrawResp{
			Code:    400,
			Message: "提现账户不能为空",
		}, nil
	}

	// 执行提现操作
	err := l.svcCtx.MemberAccountModel.Withdraw(in.MemberId, in.Amount)
	if err != nil {
		l.Logger.Errorf("提现失败: member_id=%d, amount=%.2f, error=%v", in.MemberId, in.Amount, err)
		return &account.WithdrawResp{
			Code:    500,
			Message: fmt.Sprintf("提现失败: %v", err),
		}, nil
	}

	// 查询提现后余额
	total, _, _, err := l.svcCtx.MemberAccountModel.GetBalance(in.MemberId)
	if err != nil {
		l.Logger.Errorf("获取余额失败: member_id=%d, error=%v", in.MemberId, err)
		total = 0 // 如果获取失败，设为0
	}

	// 生成提现单号
	withdrawNo := fmt.Sprintf("WD%d%d", in.MemberId, time.Now().Unix())

	l.Logger.Infof("提现成功: member_id=%d, amount=%.2f, withdraw_no=%s, balance_after=%.2f",
		in.MemberId, in.Amount, withdrawNo, total)

	return &account.WithdrawResp{
		Code:         200,
		Message:      "提现申请已提交，请等待审核",
		WithdrawNo:   withdrawNo,
		BalanceAfter: total,
	}, nil
}
