-- =====================================================
-- Member Service Database Initialization Script
-- 会员服务数据库初始化脚本
-- =====================================================

-- 设置字符集和时区
SET NAMES utf8mb4;
SET time_zone = '+00:00';
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `mp_db` 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE `mp_db`;

-- =====================================================
-- 会员主表 (member)
-- =====================================================
CREATE TABLE IF NOT EXISTS `member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` bigint(20) DEFAULT 0 COMMENT '商户ID',
  `store_id` bigint(20) DEFAULT 0 COMMENT '店铺ID',
  `username` varchar(20) NOT NULL COMMENT '用户名',
  `password_hash` varchar(150) NOT NULL COMMENT '密码哈希',
  `auth_key` varchar(32) DEFAULT '' COMMENT '认证密钥',
    `password_reset_token` varchar(150) DEFAULT '' COMMENT '密码重置令牌',
  `mobile_reset_token` varchar(150) DEFAULT '' COMMENT '手机重置令牌',
  `type` int(11) DEFAULT 1 COMMENT '用户类型',
    `realname` varchar(50) DEFAULT '' COMMENT '真实姓名',
    `nickname` varchar(60) DEFAULT '' COMMENT '昵称',
  `head_portrait` varchar(150) DEFAULT '' COMMENT '头像',
  `gender` int(11) DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `qq` varchar(20) DEFAULT '' COMMENT 'QQ号',
    `email` varchar(60) DEFAULT '' COMMENT '邮箱',
    `birthday` date DEFAULT NULL COMMENT '生日',
  `province_id` bigint(20) DEFAULT 0 COMMENT '省份ID',
  `city_id` bigint(20) DEFAULT 0 COMMENT '城市ID',
  `area_id` bigint(20) DEFAULT 0 COMMENT '地区ID',
  `address` varchar(100) DEFAULT '' COMMENT '详细地址',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号',
    `tel_no` varchar(20) DEFAULT '' COMMENT '电话号码',
  `bg_image` varchar(200) DEFAULT '' COMMENT '背景图片',
  `description` varchar(200) DEFAULT '' COMMENT '个人描述',
  `visit_count` int(11) DEFAULT 0 COMMENT '访问次数',
  `last_time` bigint(20) DEFAULT 0 COMMENT '最后登录时间',
  `last_ip` varchar(40) DEFAULT '' COMMENT '最后登录IP',
  `role` int(11) DEFAULT 1 COMMENT '角色',
  `current_level` int(11) DEFAULT 1 COMMENT '当前等级',
  `level_expiration_time` bigint(20) DEFAULT 0 COMMENT '等级过期时间',
  `level_buy_type` int(11) DEFAULT 0 COMMENT '等级购买类型',
  `pid` bigint(20) DEFAULT 0 COMMENT '上级ID',
  `level` int(11) DEFAULT 1 COMMENT '层级',
  `tree` varchar(2000) DEFAULT '' COMMENT '层级树',
  `promoter_code` varchar(50) DEFAULT '' COMMENT '推广员代码',
  `certification_type` int(11) DEFAULT 0 COMMENT '认证类型',
  `certification_pic` varchar(150) DEFAULT '' COMMENT '认证图片',
  `status` int(11) DEFAULT 1 COMMENT '状态：-1删除，0禁用，1启用',
  `created_at` bigint(20) DEFAULT 0 COMMENT '创建时间',
  `updated_at` bigint(20) DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_email` (`email`),
  KEY `idx_merchant_store` (`merchant_id`, `store_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员主表';

-- =====================================================
-- 会员账户表 (member_account)
-- =====================================================
CREATE TABLE IF NOT EXISTS `member_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` bigint(20) DEFAULT 0 COMMENT '商户ID',
  `store_id` bigint(20) DEFAULT 0 COMMENT '店铺ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `member_type` int(11) DEFAULT 1 COMMENT '会员类型',
  `user_money` decimal(10,2) DEFAULT 0.00 COMMENT '用户余额',
  `accumulate_money` decimal(10,2) DEFAULT 0.00 COMMENT '累计充值金额',
  `give_money` decimal(10,2) DEFAULT 0.00 COMMENT '赠送金额',
  `consume_money` decimal(10,2) DEFAULT 0.00 COMMENT '消费金额',
  `frozen_money` decimal(10,2) DEFAULT 0.00 COMMENT '冻结金额',
  `user_integral` bigint(20) DEFAULT 0 COMMENT '用户积分',
  `accumulate_integral` bigint(20) DEFAULT 0 COMMENT '累计积分',
  `give_integral` bigint(20) DEFAULT 0 COMMENT '赠送积分',
  `consume_integral` decimal(10,2) DEFAULT 0.00 COMMENT '消费积分',
  `frozen_integral` bigint(20) DEFAULT 0 COMMENT '冻结积分',
  `user_growth` bigint(20) DEFAULT 0 COMMENT '用户成长值',
  `accumulate_growth` bigint(20) DEFAULT 0 COMMENT '累计成长值',
  `consume_growth` bigint(20) DEFAULT 0 COMMENT '消费成长值',
  `frozen_growth` bigint(20) DEFAULT 0 COMMENT '冻结成长值',
  `economize_money` decimal(10,2) DEFAULT 0.00 COMMENT '节省金额',
  `accumulate_drawn_money` decimal(10,2) DEFAULT 0.00 COMMENT '累计提现金额',
  `status` int(11) DEFAULT 1 COMMENT '状态：-1删除，0禁用，1启用',
  `created_at` bigint(20) DEFAULT 0 COMMENT '创建时间',
  `updated_at` bigint(20) DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_member_id` (`member_id`),
  KEY `idx_merchant_store` (`merchant_id`, `store_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员账户表';

-- =====================================================
-- 插入测试数据
-- =====================================================

-- 插入测试会员数据
INSERT INTO `member` (
  `username`, `password_hash`, `nickname`, `mobile`, `email`, 
  `status`, `created_at`, `updated_at`
) VALUES 
(
  'testuser', 
  'e10adc3949ba59abbe56e057f20f883e', -- 密码: 123456 (MD5)
  '测试用户', 
  '***********', 
  '<EMAIL>',
  1, 
  UNIX_TIMESTAMP(), 
  UNIX_TIMESTAMP()
),
(
  'admin', 
  'e10adc3949ba59abbe56e057f20f883e', -- 密码: 123456 (MD5)
  '管理员', 
  '***********', 
  '<EMAIL>',
  1, 
  UNIX_TIMESTAMP(), 
  UNIX_TIMESTAMP()
);

-- 为测试会员创建账户记录
INSERT INTO `member_account` (
  `member_id`, `member_type`, `user_money`, `user_integral`, 
  `status`, `created_at`, `updated_at`
) VALUES 
(1, 1, 0.00, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 2, 0.00, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- =====================================================
-- 完成初始化
-- =====================================================
COMMIT;