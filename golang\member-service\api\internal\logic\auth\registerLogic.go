package auth

import (
	"context"
	"fmt"
	memberpb "member/member"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户注册
func NewRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterLogic {
	return &RegisterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RegisterLogic) Register(req *types.RegisterReq) (resp *types.RegisterResp, err error) {

	// 调用RPC服务
	rpcResp, err := l.svcCtx.MemberRpc.Register(l.ctx, &memberpb.RegisterReq{
		Username:   req.Username,
		Password:   req.Password,
		Mobile:     req.Mobile,
		Email:      req.Email,
		Nickname:   req.Nickname,
		Source:     req.Source,
		MerchantId: 0,
		StoreId:    0,
	})

	if err != nil {
		return nil, fmt.Errorf("调用rpc的Register方法失败: %v", err)
	}

	// 映射为API响应结构
	return &types.RegisterResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
		Data: types.RegisterData{
			MemberId: rpcResp.MemberId,
		},
	}, nil
}
