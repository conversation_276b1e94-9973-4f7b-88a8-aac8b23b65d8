package admin

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuditCancelApplicationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 审核注销申请
func NewAuditCancelApplicationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuditCancelApplicationLogic {
	return &AuditCancelApplicationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AuditCancelApplicationLogic) AuditCancelApplication(req *types.AuditCancelReq) (resp *types.CommonResp, err error) {
	// todo: add your logic here and delete this line

	return
}
