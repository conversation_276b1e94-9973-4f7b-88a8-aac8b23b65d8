package logic

import (
	"context"
	"fmt"

	"member/internal/svc"
	"member/member"
	"member/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type SubmitCancelApplicationLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSubmitCancelApplicationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubmitCancelApplicationLogic {
	return &SubmitCancelApplicationLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 注销申请
func (l *SubmitCancelApplicationLogic) SubmitCancelApplication(in *member.SubmitCancelApplicationReq) (*member.CommonResp, error) {
	// 参数验证
	if in.MemberId <= 0 {
		return &member.CommonResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	if len(in.Content) == 0 {
		return &member.CommonResp{
			Code:    400,
			Message: "申请内容不能为空",
		}, nil
	}

	if len(in.Content) > 500 {
		return &member.CommonResp{
			Code:    400,
			Message: "申请内容不能超过500字符",
		}, nil
	}

	// 检查用户是否存在
	memberData, err := l.svcCtx.MemberModel.FindByID(in.MemberId)
	if err != nil {
		if err.Error() == fmt.Sprintf("用户id=%d不存在", in.MemberId) {
			return &member.CommonResp{
				Code:    404,
				Message: "用户不存在",
			}, nil
		}
		logx.Errorf("查询用户信息失败: %v", err)
		return &member.CommonResp{
			Code:    500,
			Message: "查询用户信息失败",
		}, nil
	}

	// 检查用户状态
	if memberData.Status == -1 {
		return &member.CommonResp{
			Code:    400,
			Message: "用户已被删除，无法申请注销",
		}, nil
	}

	// 检查是否已有待审核的注销申请
	existingCancel, err := l.svcCtx.MemberCancelModel.FindByMemberID(in.MemberId)
	if err == nil && existingCancel != nil {
		// 如果有待审核的申请
		if existingCancel.AuditStatus == 0 {
			return &member.CommonResp{
				Code:    400,
				Message: "您已有待审核的注销申请，请勿重复提交",
			}, nil
		}
		// 如果之前的申请已通过
		if existingCancel.AuditStatus == 1 {
			return &member.CommonResp{
				Code:    400,
				Message: "您的注销申请已通过，账户将被注销",
			}, nil
		}
	}

	// 创建注销申请
	cancelData := &model.MemberCancel{
		MerchantID:   memberData.MerchantID,
		StoreID:      memberData.StoreID,
		MemberID:     in.MemberId,
		Content:      in.Content,
		AuditStatus:  0, // 0: 待审核
		AuditTime:    0,
		RefusalCause: "",
		IsAddon:      0,
		AddonName:    "",
		Status:       1, // 1: 正常
	}

	err = l.svcCtx.MemberCancelModel.Create(cancelData)
	if err != nil {
		logx.Errorf("创建注销申请失败: %v", err)
		return &member.CommonResp{
			Code:    500,
			Message: "创建注销申请失败",
		}, nil
	}

	return &member.CommonResp{
		Code:    200,
		Message: "注销申请提交成功，请等待审核",
	}, nil
}
