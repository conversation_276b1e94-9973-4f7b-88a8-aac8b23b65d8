﻿package logic

import (
	"context"
	"dict_rpc/dict"
	"errors"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"
	"dict_category_rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type UpdateDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictCategoryLogic {
	return &UpdateDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典项分类
func (l *UpdateDictCategoryLogic) UpdateDictCategory(in *dict_category.UpdateDictCategoryReq) (*dict_category.UpdateDictCategoryResp, error) {
	// todo: add your logic here and delete this line
	// 参数验证
	if in.DictId <= 0 {
		return &dict_category.UpdateDictCategoryResp{
			Success: false,
			Message: "字典ID不能为空",
		}, nil
	}

	if in.Name == "" {
		return &dict_category.UpdateDictCategoryResp{
			Success: false,
			Message: "字典分类名称不能为空",
		}, nil
	}

	// 验证字典是否存在
	_, err := l.svcCtx.DictRpc.GetDict(l.ctx, &dict.GetDictReq{
		Id: in.DictId,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dict_category.UpdateDictCategoryResp{
				Success: false,
				Message: "字典不存在",
			}, nil
		}
		return &dict_category.UpdateDictCategoryResp{
			Success: false,
			Message: "查询字典失败",
		}, nil
	}

	// 更新字典项分类
	err = l.svcCtx.DictCategoryModel.Update(&model.DictCategory{
		DictID: in.DictId,
		Name:   in.Name,
		Status: in.Status,
	})

	if err != nil {
		return &dict_category.UpdateDictCategoryResp{
			Success: false,
			Message: "更新字典项分类失败",
		}, nil
	}

	return &dict_category.UpdateDictCategoryResp{
		Success: true,
		Message: "更新字典项分类成功",
	}, nil
}

