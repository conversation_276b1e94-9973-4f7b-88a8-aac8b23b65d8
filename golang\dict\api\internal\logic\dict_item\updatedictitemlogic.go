﻿package dict_item

import (
	"context"
	"dict_item_rpc/dict_item"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新字典项
func NewUpdateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictItemLogic {
	return &UpdateDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDictItemLogic) UpdateDictItem(req *types.UpdateDictItemReq) (resp *types.UpdateDictItemResp, err error) {
	// 调用dict_item RPC服务更新字典项

	// 调用dict_item服务
	respRpc, err := l.svcCtx.DictItemRpc.UpdateDictItem(l.ctx, &dict_item.UpdateDictItemReq{
		Id:         req.Id,
		DictId:     req.DictId,
		CategoryId: req.CategoryId,
		Code:       req.Code,
		Name:       req.Name,
		Status:     req.Status,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_item服务的UpdateDictItem方法失败: %v", err)
	}
	return &types.UpdateDictItemResp{
		Success: respRpc.Success,
		Message: "更新成功",
	}, nil
}
