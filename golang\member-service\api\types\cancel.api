syntax = "v1"

// ===== 用户注销申请结构 =====
type MemberCancel {
    Id           int64  `json:"id"`
    MerchantId   int64  `json:"merchant_id"`
    StoreId      int64  `json:"store_id"`
    MemberId     int64  `json:"member_id"`
    Content      string `json:"content"`       // 申请内容
    AuditStatus  int    `json:"audit_status"`  // 审核状态 0:申请 1:通过 -1:失败
    AuditTime    int64  `json:"audit_time"`    // 审核时间
    RefusalCause string `json:"refusal_cause"` // 拒绝原因
    IsAddon      int    `json:"is_addon"`
    AddonName    string `json:"addon_name"`
    Status       int    `json:"status"`
    CreatedAt    int64  `json:"created_at"`
    UpdatedAt    int64  `json:"updated_at"`
}

// ===== 审核状态枚举 =====
type CancelAuditStatus {
    Pending  int `json:"pending"`  // 0:申请中
    Approved int `json:"approved"` // 1:已通过
    Rejected int `json:"rejected"` // -1:已拒绝
}

// ===== 提交注销申请 =====
type SubmitCancelReq {
    Content string `json:"content"` // 申请原因
}

// ===== 获取注销申请状态 =====
type GetCancelStatusReq {
    MemberId int64 `form:"member_id,optional"`
}

type CancelStatusData {
    HasApplication bool   `json:"has_application"` // 是否有申请记录
    AuditStatus    int    `json:"audit_status"`    // 审核状态
    StatusText     string `json:"status_text"`     // 状态描述
    Content        string `json:"content"`         // 申请内容
    SubmitTime     int64  `json:"submit_time"`     // 提交时间
    AuditTime      int64  `json:"audit_time"`      // 审核时间
    RefusalCause   string `json:"refusal_cause"`   // 拒绝原因
}

type GetCancelStatusResp {
    Code    int              `json:"code"`
    Message string           `json:"message"`
    Data    CancelStatusData `json:"data"`
}

// ===== 撤销注销申请 =====
type WithdrawCancelReq {
    Reason string `json:"reason,optional"` // 撤销原因
}

// ===== 获取待审核注销申请列表 =====
type GetPendingCancelListReq {
    BasePageReq
    MemberId  int64  `form:"member_id,optional"`
    Username  string `form:"username,optional"`
    StartTime int64  `form:"start_time,optional"`
    EndTime   int64  `form:"end_time,optional"`
}

type PendingCancelItem {
    Id           int64  `json:"id"`
    MemberId     int64  `json:"member_id"`
    Username     string `json:"username"`
    Nickname     string `json:"nickname"`
    Mobile       string `json:"mobile"`
    Content      string `json:"content"`
    SubmitTime   int64  `json:"submit_time"`
    AuditStatus  int    `json:"audit_status"`
    RefusalCause string `json:"refusal_cause"`
}

type PendingCancelListData {
    List     []PendingCancelItem `json:"list"`
    PageInfo PageInfo            `json:"page_info"`
}

type GetPendingCancelListResp {
    Code    int                   `json:"code"`
    Message string                `json:"message"`
    Data    PendingCancelListData `json:"data"`
}

// ===== 审核注销申请 =====
type AuditCancelReq {
    Id           int64  `json:"id"`
    AuditStatus  int    `json:"audit_status"`            // 1:通过 -1:拒绝
    RefusalCause string `json:"refusal_cause,optional"`  // 拒绝原因
    Remark       string `json:"remark,optional"`         // 审核备注
}

// ===== 批量审核注销申请 =====
type BatchAuditCancelReq {
    Ids          []int64 `json:"ids"`
    AuditStatus  int     `json:"audit_status"`            // 1:通过 -1:拒绝
    RefusalCause string  `json:"refusal_cause,optional"`  // 拒绝原因
    Remark       string  `json:"remark,optional"`         // 审核备注
}

// ===== 获取注销申请详情 =====
type GetCancelDetailReq {
    Id int64 `path:"id"`
}

type CancelDetail {
    Id           int64  `json:"id"`
    MemberId     int64  `json:"member_id"`
    Username     string `json:"username"`
    Nickname     string `json:"nickname"`
    Mobile       string `json:"mobile"`
    Email        string `json:"email"`
    RegisterTime int64  `json:"register_time"`
    LastLoginTime int64  `json:"last_login_time"`
    Content      string `json:"content"`
    SubmitTime   int64  `json:"submit_time"`
    AuditStatus  int    `json:"audit_status"`
    AuditTime    int64  `json:"audit_time"`
    RefusalCause string `json:"refusal_cause"`
    // 账户信息
    Balance    float64 `json:"balance"`
    Integral   int64   `json:"integral"`
    Growth     int64   `json:"growth"`
    // 业务数据统计
    OrderCount   int64 `json:"order_count"`
    ArticleCount int64 `json:"article_count"`
    CommentCount int64 `json:"comment_count"`
}

type GetCancelDetailResp {
    Code    int          `json:"code"`
    Message string       `json:"message"`
    Data    CancelDetail `json:"data"`
} 