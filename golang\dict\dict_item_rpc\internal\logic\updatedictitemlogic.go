﻿package logic

import (
	"context"
	"dict_category_rpc/dict_category"
	"dict_rpc/dict"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/svc"
	"dict_item_rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictItemLogic {
	return &UpdateDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典项项
func (l *UpdateDictItemLogic) UpdateDictItem(in *dict_item.UpdateDictItemReq) (*dict_item.UpdateDictItemResp, error) {
	// 更新字典项业务逻辑

	// 参数验证
	if in.Id <= 0 {
		return &dict_item.UpdateDictItemResp{
			Success: false,
			Message: "字典项ID不能为空",
		}, nil
	}
	if in.DictId <= 0 {
		return &dict_item.UpdateDictItemResp{
			Success: false,
			Message: "字典ID不能为空",
		}, nil
	}
	if in.CategoryId <= 0 {
		return &dict_item.UpdateDictItemResp{
			Success: false,
			Message: "字典分类ID不能为空",
		}, nil
	}

	// 检查字典是否存在
	_, err := l.svcCtx.DictRpc.GetDict(l.ctx, &dict.GetDictReq{
		Id: in.DictId,
	})
	if err != nil {
		return &dict_item.UpdateDictItemResp{
			Success: false,
			Message: "字典不存在,请检查字典ID是否正确",
		}, nil
	}
	// 检查字典分类是否存在
	_, err = l.svcCtx.DictCategoryRpc.GetDictCategory(l.ctx, &dict_category.GetDictCategoryReq{
		Id: in.CategoryId,
	})
	if err != nil {
		return &dict_item.UpdateDictItemResp{
			Success: false,
			Message: "字典分类不存在,请检查字典分类ID是否正确",
		}, nil
	}
	// 更新字典项
	err = l.svcCtx.DictItemModel.Update(&model.DictItem{
		ID:         in.Id,
		DictID:     in.DictId,
		CategoryID: in.CategoryId,
		Code:       in.Code,
		Name:       in.Name,
		Status:     in.Status,
	})

	if err != nil {
		return &dict_item.UpdateDictItemResp{
			Success: false,
			Message: "更新字典项失败",
		}, nil
	}

	return &dict_item.UpdateDictItemResp{
		Success: true,
		Message: "更新字典项成功",
	}, nil
}
