// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	dict "api/internal/handler/dict"
	dict_category "api/internal/handler/dict_category"
	dict_item "api/internal/handler/dict_item"
	"api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 创建字典
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: dict.CreateDictHandler(serverCtx),
			},
			{
				// 删除字典
				Method:  http.MethodDelete,
				Path:    "/delete/:id",
				Handler: dict.DeleteDictHandler(serverCtx),
			},
			{
				// 获取字典详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: dict.GetDictHandler(serverCtx),
			},
			{
				// 字典列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: dict.ListDictHandler(serverCtx),
			},
			{
				// 更新字典
				Method:  http.MethodPut,
				Path:    "/update/:id",
				Handler: dict.UpdateDictHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/dict"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建字典分类
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: dict_category.CreateDictCategoryHandler(serverCtx),
			},
			{
				// 删除字典分类
				Method:  http.MethodDelete,
				Path:    "/delete/:id",
				Handler: dict_category.DeleteDictCategoryHandler(serverCtx),
			},
			{
				// 获取字典分类详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: dict_category.GetDictCategoryHandler(serverCtx),
			},
			{
				// 字典分类列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: dict_category.ListDictCategoryHandler(serverCtx),
			},
			{
				// 更新字典分类
				Method:  http.MethodPut,
				Path:    "/update/:id",
				Handler: dict_category.UpdateDictCategoryHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/dict/category"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建字典项
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: dict_item.CreateDictItemHandler(serverCtx),
			},
			{
				// 删除字典项
				Method:  http.MethodDelete,
				Path:    "/delete/:id",
				Handler: dict_item.DeleteDictItemHandler(serverCtx),
			},
			{
				// 获取字典项详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: dict_item.GetDictItemHandler(serverCtx),
			},
			{
				// 字典项列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: dict_item.ListDictItemHandler(serverCtx),
			},
			{
				// 更新字典项
				Method:  http.MethodPut,
				Path:    "/update/:id",
				Handler: dict_item.UpdateDictItemHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/dict/item"),
	)
}
