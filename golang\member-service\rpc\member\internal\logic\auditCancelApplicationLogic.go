package logic

import (
	"context"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuditCancelApplicationLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAuditCancelApplicationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuditCancelApplicationLogic {
	return &AuditCancelApplicationLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *AuditCancelApplicationLogic) AuditCancelApplication(in *member.AuditCancelApplicationReq) (*member.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &member.CommonResp{}, nil
}
