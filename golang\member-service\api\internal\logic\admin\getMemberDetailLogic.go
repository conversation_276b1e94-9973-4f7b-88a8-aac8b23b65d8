package admin

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户详情
func NewGetMemberDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberDetailLogic {
	return &GetMemberDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMemberDetailLogic) GetMemberDetail(req *types.GetMemberDetailReq) (resp *types.GetMemberDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
