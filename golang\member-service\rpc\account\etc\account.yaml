Name: account.rpc
ListenOn: 0.0.0.0:9001
Etcd:
  Hosts:
    - etcd:2379
  Key: account.rpc

# 数据库连接字符串
DataSource: root:123456@tcp(mysql:3306)/mp_db?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

# 配置日志
Log:
  Mode: console
  Level: info

# 连接到docker里面的mysql数据库
MySQL:
  DataSource: root:123456@tcp(mysql:3306)/mp_db?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

# 配置redis
CacheRedis:
  - Host: redis:6379
    Pass:
    Type: node

# JWT配置
JwtAuth:
  AccessSecret: "*********"
  AccessExpire: 3600
