﻿package dict

import (
	"api/internal/svc"
	"api/internal/types"
	"context"
	"dict_rpc/dict"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 字典列表
func NewListDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictLogic {
	return &ListDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDictLogic) ListDict(req *types.ListDictReq) (resp *types.ListDictResp, err error) {
	// todo: add your logic here and delete this line

	// 调用dict_rpc服务
	respRpc, err := l.svcCtx.DictRpc.ListDict(l.ctx, &dict.ListDictReq{
		Page:     req.Page,
		PageSize: req.PageSize,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_rpc服务的ListDict方法失败: %v", err)
	}

	var dataList []types.Dict

	for _, v := range respRpc.List {
		dataList = append(dataList, types.Dict{
			Id:          v.Id,
			Code:        v.Code,
			Name:        v.Name,
			Remark:      v.Remark,
			Status:      v.Status,
			CreatedTime: v.CreatedTime,
			UpdatedTime: v.UpdatedTime,
		})
	}

	return &types.ListDictResp{
		Total:   respRpc.Total,
		List:    dataList,
		Message: "获取字典列表成功",
	}, nil
}

