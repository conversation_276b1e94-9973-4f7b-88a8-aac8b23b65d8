# Member Service API 接口文档

## 概述

Member Service 是一个基于 Go-Zero 框架开发的用户管理微服务，提供用户注册、登录、认证、信息管理等功能。

**Base URL**: `http://localhost:8005`

**认证方式**: <PERSON><PERSON> (JWT)

---

## 🔐 认证相关接口 (无需JWT)

### 1. 用户注册

**接口**: `POST /api/v1/auth/register`

**描述**: 用户注册新账户

**请求参数**:
```json
{
  "username": "string",      // 用户名 (必填)
  "password": "string",      // 密码 (必填)
  "email": "string",         // 邮箱 (必填)
  "mobile": "string",        // 手机号 (必填)
  "verify_code": "string"    // 验证码 (必填)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "member_id": 1
  }
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "该账号已存在,请登录",
  "data": {
    "member_id": 0
  }
}
```

---

### 2. 用户登录

**接口**: `POST /api/v1/auth/login`

**描述**: 用户登录获取访问令牌

**请求参数**:
```json
{
  "username": "string",      // 用户名 (必填)
  "password": "string",      // 密码 (必填)
  "login_type": "string"     // 登录类型: "username" | "mobile" | "email" (必填)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "user_info": {
      "id": 1,
      "username": "testuser",
      "type": 1,
      "merchant_id": 0,
      "store_id": 0,
      "realname": "",
      "nickname": "",
      "head_portrait": "",
      "gender": 0,
      "qq": "",
      "email": "",
      "birthday": "",
      "province_id": 0,
      "city_id": 0,
      "area_id": 0,
      "address": "",
      "mobile": "",
      "tel_no": "",
      "bg_image": "",
      "description": "",
      "visit_count": 0,
      "last_time": 0,
      "last_ip": "",
      "role": 0,
      "current_level": 0,
      "level_expiration_time": 0,
      "level_buy_type": 0,
      "pid": 0,
      "level": 0,
      "tree": "",
      "promoter_code": "",
      "certification_type": 0,
      "source": "",
      "status": 0,
      "created_at": 0,
      "updated_at": 0,
      "region_id": 0
    }
  }
}
```

---

### 3. 重置密码

**接口**: `POST /api/v1/auth/reset-password`

**描述**: 通过手机号重置用户密码

**请求参数**:
```json
{
  "mobile": "string",        // 手机号 (必填)
  "new_password": "string",  // 新密码 (必填)
  "verify_code": "string"    // 验证码 (必填)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "修改密码成功"
}
```

---

### 4. 刷新访问令牌

**接口**: `POST /api/v1/auth/refresh`

**描述**: 使用刷新令牌获取新的访问令牌

**请求参数**:
```json
{
  "refresh_token": "string"  // 刷新令牌 (必填)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "刷新token成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600
  }
}
```

---

## 👤 用户管理接口 (需要JWT)

### 5. 获取用户信息

**接口**: `GET /api/v1/member/info`

**描述**: 获取当前登录用户的详细信息

**请求头**:
```
Authorization: Bearer {access_token}
```

**请求参数** (Query):
```
member_id: number  // 可选，管理员查看其他用户时使用
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "merchant_id": 0,
    "store_id": 0,
    "username": "testuser",
    "type": 1,
    "realname": "张三",
    "nickname": "小张",
    "head_portrait": "https://example.com/avatar.jpg",
    "gender": 1,
    "qq": "123456789",
    "email": "<EMAIL>",
    "birthday": "1990-01-01",
    "province_id": 110000,
    "city_id": 110100,
    "area_id": 110101,
    "address": "北京市朝阳区",
    "mobile": "***********",
    "tel_no": "010-12345678",
    "bg_image": "https://example.com/bg.jpg",
    "description": "用户描述",
    "visit_count": 100,
    "last_time": **********,
    "last_ip": "***********",
    "role": 0,
    "current_level": 1,
    "level_expiration_time": 1672531200,
    "level_buy_type": 0,
    "pid": 0,
    "level": 1,
    "tree": "0,1",
    "promoter_code": "ABC123",
    "certification_type": 1,
    "source": "web",
    "status": 1,
    "created_at": **********,
    "updated_at": **********,
    "region_id": 110101
  }
}
```

---

### 6. 更新用户信息

**接口**: `PUT /api/v1/member/info`

**描述**: 更新用户个人信息

**请求头**:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "realname": "string",          // 真实姓名 (可选)
  "nickname": "string",          // 昵称 (可选)
  "head_portrait": "string",     // 头像URL (可选)
  "gender": 0,                   // 性别: 0-未知, 1-男, 2-女 (可选)
  "qq": "string",                // QQ号 (可选)
  "email": "string",             // 邮箱 (可选)
  "birthday": "string",          // 生日 YYYY-MM-DD (可选)
  "province_id": 0,              // 省份ID (可选)
  "city_id": 0,                  // 城市ID (可选)
  "area_id": 0,                  // 区域ID (可选)
  "address": "string",           // 详细地址 (可选)
  "mobile": "string",            // 手机号 (可选)
  "tel_no": "string",            // 固定电话 (可选)
  "description": "string"        // 个人描述 (可选)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新用户信息成功"
}
```

---

### 7. 用户登出

**接口**: `POST /api/v1/member/logout`

**描述**: 用户登出，使当前令牌失效

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登出成功"
}
```

---

## 💰 账户管理接口 (需要JWT)

### 8. 获取账户信息

**接口**: `GET /api/v1/member/account`

**描述**: 获取用户账户余额和积分信息

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取账户信息成功",
  "data": {
    "member_id": 1,
    "balance": "1000.00",
    "frozen_balance": "0.00",
    "integral": 500,
    "frozen_integral": 0,
    "total_recharge": "2000.00",
    "total_consume": "1000.00",
    "created_at": **********,
    "updated_at": **********
  }
}
```

---

### 9. 账户充值

**接口**: `POST /api/v1/member/account/recharge`

**描述**: 用户账户充值

**请求头**:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "amount": "100.00",        // 充值金额 (必填)
  "payment_method": "string", // 支付方式 (必填)
  "remark": "string"         // 备注 (可选)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "充值成功",
  "data": {
    "order_id": "R20240101123456",
    "amount": "100.00",
    "new_balance": "1100.00"
  }
}
```

---

### 10. 账户提现

**接口**: `POST /api/v1/member/account/withdraw`

**描述**: 用户账户提现申请

**请求头**:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "amount": "50.00",         // 提现金额 (必填)
  "withdraw_type": "string", // 提现方式 (必填)
  "account_info": "string",  // 收款账户信息 (必填)
  "remark": "string"         // 备注 (可选)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "提现申请提交成功",
  "data": {
    "withdraw_id": "W20240101123456",
    "amount": "50.00",
    "fee": "2.00",
    "actual_amount": "48.00",
    "status": "pending"
  }
}
```

---

## 🔐 实名认证接口 (需要JWT)

### 11. 获取实名认证信息

**接口**: `GET /api/v1/member/certification`

**描述**: 获取用户实名认证状态和信息

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取认证信息成功",
  "data": {
    "member_id": 1,
    "certification_type": 1,
    "real_name": "张三",
    "id_card": "110101199001011234",
    "status": 1,
    "audit_time": **********,
    "audit_remark": "认证通过",
    "created_at": **********,
    "updated_at": **********
  }
}
```

---

### 12. 提交实名认证

**接口**: `POST /api/v1/member/certification`

**描述**: 提交实名认证申请

**请求头**:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "certification_type": 1,      // 认证类型: 1-身份证 (必填)
  "real_name": "string",        // 真实姓名 (必填)
  "id_card": "string",          // 身份证号 (必填)
  "id_card_front": "string",    // 身份证正面照片URL (必填)
  "id_card_back": "string",     // 身份证反面照片URL (必填)
  "hand_held": "string"         // 手持身份证照片URL (可选)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "认证申请提交成功",
  "data": {
    "certification_id": 1,
    "status": "pending"
  }
}
```

---

## 📊 统计信息接口 (需要JWT)

### 13. 获取用户统计信息

**接口**: `GET /api/v1/member/stat`

**描述**: 获取用户相关的统计数据

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取统计信息成功",
  "data": {
    "member_id": 1,
    "total_orders": 50,
    "total_amount": "5000.00",
    "total_integral": 2500,
    "invite_count": 10,
    "level_info": {
      "current_level": 2,
      "next_level": 3,
      "upgrade_need": "1000.00"
    },
    "created_at": **********,
    "updated_at": **********
  }
}
```

---

## 🚫 注销管理接口 (需要JWT)

### 14. 提交注销申请

**接口**: `POST /api/v1/member/cancel`

**描述**: 用户提交账户注销申请

**请求头**:
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "reason": "string",        // 注销原因 (必填)
  "password": "string"       // 当前密码确认 (必填)
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "注销申请提交成功",
  "data": {
    "cancel_id": 1,
    "status": "pending",
    "apply_time": **********
  }
}
```

---

### 15. 获取注销状态

**接口**: `GET /api/v1/member/cancel/status`

**描述**: 查询用户账户注销申请状态

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取注销状态成功",
  "data": {
    "cancel_id": 1,
    "status": "pending",
    "reason": "不再使用",
    "apply_time": **********,
    "audit_time": 0,
    "audit_remark": ""
  }
}
```

---

## 🔧 管理员接口 (需要管理员权限)

### 16. 获取用户列表

**接口**: `GET /api/v1/admin/members`

**描述**: 管理员获取用户列表

**请求头**:
```
Authorization: Bearer {admin_access_token}
```

**请求参数** (Query):
```
page: number       // 页码，默认1
page_size: number  // 每页数量，默认10
keyword: string    // 搜索关键词 (可选)
status: number     // 用户状态筛选 (可选)
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 10,
    "list": [
      {
        "id": 1,
        "username": "testuser",
        "realname": "张三",
        "mobile": "***********",
        "email": "<EMAIL>",
        "status": 1,
        "created_at": **********,
        "last_time": **********
      }
    ]
  }
}
```

---

### 17. 获取用户详情

**接口**: `GET /api/v1/admin/members/{id}`

**描述**: 管理员获取指定用户的详细信息

**请求头**:
```
Authorization: Bearer {admin_access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取用户详情成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "realname": "张三",
    "mobile": "***********",
    "email": "<EMAIL>",
    "status": 1,
    "account_info": {
      "balance": "1000.00",
      "integral": 500
    },
    "certification_info": {
      "status": 1,
      "real_name": "张三"
    },
    "created_at": **********,
    "updated_at": **********
  }
}
```

---

## 📋 数据结构说明

### User 用户信息结构
```json
{
  "id": 1,                          // 用户ID
  "merchant_id": 0,                 // 商户ID
  "store_id": 0,                    // 店铺ID
  "username": "string",             // 用户名
  "type": 1,                        // 用户类型: 1-普通用户, 2-管理员
  "realname": "string",             // 真实姓名
  "nickname": "string",             // 昵称
  "head_portrait": "string",        // 头像URL
  "gender": 0,                      // 性别: 0-未知, 1-男, 2-女
  "qq": "string",                   // QQ号
  "email": "string",                // 邮箱
  "birthday": "string",             // 生日
  "province_id": 0,                 // 省份ID
  "city_id": 0,                     // 城市ID
  "area_id": 0,                     // 区域ID
  "address": "string",              // 详细地址
  "mobile": "string",               // 手机号
  "tel_no": "string",               // 固定电话
  "bg_image": "string",             // 背景图片
  "description": "string",          // 个人描述
  "visit_count": 0,                 // 访问次数
  "last_time": 0,                   // 最后登录时间
  "last_ip": "string",              // 最后登录IP
  "role": 0,                        // 角色
  "current_level": 0,               // 当前等级
  "level_expiration_time": 0,       // 等级过期时间
  "level_buy_type": 0,              // 等级购买类型
  "pid": 0,                         // 父级ID
  "level": 0,                       // 等级
  "tree": "string",                 // 层级关系
  "promoter_code": "string",        // 推广码
  "certification_type": 0,          // 认证类型
  "source": "string",               // 注册来源
  "status": 1,                      // 状态: 0-禁用, 1-正常
  "created_at": 0,                  // 创建时间
  "updated_at": 0,                  // 更新时间
  "region_id": 0                    // 地区ID
}
```

---

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 🎯 下一步实现建议

基于当前实现状态，建议按以下优先级顺序实现剩余接口：

### 🥇 **高优先级** (核心用户功能)
1. **更新用户信息** `PUT /api/v1/member/info` - 用户资料管理基础功能
2. **获取账户信息** `GET /api/v1/member/account` - 查看余额和积分
3. **用户登出** `POST /api/v1/member/logout` - 安全退出功能

### 🥈 **中优先级** (扩展功能)
4. **获取用户统计信息** `GET /api/v1/member/stat` - 用户数据统计
5. **获取实名认证信息** `GET /api/v1/member/certification` - 认证状态查询
6. **提交实名认证** `POST /api/v1/member/certification` - 实名认证申请

### 🥉 **中低优先级** (财务功能)
7. **账户充值** `POST /api/v1/member/account/recharge` - 资金充值
8. **账户提现** `POST /api/v1/member/account/withdraw` - 资金提现
9. **提交注销申请** `POST /api/v1/member/cancel` - 账户注销
10. **获取注销状态** `GET /api/v1/member/cancel/status` - 注销状态查询

### 🔧 **低优先级** (管理员功能)
11-19. **所有管理员接口** - 后台管理功能，可在核心功能完成后实现

### ⚠️ **当前问题修复**
- **修复JWT json.Number问题** - `GET /api/v1/member/info` 接口的类型转换问题

---

## 📝 备注

1. 所有时间戳均为Unix时间戳（秒）
2. 金额字段统一使用字符串类型，保留2位小数
3. JWT Token有效期为1小时，Refresh Token有效期为7天
4. 管理员接口需要用户类型为2（管理员）的Token
5. 部分接口可能需要额外的业务逻辑实现
6. **实现状态说明**：
   - ✅ **已实现**: 包含完整业务逻辑，可正常使用
   - 📋 **仅生成骨架**: 由goctl生成的基础代码框架，包含`todo: add your logic here`注释
   - 🚫 **未实现**: 完全没有相关代码 