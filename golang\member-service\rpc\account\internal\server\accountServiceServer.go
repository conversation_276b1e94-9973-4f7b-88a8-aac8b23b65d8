// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: account.proto

package server

import (
	"context"

	"account/account"
	"account/internal/logic"
	"account/internal/svc"
)

type AccountServiceServer struct {
	svcCtx *svc.ServiceContext
	account.UnimplementedAccountServiceServer
}

func NewAccountServiceServer(svcCtx *svc.ServiceContext) *AccountServiceServer {
	return &AccountServiceServer{
		svcCtx: svcCtx,
	}
}

// 账户信息管理
func (s *AccountServiceServer) CreateAccount(ctx context.Context, in *account.CreateAccountReq) (*account.CreateAccountResp, error) {
	l := logic.NewCreateAccountLogic(ctx, s.svcCtx)
	return l.CreateAccount(in)
}

func (s *AccountServiceServer) GetAccountInfo(ctx context.Context, in *account.GetAccountInfoReq) (*account.GetAccountInfoResp, error) {
	l := logic.NewGetAccountInfoLogic(ctx, s.svcCtx)
	return l.GetAccountInfo(in)
}

func (s *AccountServiceServer) GetAccountList(ctx context.Context, in *account.GetAccountListReq) (*account.GetAccountListResp, error) {
	l := logic.NewGetAccountListLogic(ctx, s.svcCtx)
	return l.GetAccountList(in)
}

// 余额操作
func (s *AccountServiceServer) GetBalance(ctx context.Context, in *account.GetBalanceReq) (*account.GetBalanceResp, error) {
	l := logic.NewGetBalanceLogic(ctx, s.svcCtx)
	return l.GetBalance(in)
}

func (s *AccountServiceServer) Recharge(ctx context.Context, in *account.RechargeReq) (*account.RechargeResp, error) {
	l := logic.NewRechargeLogic(ctx, s.svcCtx)
	return l.Recharge(in)
}

func (s *AccountServiceServer) Withdraw(ctx context.Context, in *account.WithdrawReq) (*account.WithdrawResp, error) {
	l := logic.NewWithdrawLogic(ctx, s.svcCtx)
	return l.Withdraw(in)
}

func (s *AccountServiceServer) FreezeMoney(ctx context.Context, in *account.FreezeMoneyReq) (*account.CommonResp, error) {
	l := logic.NewFreezeMoneyLogic(ctx, s.svcCtx)
	return l.FreezeMoney(in)
}

func (s *AccountServiceServer) UnfreezeMoney(ctx context.Context, in *account.UnfreezeMoneyReq) (*account.CommonResp, error) {
	l := logic.NewUnfreezeMoneyLogic(ctx, s.svcCtx)
	return l.UnfreezeMoney(in)
}

// 积分操作
func (s *AccountServiceServer) GetIntegralInfo(ctx context.Context, in *account.GetIntegralInfoReq) (*account.GetIntegralInfoResp, error) {
	l := logic.NewGetIntegralInfoLogic(ctx, s.svcCtx)
	return l.GetIntegralInfo(in)
}

func (s *AccountServiceServer) AddIntegral(ctx context.Context, in *account.AddIntegralReq) (*account.CommonResp, error) {
	l := logic.NewAddIntegralLogic(ctx, s.svcCtx)
	return l.AddIntegral(in)
}

func (s *AccountServiceServer) ConsumeIntegral(ctx context.Context, in *account.ConsumeIntegralReq) (*account.CommonResp, error) {
	l := logic.NewConsumeIntegralLogic(ctx, s.svcCtx)
	return l.ConsumeIntegral(in)
}

// 成长值操作
func (s *AccountServiceServer) GetGrowthInfo(ctx context.Context, in *account.GetGrowthInfoReq) (*account.GetGrowthInfoResp, error) {
	l := logic.NewGetGrowthInfoLogic(ctx, s.svcCtx)
	return l.GetGrowthInfo(in)
}

func (s *AccountServiceServer) AddGrowth(ctx context.Context, in *account.AddGrowthReq) (*account.CommonResp, error) {
	l := logic.NewAddGrowthLogic(ctx, s.svcCtx)
	return l.AddGrowth(in)
}

// 账户日志
func (s *AccountServiceServer) AddMoneyLog(ctx context.Context, in *account.AddMoneyLogReq) (*account.CommonResp, error) {
	l := logic.NewAddMoneyLogLogic(ctx, s.svcCtx)
	return l.AddMoneyLog(in)
}

func (s *AccountServiceServer) AddIntegralLog(ctx context.Context, in *account.AddIntegralLogReq) (*account.CommonResp, error) {
	l := logic.NewAddIntegralLogLogic(ctx, s.svcCtx)
	return l.AddIntegralLog(in)
}

func (s *AccountServiceServer) GetAccountLog(ctx context.Context, in *account.GetAccountLogReq) (*account.GetAccountLogResp, error) {
	l := logic.NewGetAccountLogLogic(ctx, s.svcCtx)
	return l.GetAccountLog(in)
}
