﻿package dict_category

import (
	"context"
	"dict_category_rpc/dict_category"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除字典项列表分类
func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictCategoryLogic) DeleteDictCategory(req *types.DeleteDictCategoryReq) (resp *types.DeleteDictCategoryResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_category服务
	respRpc, err := l.svcCtx.DictCategoryRpc.DeleteDictCategory(l.ctx, &dict_category.DeleteDictCategoryReq{
		Id: req.Id,
	})

	if err != nil {
		return nil,fmt.Errorf("调用dict_category服务的DeleteDictCategory方法失败: %v",err)
	}



	return &types.DeleteDictCategoryResp{
		Success: respRpc.Success,
		Message: respRpc.Message,
	},nil
 }

