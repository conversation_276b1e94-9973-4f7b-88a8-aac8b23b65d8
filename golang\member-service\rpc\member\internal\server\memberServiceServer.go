// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: member.proto

package server

import (
	"context"

	"member/internal/logic"
	"member/internal/svc"
	"member/member"
)

type MemberServiceServer struct {
	svcCtx *svc.ServiceContext
	member.UnimplementedMemberServiceServer
}

func NewMemberServiceServer(svcCtx *svc.ServiceContext) *MemberServiceServer {
	return &MemberServiceServer{
		svcCtx: svcCtx,
	}
}

// 用户认证相关
func (s *MemberServiceServer) Register(ctx context.Context, in *member.RegisterReq) (*member.RegisterResp, error) {
	l := logic.NewRegisterLogic(ctx, s.svcCtx)
	return l.Register(in)
}

func (s *MemberServiceServer) Login(ctx context.Context, in *member.LoginReq) (*member.LoginResp, error) {
	l := logic.NewLoginLogic(ctx, s.svcCtx)
	return l.Login(in)
}

func (s *MemberServiceServer) Logout(ctx context.Context, in *member.LogoutReq) (*member.LogoutResp, error) {
	l := logic.NewLogoutLogic(ctx, s.svcCtx)
	return l.Logout(in)
}

func (s *MemberServiceServer) RefreshToken(ctx context.Context, in *member.RefreshTokenReq) (*member.RefreshTokenResp, error) {
	l := logic.NewRefreshTokenLogic(ctx, s.svcCtx)
	return l.RefreshToken(in)
}

func (s *MemberServiceServer) ResetPassword(ctx context.Context, in *member.ResetPasswordReq) (*member.CommonResp, error) {
	l := logic.NewResetPasswordLogic(ctx, s.svcCtx)
	return l.ResetPassword(in)
}

func (s *MemberServiceServer) ValidateToken(ctx context.Context, in *member.ValidateTokenReq) (*member.ValidateTokenResp, error) {
	l := logic.NewValidateTokenLogic(ctx, s.svcCtx)
	return l.ValidateToken(in)
}

// 用户信息管理
func (s *MemberServiceServer) GetMemberInfo(ctx context.Context, in *member.GetMemberInfoReq) (*member.GetMemberInfoResp, error) {
	l := logic.NewGetMemberInfoLogic(ctx, s.svcCtx)
	return l.GetMemberInfo(in)
}

func (s *MemberServiceServer) UpdateMemberInfo(ctx context.Context, in *member.UpdateMemberInfoReq) (*member.CommonResp, error) {
	l := logic.NewUpdateMemberInfoLogic(ctx, s.svcCtx)
	return l.UpdateMemberInfo(in)
}

func (s *MemberServiceServer) GetMemberList(ctx context.Context, in *member.GetMemberListReq) (*member.GetMemberListResp, error) {
	l := logic.NewGetMemberListLogic(ctx, s.svcCtx)
	return l.GetMemberList(in)
}

func (s *MemberServiceServer) DeleteMember(ctx context.Context, in *member.DeleteMemberReq) (*member.CommonResp, error) {
	l := logic.NewDeleteMemberLogic(ctx, s.svcCtx)
	return l.DeleteMember(in)
}

func (s *MemberServiceServer) CreateMember(ctx context.Context, in *member.CreateMemberReq) (*member.CreateMemberResp, error) {
	l := logic.NewCreateMemberLogic(ctx, s.svcCtx)
	return l.CreateMember(in)
}

// 实名认证管理
func (s *MemberServiceServer) GetCertificationInfo(ctx context.Context, in *member.GetCertificationInfoReq) (*member.GetCertificationInfoResp, error) {
	l := logic.NewGetCertificationInfoLogic(ctx, s.svcCtx)
	return l.GetCertificationInfo(in)
}

func (s *MemberServiceServer) SubmitCertification(ctx context.Context, in *member.SubmitCertificationReq) (*member.CommonResp, error) {
	l := logic.NewSubmitCertificationLogic(ctx, s.svcCtx)
	return l.SubmitCertification(in)
}

func (s *MemberServiceServer) AuditCertification(ctx context.Context, in *member.AuditCertificationReq) (*member.CommonResp, error) {
	l := logic.NewAuditCertificationLogic(ctx, s.svcCtx)
	return l.AuditCertification(in)
}

func (s *MemberServiceServer) GetPendingCertificationList(ctx context.Context, in *member.GetPendingCertificationListReq) (*member.GetPendingCertificationListResp, error) {
	l := logic.NewGetPendingCertificationListLogic(ctx, s.svcCtx)
	return l.GetPendingCertificationList(in)
}

// 用户统计
func (s *MemberServiceServer) GetMemberStat(ctx context.Context, in *member.GetMemberStatReq) (*member.GetMemberStatResp, error) {
	l := logic.NewGetMemberStatLogic(ctx, s.svcCtx)
	return l.GetMemberStat(in)
}

func (s *MemberServiceServer) UpdateMemberStat(ctx context.Context, in *member.UpdateMemberStatReq) (*member.CommonResp, error) {
	l := logic.NewUpdateMemberStatLogic(ctx, s.svcCtx)
	return l.UpdateMemberStat(in)
}

// 注销申请
func (s *MemberServiceServer) SubmitCancelApplication(ctx context.Context, in *member.SubmitCancelApplicationReq) (*member.CommonResp, error) {
	l := logic.NewSubmitCancelApplicationLogic(ctx, s.svcCtx)
	return l.SubmitCancelApplication(in)
}

func (s *MemberServiceServer) GetCancelStatus(ctx context.Context, in *member.GetCancelStatusReq) (*member.GetCancelStatusResp, error) {
	l := logic.NewGetCancelStatusLogic(ctx, s.svcCtx)
	return l.GetCancelStatus(in)
}

func (s *MemberServiceServer) AuditCancelApplication(ctx context.Context, in *member.AuditCancelApplicationReq) (*member.CommonResp, error) {
	l := logic.NewAuditCancelApplicationLogic(ctx, s.svcCtx)
	return l.AuditCancelApplication(in)
}

func (s *MemberServiceServer) GetPendingCancelList(ctx context.Context, in *member.GetPendingCancelListReq) (*member.GetPendingCancelListResp, error) {
	l := logic.NewGetPendingCancelListLogic(ctx, s.svcCtx)
	return l.GetPendingCancelList(in)
}
