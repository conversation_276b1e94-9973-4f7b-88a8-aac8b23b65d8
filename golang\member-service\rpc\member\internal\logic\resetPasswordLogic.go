package logic

import (
	"context"
	"member/model"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type ResetPasswordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewResetPasswordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ResetPasswordLogic {
	return &ResetPasswordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ResetPasswordLogic) ResetPassword(in *member.ResetPasswordReq) (*member.CommonResp, error) {

	// 参数校验
	if in.Mobile == "" {
		return &member.CommonResp{
			Code:    400,
			Message: "手机号不能为空",
		}, nil
	}
	// 修改密码
	err := l.svcCtx.MemberModel.UpdatePassword(&model.Member{
		Mobile:       in.Mobile,
		PasswordHash: in.NewPassword,
	}, l.svcCtx.Config.JwtAuth.AccessSecret)

	if err != nil {
		return &member.CommonResp{
			Code:    500,
			Message: "修改密码失败",
		}, nil
	}

	return &member.CommonResp{
		Code:    200,
		Message: "修改密码成功",
	}, nil
}
