// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	admin "api/internal/handler/admin"
	auth "api/internal/handler/auth"
	member "api/internal/handler/member"
	"api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AdminAuth},
			[]rest.Route{
				{
					// 审核注销申请
					Method:  http.MethodPost,
					Path:    "/cancels/:id/audit",
					Handler: admin.AuditCancelApplicationHandler(serverCtx),
				},
				{
					// 获取待审核注销列表
					Method:  http.MethodGet,
					Path:    "/cancels/pending",
					Handler: admin.GetPendingCancelListHandler(serverCtx),
				},
				{
					// 审核实名认证
					Method:  http.MethodPost,
					Path:    "/certifications/:id/audit",
					Handler: admin.AuditCertificationHandler(serverCtx),
				},
				{
					// 获取待审核认证列表
					Method:  http.MethodGet,
					Path:    "/certifications/pending",
					Handler: admin.GetPendingCertificationListHandler(serverCtx),
				},
				{
					// 获取用户列表
					Method:  http.MethodGet,
					Path:    "/members",
					Handler: admin.GetMemberListHandler(serverCtx),
				},
				{
					// 获取用户详情
					Method:  http.MethodGet,
					Path:    "/members/:id",
					Handler: admin.GetMemberDetailHandler(serverCtx),
				},
				{
					// 删除用户
					Method:  http.MethodDelete,
					Path:    "/members/:id",
					Handler: admin.DeleteMemberHandler(serverCtx),
				},
				{
					// 冻结用户资金
					Method:  http.MethodPost,
					Path:    "/members/:id/freeze",
					Handler: admin.FreezeMoneyHandler(serverCtx),
				},
				{
					// 解冻用户资金
					Method:  http.MethodPost,
					Path:    "/members/:id/unfreeze",
					Handler: admin.UnfreezeMoneyHandler(serverCtx),
				},
				{
					// 获取用户统计报表
					Method:  http.MethodGet,
					Path:    "/reports/stats",
					Handler: admin.GetMemberStatReportHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.JwtAuth.AccessSecret),
		rest.WithPrefix("/api/v1/admin"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 用户登录
				Method:  http.MethodPost,
				Path:    "/login",
				Handler: auth.LoginHandler(serverCtx),
			},
			{
				// 刷新token
				Method:  http.MethodPost,
				Path:    "/refresh",
				Handler: auth.RefreshTokenHandler(serverCtx),
			},
			{
				// 用户注册
				Method:  http.MethodPost,
				Path:    "/register",
				Handler: auth.RegisterHandler(serverCtx),
			},
			{
				// 重置密码
				Method:  http.MethodPost,
				Path:    "/reset-password",
				Handler: auth.ResetPasswordHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/auth"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 获取账户信息
				Method:  http.MethodGet,
				Path:    "/account",
				Handler: member.GetAccountInfoHandler(serverCtx),
			},
			{
				// 充值
				Method:  http.MethodPost,
				Path:    "/account/recharge",
				Handler: member.RechargeHandler(serverCtx),
			},
			{
				// 提现
				Method:  http.MethodPost,
				Path:    "/account/withdraw",
				Handler: member.WithdrawHandler(serverCtx),
			},
			{
				// 提交注销申请
				Method:  http.MethodPost,
				Path:    "/cancel",
				Handler: member.SubmitCancelApplicationHandler(serverCtx),
			},
			{
				// 获取注销状态
				Method:  http.MethodGet,
				Path:    "/cancel/status",
				Handler: member.GetCancelStatusHandler(serverCtx),
			},
			{
				// 获取实名认证信息
				Method:  http.MethodGet,
				Path:    "/certification",
				Handler: member.GetCertificationInfoHandler(serverCtx),
			},
			{
				// 提交实名认证
				Method:  http.MethodPost,
				Path:    "/certification",
				Handler: member.SubmitCertificationHandler(serverCtx),
			},
			{
				// 获取用户信息
				Method:  http.MethodGet,
				Path:    "/info",
				Handler: member.GetMemberInfoHandler(serverCtx),
			},
			{
				// 更新用户信息
				Method:  http.MethodPut,
				Path:    "/info",
				Handler: member.UpdateMemberInfoHandler(serverCtx),
			},
			{
				// 用户登出
				Method:  http.MethodPost,
				Path:    "/logout",
				Handler: member.LogoutHandler(serverCtx),
			},
			{
				// 获取统计信息
				Method:  http.MethodGet,
				Path:    "/stat",
				Handler: member.GetMemberStatHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.JwtAuth.AccessSecret),
		rest.WithPrefix("/api/v1/member"),
	)
}
