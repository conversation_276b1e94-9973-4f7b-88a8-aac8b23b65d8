package admin

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuditCertificationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 审核实名认证
func NewAuditCertificationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuditCertificationLogic {
	return &AuditCertificationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AuditCertificationLogic) AuditCertification(req *types.AuditCertificationReq) (resp *types.CommonResp, err error) {
	// todo: add your logic here and delete this line

	return
}
