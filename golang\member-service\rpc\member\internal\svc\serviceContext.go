package svc

import (
	"member/internal/config"
	"member/model"
)

type ServiceContext struct {
	Config                   config.Config
	MemberModel              *model.MemberModel
	MemberAccountModel       *model.MemberAccountModel
	MemberCertificationModel *model.MemberCertificationModel
	MemberStatModel          *model.MemberStatModel
	MemberCancelModel        *model.MemberCancelModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	db := model.NewDb(c.MySQL.DataSource)
	return &ServiceContext{
		Config:                   c,
		MemberModel:              model.NewMemberModel(db),
		MemberAccountModel:       model.NewMemberAccountModel(db),
		MemberCertificationModel: model.NewMemberCertificationModel(db),
		MemberStatModel:          model.NewMemberStatModel(db),
		MemberCancelModel:        model.NewMemberCancelModel(db),
	}
}
