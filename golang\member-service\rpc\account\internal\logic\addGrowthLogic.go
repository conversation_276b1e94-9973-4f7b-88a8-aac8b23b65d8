package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddGrowthLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddGrowthLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddGrowthLogic {
	return &AddGrowthLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *AddGrowthLogic) AddGrowth(in *account.AddGrowthReq) (*account.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &account.CommonResp{}, nil
}
