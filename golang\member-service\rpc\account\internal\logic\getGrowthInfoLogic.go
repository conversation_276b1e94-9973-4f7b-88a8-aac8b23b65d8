package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGrowthInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetGrowthInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGrowthInfoLogic {
	return &GetGrowthInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 成长值操作
func (l *GetGrowthInfoLogic) GetGrowthInfo(in *account.GetGrowthInfoReq) (*account.GetGrowthInfoResp, error) {
	// todo: add your logic here and delete this line

	return &account.GetGrowthInfoResp{}, nil
}
