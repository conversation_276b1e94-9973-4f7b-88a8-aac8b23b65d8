package JWT

import (
	"github.com/golang-jwt/jwt/v4"
	"time"
)

// Token过期时间常量
const (
	DefaultAccessTokenExpire  = 3600          // 1小时
	DefaultRefreshTokenExpire = 7 * 24 * 3600 // 7天
)

// UserInfo JWT中的用户信息结构
type UserInfo struct {
	MemberID   int64  `json:"member_id"`
	Username   string `json:"username"`
	MemberType int32  `json:"type"`
}

// GetJwtToken 生成JWT令牌（兼容旧版本，只包含用户名）
func GetJwtToken(secretKey string, iat, seconds int64, username string) (string, error) {
	claims := make(jwt.MapClaims)
	claims["exp"] = iat + seconds
	claims["iat"] = iat
	claims["username"] = username
	token := jwt.New(jwt.SigningMethodHS256)
	token.Claims = claims
	return token.SignedString([]byte(secretKey))
}

// GenerateToken 生成包含完整用户信息的JWT令牌
func GenerateToken(secretKey string, expireSeconds int64, userInfo UserInfo) (string, error) {
	now := time.Now().Unix()
	claims := make(jwt.MapClaims)
	claims["exp"] = now + expireSeconds
	claims["iat"] = now
	claims["member_id"] = userInfo.MemberID
	claims["username"] = userInfo.Username
	claims["type"] = userInfo.MemberType

	token := jwt.New(jwt.SigningMethodHS256)
	token.Claims = claims
	return token.SignedString([]byte(secretKey))
}

// GenerateTokenPair 生成Token对（AccessToken + RefreshToken）
func GenerateTokenPair(secretKey string, accessExpire int64, userInfo UserInfo) (accessToken, refreshToken string, err error) {
	// 如果没有指定过期时间，使用默认值
	if accessExpire <= 0 {
		accessExpire = DefaultAccessTokenExpire
	}

	accessToken, err = GenerateToken(secretKey, accessExpire, userInfo)
	if err != nil {
		return "", "", err
	}

	refreshToken, err = GenerateToken(secretKey, DefaultRefreshTokenExpire, userInfo)
	if err != nil {
		return "", "", err
	}

	return accessToken, refreshToken, nil
}

// ParseToken 解析JWT令牌并提取用户信息
func ParseToken(tokenString, secretKey string) (*UserInfo, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})

	if err != nil || !token.Valid {
		return nil, err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, jwt.ErrInvalidKey
	}

	userInfo := &UserInfo{}

	// 提取member_id（如果存在）
	if memberID, exists := claims["member_id"]; exists {
		if memberIDFloat, ok := memberID.(float64); ok {
			userInfo.MemberID = int64(memberIDFloat)
		}
	}

	// 提取username
	if username, exists := claims["username"]; exists {
		if usernameStr, ok := username.(string); ok {
			userInfo.Username = usernameStr
		}
	}

	// 提取type（如果存在）
	if memberType, exists := claims["type"]; exists {
		if typeFloat, ok := memberType.(float64); ok {
			userInfo.MemberType = int32(typeFloat)
		}
	}

	return userInfo, nil
}
