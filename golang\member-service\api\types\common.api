syntax = "v1"

// ===== 通用响应结构 =====
type CommonResp {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

// ===== 分页相关 =====
type PageInfo {
    Page     int `json:"page"`
    PageSize int `json:"page_size"`
    Total    int `json:"total"`
}

type BasePageReq {
    Page     int `form:"page,default=1"`
    PageSize int `form:"page_size,default=10"`
}

// ===== JWT 相关 =====
type JwtAuth {
    AccessToken  string `header:"Authorization"`
    RefreshToken string `header:"X-Refresh-Token,optional"`
}

// ===== 用户基础信息 =====
type BaseUserInfo {
    Id           int64  `json:"id"`
    Username     string `json:"username"`
    Nickname     string `json:"nickname"`
    Realname     string `json:"realname"`
    Mobile       string `json:"mobile"`
    Email        string `json:"email"`
    HeadPortrait string `json:"head_portrait"`
    Gender       int    `json:"gender"` // 0:未知 1:男 2:女
    Status       int    `json:"status"`
    CreatedAt    int64  `json:"created_at"`
    UpdatedAt    int64  `json:"updated_at"`
}

// ===== 状态枚举 =====
type Status {
    Deleted  int `json:"deleted"`  // -1
    Disabled int `json:"disabled"` // 0
    Enabled  int `json:"enabled"`  // 1
}

type MemberType {
    Member int `json:"member"` // 1:会员
    Admin  int `json:"admin"`  // 2:后台管理员
    Merchant int `json:"merchant"` // 3:商家管理员
}

type Gender {
    Unknown int `json:"unknown"` // 0:未知
    Male    int `json:"male"`    // 1:男
    Female  int `json:"female"`  // 2:女
} 