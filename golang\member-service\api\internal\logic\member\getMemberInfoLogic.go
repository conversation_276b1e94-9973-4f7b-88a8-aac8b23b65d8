package member

import (
	"context"
	"encoding/json"
	"fmt"
	memberpb "member/member"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type GetMemberInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户信息
func NewGetMemberInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberInfoLogic {
	return &GetMemberInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMemberInfoLogic) GetMemberInfo(req *types.GetMemberInfoReq) (resp *types.GetMemberInfoResp, err error) {
	// 从JWT token中获取当前用户ID
	var memberId int64

	// 如果请求中指定了member_id，使用指定的ID
	if req.MemberId > 0 {
		memberId = req.MemberId
	} else {
		// 否则从JWT token中获取当前用户member_id
		memberIdvalue := l.ctx.Value("member_id")
		if memberIdvalue == nil {
			return nil, fmt.Errorf("无法获取用户身份信息")
		}
		// 开始处理不同类型的member_id的值
		switch v := memberIdvalue.(type) {
		case int64:
			memberId = v
		case float64:
			memberId = int64(v)
		case string:
			if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
				memberId = parsed
			} else {
				return nil, fmt.Errorf("用户身份信息格式错误")
			}
		case json.Number:
			if parsed, parseErr := v.Int64(); parseErr == nil {
				memberId = parsed
			} else {
				return nil, fmt.Errorf("用户身份信息格式错误")
			}
		default:
			return nil, fmt.Errorf("用户身份信息类型错误: %T", v)
		}

	}

	if memberId <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}
	// 调用RPC服务获取用户信息
	rpcResp, err := l.svcCtx.MemberRpc.GetMemberInfo(l.ctx, &memberpb.GetMemberInfoReq{
		MemberId: memberId,
	})
	if err != nil {
		return nil, fmt.Errorf("调用RPC获取用户信息失败: %v", err)
	}

	// 检查RPC响应
	if rpcResp.Code != 200 {
		return &types.GetMemberInfoResp{
			Code:    int(rpcResp.Code),
			Message: rpcResp.Message,
		}, nil
	}

	// 转换RPC响应为API响应格式
	memberInfo := types.Member{
		Id:         rpcResp.Data.Id,
		MerchantId: rpcResp.Data.MerchantId,
		StoreId:    rpcResp.Data.StoreId,
		Username:   rpcResp.Data.Username,
		// 敏感字段不返回给前端
		Type:                int(rpcResp.Data.Type),
		Realname:            rpcResp.Data.Realname,
		Nickname:            rpcResp.Data.Nickname,
		HeadPortrait:        rpcResp.Data.HeadPortrait,
		Gender:              int(rpcResp.Data.Gender),
		Qq:                  rpcResp.Data.Qq,
		Email:               rpcResp.Data.Email,
		Birthday:            rpcResp.Data.Birthday,
		ProvinceId:          rpcResp.Data.ProvinceId,
		CityId:              rpcResp.Data.CityId,
		AreaId:              rpcResp.Data.AreaId,
		Address:             rpcResp.Data.Address,
		Mobile:              rpcResp.Data.Mobile,
		TelNo:               rpcResp.Data.TelNo,
		BgImage:             rpcResp.Data.BgImage,
		Description:         rpcResp.Data.Description,
		VisitCount:          int(rpcResp.Data.VisitCount),
		LastTime:            rpcResp.Data.LastTime,
		LastIp:              rpcResp.Data.LastIp,
		Role:                int(rpcResp.Data.Role),
		CurrentLevel:        int(rpcResp.Data.CurrentLevel),
		LevelExpirationTime: rpcResp.Data.LevelExpirationTime,
		LevelBuyType:        int(rpcResp.Data.LevelBuyType),
		Pid:                 rpcResp.Data.Pid,
		Level:               int(rpcResp.Data.Level),
		Tree:                rpcResp.Data.Tree,
		PromoterCode:        rpcResp.Data.PromoterCode,
		CertificationType:   int(rpcResp.Data.CertificationType),
		Source:              rpcResp.Data.Source,
		Status:              int(rpcResp.Data.Status),
		CreatedAt:           rpcResp.Data.CreatedAt,
		UpdatedAt:           rpcResp.Data.UpdatedAt,
		RegionId:            rpcResp.Data.RegionId,
	}

	return &types.GetMemberInfoResp{
		Code:    200,
		Message: "获取用户信息成功",
		Data:    memberInfo,
	}, nil
}
