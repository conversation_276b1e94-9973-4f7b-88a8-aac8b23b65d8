package member

import (
	"context"
	"encoding/json"
	"fmt"
	"member/member"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type GetCancelStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取注销状态
func NewGetCancelStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCancelStatusLogic {
	return &GetCancelStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCancelStatusLogic) GetCancelStatus(req *types.GetCancelStatusReq) (resp *types.GetCancelStatusResp, err error) {

	var memberId int64

	// 从JWT token 中获取当前用户member_id
	memberIdvalue := l.ctx.Value("member_id")
	if memberIdvalue == nil {
		return nil, fmt.Errorf("无法获取用户身份信息")
	}

	// 开始处理不同的类型的member_id的值
	switch v := memberIdvalue.(type) {
	case int64:
		memberId = v
	case float64:
		memberId = int64(v)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	case json.Number:
		if parsed, parseErr := v.Int64(); parseErr == nil {
			memberId = parsed
		} else {
			return nil, fmt.Errorf("用户身份信息格式错误")
		}
	default:
		return nil, fmt.Errorf("用户身份信息类型错误: %T", v)
	}

	// 调用RPC服务开始处理业务
	rpcResp, err := l.svcCtx.MemberRpc.GetCancelStatus(l.ctx, &member.GetCancelStatusReq{
		MemberId: memberId,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC获取注销状态失败: %v", err)
	}

	// 构建响应数据，需要从MemberCancel映射到CancelStatusData
	var hasApplication bool
	var statusText string
	var submitTime int64

	// 如果RPC返回了数据，说明有申请记录
	if rpcResp.Data != nil && rpcResp.Data.Id > 0 {
		hasApplication = true
		submitTime = rpcResp.Data.CreatedAt // 使用创建时间作为提交时间

		// 根据审核状态生成状态文本
		switch rpcResp.Data.AuditStatus {
		case 0:
			statusText = "申请中"
		case 1:
			statusText = "已通过"
		case -1:
			statusText = "已拒绝"
		default:
			statusText = "未知状态"
		}
	} else {
		hasApplication = false
		statusText = "无申请记录"
	}

	return &types.GetCancelStatusResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
		Data: types.CancelStatusData{
			HasApplication: hasApplication,
			AuditStatus:    int(rpcResp.Data.AuditStatus),
			StatusText:     statusText,
			Content:        rpcResp.Data.Content,
			SubmitTime:     submitTime,
			AuditTime:      rpcResp.Data.AuditTime,
			RefusalCause:   rpcResp.Data.RefusalCause,
		},
	}, nil
}
