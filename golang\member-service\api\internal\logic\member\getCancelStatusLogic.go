package member

import (
	"context"
	"encoding/json"
	"fmt"
	"member/member"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
)

type GetCancelStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取注销状态
func NewGetCancelStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCancelStatusLogic {
	return &GetCancelStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCancelStatusLogic) GetCancelStatus(req *types.GetCancelStatusReq) (resp *types.GetCancelStatusResp, err error) {

	var memberId int64

	// 从JWT token 中获取当前用户member_id
	memberIdvalue := l.ctx.Value("member_id")
	if memberIdvalue == nil {
		return nil, fmt.Errorf("无法获取用户身份信息")
	}

	// 开始处理不同的类型的member_id的值
	switch v := memberIdvalue.(type) {
	case int64:
		memberId = v
	case float64:
		memberId = int64(v)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberId = parsed
		}else {
			return nil,fmt.Errorf("用户身份信息格式错误")
		}
	case json.Number:
		if parsed, parseErr := v.Int64(); parseErr == nil {
			memberId = parsed
		}else {
			return nil,fmt.Errorf("用户身份信息格式错误")
		}
	default:
		return nil,fmt.Errorf("用户身份信息类型错误: %T", v)
	}

	// 调用RPC服务开始处理业务
	rpcResp, err:=l.svcCtx.MemberRpc.GetCancelStatus(l.ctx,&member.GetCancelStatusReq{
		
	})
	

	return
}
