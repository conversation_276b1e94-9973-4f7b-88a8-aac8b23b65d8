﻿package dict_category

import (
	"context"
	"dict_category_rpc/dict_category"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新字典项项列表分类
func NewUpdateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictCategoryLogic {
	return &UpdateDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDictCategoryLogic) UpdateDictCategory(req *types.UpdateDictCategoryReq) (resp *types.UpdateDictCategoryResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_category服务
	respRpc, err := l.svcCtx.DictCategoryRpc.UpdateDictCategory(l.ctx, &dict_category.UpdateDictCategoryReq{
		Id:     req.Id,
		DictId: req.DictId,
		Name:   req.Name,
		Status: req.Status,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_category服务的UpdateDictCategory方法失败: %v", err)
	}
	return &types.UpdateDictCategoryResp{
		Success: respRpc.Success,
		Message: "更新字典项项列表分类成功",
	},nil
}


