package logic

import (
	"context"
	"errors"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type DeleteDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictLogic {
	return &DeleteDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典
func (l *DeleteDictLogic) DeleteDict(in *dict.DeleteDictReq) (*dict.DeleteDictResp, error) {
	// 参数验证
	if in.Id <= 0 {
		return &dict.DeleteDictResp{
			Success: false,
			Message: "字典ID不能为空",
		}, nil
	}

	// 检查字典是否存在
	_, err := l.svcCtx.DictModel.FindByID(in.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dict.DeleteDictResp{
				Success: false,
				Message: "字典不存在",
			}, nil
		}
		return &dict.DeleteDictResp{
			Success: false,
			Message: "查询字典失败: " + err.Error(),
		}, nil
	}

	// 使用数据库级联删除 - 直接在数据库层面删除相关记录
	err = l.svcCtx.DictModel.DeleteWithCascade(in.Id)
	if err != nil {
		return &dict.DeleteDictResp{
			Success: false,
			Message: "删除字典失败: " + err.Error(),
		}, nil
	}

	return &dict.DeleteDictResp{
		Success: true,
		Message: "删除字典成功",
	}, nil
}
