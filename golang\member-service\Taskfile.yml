# https://taskfile.dev
# Member Service Taskfile

version: '3'

vars:
  # 服务端口配置
  API_PORT: 8888
  MEMBER_RPC_PORT: 8080
  ACCOUNT_RPC_PORT: 8081

tasks:
  # ===== 代码生成 =====
  gen:api:
    desc: "生成API服务代码"
    dir: api
    cmds:
      - goctl api go -api desc/member-api.api -dir . --style=goZero

  gen:rpc:member:
    desc: "生成Member RPC服务代码"
    dir: rpc/member
    cmds:
      - goctl rpc protoc pb/member.proto --go_out=. --go-grpc_out=. --zrpc_out=. --style=goZero

  gen:rpc:account:
    desc: "生成Account RPC服务代码"
    dir: rpc/account
    cmds:
      - goctl rpc protoc pb/account.proto --go_out=. --go-grpc_out=. --zrpc_out=. --style=goZero

  gen:all:
    desc: "生成所有服务代码"
    deps: [gen:api, gen:rpc:member, gen:rpc:account]

  # ===== Docker相关 =====
  docker:gen:
    desc: "生成所有服务的Dockerfile"
    cmds:
      - cd api && goctl docker --go member.go --port {{.API_PORT}} --version 1.0.0
      - cd rpc/member && goctl docker --go member.go --port {{.MEMBER_RPC_PORT}} --version 1.0.0
      - cd rpc/account && goctl docker --go account.go --port {{.ACCOUNT_RPC_PORT}} --version 1.0.0

  docker:build:
    desc: "构建所有Docker镜像"
    cmds:
      - docker-compose build

  docker:up:
    desc: "启动所有服务"
    cmds:
      - docker-compose up -d

  docker:down:
    desc: "停止所有服务"
    cmds:
      - docker-compose down

  docker:restart:
    desc: "重启所有服务"
    cmds:
      - docker-compose down
      - docker-compose up -d

  docker:logs:
    desc: "查看所有服务日志"
    cmds:
      - docker-compose logs -f

  docker:logs:api:
    desc: "查看API服务日志"
    cmds:
      - docker-compose logs -f member-api

  docker:logs:member:
    desc: "查看Member RPC服务日志"
    cmds:
      - docker-compose logs -f member-rpc

  docker:logs:account:
    desc: "查看Account RPC服务日志"
    cmds:
      - docker-compose logs -f account-rpc

  docker:clean:
    desc: "清理Docker环境"
    cmds:
      - docker-compose down -v
      - docker system prune -f

  # ===== 运行服务 =====
  run:api:
    desc: "运行API服务 (端口: {{.API_PORT}})"
    dir: api
    cmds:
      - go run member.go -f etc/member-api.yaml

  run:rpc:member:
    desc: "运行Member RPC服务 (端口: {{.MEMBER_RPC_PORT}})"
    dir: rpc/member
    cmds:
      - go run member.go -f etc/member.yaml

  run:rpc:account:
    desc: "运行Account RPC服务 (端口: {{.ACCOUNT_RPC_PORT}})"
    dir: rpc/account
    cmds:
      - go run account.go -f etc/account.yaml

  # ===== 构建服务 =====
  build:api:
    desc: "构建API服务"
    dir: api
    cmds:
      - go build -o member-api.exe member.go

  build:rpc:member:
    desc: "构建Member RPC服务"
    dir: rpc/member
    cmds:
      - go build -o member-rpc.exe member.go

  build:rpc:account:
    desc: "构建Account RPC服务"
    dir: rpc/account
    cmds:
      - go build -o account-rpc.exe account.go

  build:all:
    desc: "构建所有服务"
    deps: [build:api, build:rpc:member, build:rpc:account]

  # ===== 模块管理 =====
  mod:tidy:
    desc: "整理模块依赖"
    cmds:
      - cd api && go mod tidy
      - cd rpc/member && go mod tidy
      - cd rpc/account && go mod tidy

  mod:download:
    desc: "下载依赖"
    cmds:
      - cd api && go mod download
      - cd rpc/member && go mod download
      - cd rpc/account && go mod download

  # ===== 清理 =====
  clean:generated:
    desc: "清理生成的文件"
    cmds:
      - Remove-Item -Recurse -Force api/internal -ErrorAction SilentlyContinue
      - Remove-Item api/member.go -ErrorAction SilentlyContinue
      - Remove-Item api/go.mod -ErrorAction SilentlyContinue
      - Remove-Item -Recurse -Force rpc/member/internal -ErrorAction SilentlyContinue
      - Remove-Item rpc/member/member.go -ErrorAction SilentlyContinue
      - Remove-Item rpc/member/go.mod -ErrorAction SilentlyContinue
      - Remove-Item -Recurse -Force rpc/member/member -ErrorAction SilentlyContinue
      - Remove-Item -Recurse -Force rpc/member/memberservice -ErrorAction SilentlyContinue
      - Remove-Item -Recurse -Force rpc/account/internal -ErrorAction SilentlyContinue
      - Remove-Item rpc/account/account.go -ErrorAction SilentlyContinue
      - Remove-Item rpc/account/go.mod -ErrorAction SilentlyContinue
      - Remove-Item -Recurse -Force rpc/account/account -ErrorAction SilentlyContinue
      - Remove-Item -Recurse -Force rpc/account/accountservice -ErrorAction SilentlyContinue

  clean:build:
    desc: "清理构建文件"
    cmds:
      - Remove-Item api/*.exe -ErrorAction SilentlyContinue
      - Remove-Item rpc/member/*.exe -ErrorAction SilentlyContinue
      - Remove-Item rpc/account/*.exe -ErrorAction SilentlyContinue

  clean:all:
    desc: "清理所有生成的文件"
    deps: [clean:generated, clean:build]

  # ===== 开发工具 =====
  fmt:
    desc: "格式化代码"
    cmds:
      - cd api && go fmt ./...
      - cd rpc/member && go fmt ./...
      - cd rpc/account && go fmt ./...

  vet:
    desc: "代码检查"
    cmds:
      - cd api && go vet ./...
      - cd rpc/member && go vet ./...
      - cd rpc/account && go vet ./...

  test:
    desc: "运行测试"
    cmds:
      - cd api && go test ./...
      - cd rpc/member && go test ./...
      - cd rpc/account && go test ./...

  # ===== 部署相关 =====
  deploy:local:
    desc: "本地部署 (使用Docker Compose)"
    deps: [docker:build, docker:up]

  deploy:stop:
    desc: "停止本地部署"
    deps: [docker:down]

  # ===== 帮助 =====
  help:
    desc: "显示帮助信息"
    cmds:
      - task --list
