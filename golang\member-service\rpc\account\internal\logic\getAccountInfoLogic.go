package logic

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"account/account"
	"account/internal/svc"
)

type GetAccountInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetAccountInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAccountInfoLogic {
	return &GetAccountInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetAccountInfoLogic) GetAccountInfo(in *account.GetAccountInfoReq) (*account.GetAccountInfoResp, error) {

	// 直接使用传入的member_id
	memberID := in.MemberId
	if memberID <= 0 {
		return &account.GetAccountInfoResp{
			Code:    400,
			Message: "会员ID不能为空或无效",
		}, nil
	}

	// 通过model查询账户信息
	accountInfo, err := l.svcCtx.MemberAccountModel.FindByMemberID(memberID)
	if err != nil {
		return &account.GetAccountInfoResp{
			Code:    404,
			Message: fmt.Sprintf("查询账户信息失败: %v", err),
		}, nil
	}

	return &account.GetAccountInfoResp{
		Code:    200,
		Message: "查询账户信息成功",
		Data: &account.MemberAccount{
			Id: accountInfo.ID,
		},
	}, nil
}
