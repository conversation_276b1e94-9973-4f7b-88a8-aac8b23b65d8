package logic

import (
	"context"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCancelStatusLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCancelStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCancelStatusLogic {
	return &GetCancelStatusLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetCancelStatusLogic) GetCancelStatus(in *member.GetCancelStatusReq) (*member.GetCancelStatusResp, error) {
	// todo: add your logic here and delete this line

	return &member.GetCancelStatusResp{}, nil
}
