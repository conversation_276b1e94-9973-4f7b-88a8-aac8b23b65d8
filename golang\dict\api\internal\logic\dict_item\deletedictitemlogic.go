﻿package dict_item

import (
	"api/internal/svc"
	"api/internal/types"
	"context"
	"dict_item_rpc/dict_item"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除字典项列表项
func NewDeleteDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictItemLogic {
	return &DeleteDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictItemLogic) DeleteDictItem(req *types.DeleteDictItemReq) (resp *types.DeleteDictItemResp, err error) {
	// 调用dict_item RPC服务删除字典项
	// 调用dict_item服务
	respRpc, err := l.svcCtx.DictItemRpc.DeleteDictItem(l.ctx, &dict_item.DeleteDictItemReq{
		Id: req.Id,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_item服务的DeleteDictItem方法失败: %v", err)
	}
	return &types.DeleteDictItemResp{
		Success: respRpc.Success,
		Message: "删除字典项列表项成功",
	}, nil
}
