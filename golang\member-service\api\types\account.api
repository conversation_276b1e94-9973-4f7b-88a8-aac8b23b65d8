syntax = "v1"

// ===== 账户信息结构 =====
type MemberAccount {
    Id                   int64   `json:"id"`
    MerchantId          int64   `json:"merchant_id"`
    StoreId             int64   `json:"store_id"`
    MemberId            int64   `json:"member_id"`
    MemberType          int     `json:"member_type"`
    UserMoney           float64 `json:"user_money"`           // 当前余额
    AccumulateMoney     float64 `json:"accumulate_money"`     // 累计余额
    GiveMoney           float64 `json:"give_money"`           // 累计赠送余额
    ConsumeMoney        float64 `json:"consume_money"`        // 累计消费金额
    FrozenMoney         float64 `json:"frozen_money"`         // 冻结金额
    UserIntegral        int64   `json:"user_integral"`        // 当前积分
    AccumulateIntegral  int64   `json:"accumulate_integral"`  // 累计积分
    GiveIntegral        int64   `json:"give_integral"`        // 累计赠送积分
    ConsumeIntegral     float64 `json:"consume_integral"`     // 累计消费积分
    FrozenIntegral      int64   `json:"frozen_integral"`      // 冻结积分
    UserGrowth          int64   `json:"user_growth"`          // 当前成长值
    AccumulateGrowth    int64   `json:"accumulate_growth"`    // 累计成长值
    ConsumeGrowth       int64   `json:"consume_growth"`       // 累计消费成长值
    FrozenGrowth        int64   `json:"frozen_growth"`        // 冻结成长值
    EconomizeMoney      float64 `json:"economize_money"`      // 已节约金额
    AccumulateDrawnMoney float64 `json:"accumulate_drawn_money"` // 累计提现
    Status              int     `json:"status"`                 // 状态 -1:删除 0:禁用 1:启用
    CreatedAt            int64   `json:"created_at"`             // 创建时间
    UpdatedAt            int64   `json:"updated_at"`             // 更新时间
}

// ===== 账户信息请求 =====
type GetAccountInfoReq {
    MemberId int64 `form:"member_id,optional"`
}

type GetAccountInfoResp {
    Code    int           `json:"code"`
    Message string        `json:"message"`
    Data    MemberAccount `json:"data"`
}

type GetBalanceReq {
    MemberId int64 `form:"member_id,optional"`
}

type BalanceData {
    UserMoney   float64 `json:"user_money"`
    FrozenMoney float64 `json:"frozen_money"`
    TotalMoney  float64 `json:"total_money"`
}

type GetBalanceResp {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    BalanceData `json:"data"`
}

type GetIntegralInfoReq {
    MemberId int64 `form:"member_id,optional"`
}

type IntegralData {
    UserIntegral        int64   `json:"user_integral"`
    AccumulateIntegral  int64   `json:"accumulate_integral"`
    ConsumeIntegral     float64 `json:"consume_integral"`
    FrozenIntegral      int64   `json:"frozen_integral"`
}

type GetIntegralInfoResp {
    Code    int          `json:"code"`
    Message string       `json:"message"`
    Data    IntegralData `json:"data"`
}

type GetGrowthInfoReq {
    MemberId int64 `form:"member_id,optional"`
}

type GrowthData {
    UserGrowth       int64 `json:"user_growth"`
    AccumulateGrowth int64 `json:"accumulate_growth"`
    ConsumeGrowth    int64 `json:"consume_growth"`
    FrozenGrowth     int64 `json:"frozen_growth"`
}

type GetGrowthInfoResp {
    Code    int        `json:"code"`
    Message string     `json:"message"`
    Data    GrowthData `json:"data"`
}

// ===== 账户操作请求 =====
type RechargeReq {
    Amount      float64 `json:"amount"`
    PaymentType string  `json:"payment_type"` // alipay, wechat, bank
    Remark      string  `json:"remark,optional"`
}

type RechargeData {
    OrderNo   string  `json:"order_no"`
    Amount    float64 `json:"amount"`
    PayUrl    string  `json:"pay_url,optional"`
    QrCode    string  `json:"qr_code,optional"`
}

type RechargeResp {
    Code    int          `json:"code"`
    Message string       `json:"message"`
    Data    RechargeData `json:"data"`
}

type WithdrawReq {
    Amount      float64 `json:"amount"`
    AccountType string  `json:"account_type"` // bank, alipay, wechat
    AccountNo   string  `json:"account_no"`
    AccountName string  `json:"account_name"`
    BankName    string  `json:"bank_name,optional"`
    Remark      string  `json:"remark,optional"`
}

type WithdrawData {
    WithdrawNo   string  `json:"withdraw_no"`
    Amount       float64 `json:"amount"`
    Fee          float64 `json:"fee"`
    ActualAmount float64 `json:"actual_amount"`
}

type WithdrawResp {
    Code    int          `json:"code"`
    Message string       `json:"message"`
    Data    WithdrawData `json:"data"`
}

type FreezeMoneyReq {
    MemberId int64   `json:"member_id"`
    Amount   float64 `json:"amount"`
    Reason   string  `json:"reason"`
}

type UnfreezeMoneyReq {
    MemberId int64   `json:"member_id"`
    Amount   float64 `json:"amount"`
    Reason   string  `json:"reason"`
}

type ExchangeIntegralReq {
    Integral int64  `json:"integral"`
    GoodsId  int64  `json:"goods_id,optional"`
    Remark   string `json:"remark,optional"`
}