package logic

import (
	"context"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典分类
func (l *DeleteDictCategoryLogic) DeleteDictCategory(in *dict_category.DeleteDictCategoryReq) (*dict_category.DeleteDictCategoryResp, error) {
	// 参数验证
	if in.Id <= 0 {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "分类ID不能为空",
		}, nil
	}

	// 检查字典分类是否存在
	_, err := l.svcCtx.DictCategoryModel.FindByID(in.Id)
	if err != nil {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "字典分类不存在",
		}, nil
	}

	// 使用数据库级联删除该分类下的所有字典项（避免循环依赖）
	err = l.svcCtx.DictCategoryModel.DeleteWithCascade(in.Id)
	if err != nil {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "删除字典分类失败: " + err.Error(),
		}, nil
	}

	return &dict_category.DeleteDictCategoryResp{
		Success: true,
		Message: "删除字典分类成功",
	}, nil
}
