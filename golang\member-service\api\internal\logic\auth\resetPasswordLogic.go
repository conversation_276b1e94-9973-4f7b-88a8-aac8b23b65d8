package auth

import (
	"context"
	"fmt"
	memberpb "member/member"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ResetPasswordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 重置密码
func NewResetPasswordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ResetPasswordLogic {
	return &ResetPasswordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ResetPasswordLogic) ResetPassword(req *types.ResetPasswordReq) (resp *types.CommonResp, err error) {
	// 1. 验证码校验
	if req.VerifyCode != "123456" {
		return nil, fmt.Errorf("验证码错误")
	}

	//  2. 调用rpc方法
	rpcResp, err := l.svcCtx.MemberRpc.ResetPassword(l.ctx, &memberpb.ResetPasswordReq{
		Mobile:      req.Mobile,
		NewPassword: req.NewPassword,
	})
	if err != nil {
		return nil, fmt.Errorf("调用rpc的ResetPassword方法报错: %v", err)
	}

	return &types.CommonResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
	}, nil
}
