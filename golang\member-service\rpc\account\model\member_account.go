package model

import (
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Member 会员模型（仅用于查询基本信息）
type Member struct {
	ID         int64  `gorm:"primaryKey;autoIncrement;column:id"`
	MerchantID *int64 `gorm:"column:merchant_id"`
	StoreID    *int64 `gorm:"column:store_id"`
	Type       *int32 `gorm:"column:type"`
}

// TableName 设置表名
func (Member) TableName() string {
	return "member"
}

// MemberAccount 会员账户模型
type MemberAccount struct {
	ID                   int64   `gorm:"primaryKey;autoIncrement;column:id"`
	MerchantID           int64   `gorm:"column:merchant_id;not null"`          // 商户ID
	StoreID              int64   `gorm:"column:store_id;not null"`             // 店铺ID
	MemberID             int64   `gorm:"column:member_id;uniqueIndex;not null"` // 会员ID，每个会员只有一个账户
	MemberType           int32   `gorm:"column:member_type;not null"`          // 会员类型：1=会员 2=后台管理员 3=商家管理员
	// 余额相关
	UserMoney            float64 `gorm:"column:user_money;not null;default:0"`            // 当前余额
	AccumulateMoney      float64 `gorm:"column:accumulate_money;not null;default:0"`      // 累计余额
	GiveMoney            float64 `gorm:"column:give_money;not null;default:0"`            // 累计赠送余额
	ConsumeMoney         float64 `gorm:"column:consume_money;not null;default:0"`         // 累计消费金额
	FrozenMoney          float64 `gorm:"column:frozen_money;not null;default:0"`          // 冻结金额
	// 积分相关
	UserIntegral         int64   `gorm:"column:user_integral;not null;default:0"`         // 当前积分
	AccumulateIntegral   int64   `gorm:"column:accumulate_integral;not null;default:0"`   // 累计积分
	GiveIntegral         int64   `gorm:"column:give_integral;not null;default:0"`         // 累计赠送积分
	ConsumeIntegral      float64 `gorm:"column:consume_integral;not null;default:0"`      // 累计消费积分
	FrozenIntegral       int64   `gorm:"column:frozen_integral;not null;default:0"`       // 冻结积分
	// 成长值相关
	UserGrowth           int64   `gorm:"column:user_growth;not null;default:0"`           // 当前成长值
	AccumulateGrowth     int64   `gorm:"column:accumulate_growth;not null;default:0"`     // 累计成长值
	ConsumeGrowth        int64   `gorm:"column:consume_growth;not null;default:0"`        // 累计消费成长值
	FrozenGrowth         int64   `gorm:"column:frozen_growth;not null;default:0"`         // 冻结成长值
	// 其他
	EconomizeMoney       float64 `gorm:"column:economize_money;not null;default:0"`       // 已节约金额
	AccumulateDrawnMoney float64 `gorm:"column:accumulate_drawn_money;not null;default:0"` // 累计提现
	Status               int32   `gorm:"column:status;not null;default:1"`                // 状态：-1=删除 0=禁用 1=启用
	CreatedAt            int64   `gorm:"column:created_at;not null"`                      // 创建时间
	UpdatedAt            int64   `gorm:"column:updated_at;not null"`                      // 更新时间
}

// TableName 设置表名
func (MemberAccount) TableName() string {
	return "member_account"
}

// BeforeCreate GORM钩子：创建前设置时间戳
func (a *MemberAccount) BeforeCreate(tx *gorm.DB) error {
	now := time.Now().Unix()
	a.CreatedAt = now
	a.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM钩子：更新前设置时间戳
func (a *MemberAccount) BeforeUpdate(tx *gorm.DB) error {
	a.UpdatedAt = time.Now().Unix()
	return nil
}

// 定义一个账户模型接口 - MemberAccountModel
type MemberAccountModel struct {
	db *gorm.DB
}

// 创建一个账户模型 - NewMemberAccountModel
func NewMemberAccountModel(db *gorm.DB) *MemberAccountModel {
	return &MemberAccountModel{
		db: db,
	}
}

// ===== 基础CRUD操作 =====

// FindByID 根据ID查询账户
func (m *MemberAccountModel) FindByID(id int64) (*MemberAccount, error) {
	var account MemberAccount
	result := m.db.First(&account, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("账户ID=%d不存在", id)
		}
		return nil, result.Error
	}
	return &account, nil
}

// FindByMemberID 根据会员ID查询账户
func (m *MemberAccountModel) FindByMemberID(memberID int64) (*MemberAccount, error) {
	var account MemberAccount
	result := m.db.Where("member_id = ? AND status != -1", memberID).First(&account)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("会员ID=%d的账户不存在", memberID)
		}
		return nil, result.Error
	}
	return &account, nil
}

// Create 创建账户
func (m *MemberAccountModel) Create(account *MemberAccount) error {
	return m.db.Create(account).Error
}

// Update 更新账户信息
func (m *MemberAccountModel) Update(account *MemberAccount) error {
	return m.db.Save(account).Error
}

// Delete 软删除账户
func (m *MemberAccountModel) Delete(id int64) error {
	return m.db.Model(&MemberAccount{}).Where("id = ?", id).Update("status", -1).Error
}

// ===== 余额操作 =====

// AddMoney 增加余额（充值、赠送等）
func (m *MemberAccountModel) AddMoney(memberID int64, amount float64, isGive bool) error {
	if amount <= 0 {
		return errors.New("金额必须大于0")
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		var account MemberAccount
		err := tx.Where("member_id = ?", memberID).First(&account).Error
		
		// 如果账户不存在，自动创建默认账户
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 查询用户信息获取正确的商户和店铺ID
				var member Member
				if memberErr := tx.Where("id = ?", memberID).First(&member).Error; memberErr != nil {
					return fmt.Errorf("用户不存在: %v", memberErr)
				}
				
				// 设置默认值（如果用户表中的值为空或为0）
				merchantID := int64(1) // 默认商户ID
				storeID := int64(1)    // 默认店铺ID
				memberType := int32(1) // 默认会员类型
				
				if member.MerchantID != nil && *member.MerchantID > 0 {
					merchantID = *member.MerchantID
				}
				if member.StoreID != nil && *member.StoreID > 0 {
					storeID = *member.StoreID
				}
				if member.Type != nil && *member.Type > 0 {
					memberType = *member.Type
				}
				
				// 创建账户记录
				account = MemberAccount{
					MerchantID: merchantID,
					StoreID:    storeID,
					MemberID:   memberID,
					MemberType: memberType,
					Status:     1, // 启用状态
				}
				
				// 保存新账户
				if createErr := tx.Create(&account).Error; createErr != nil {
					return fmt.Errorf("创建账户失败: %v", createErr)
				}
			} else {
				return fmt.Errorf("查询账户失败: %v", err)
			}
		}

		// 更新余额
		account.UserMoney += amount
		account.AccumulateMoney += amount

		// 如果是赠送，记录赠送金额
		if isGive {
			account.GiveMoney += amount
		}

		return tx.Save(&account).Error
	})
}

// ConsumeMoney 消费余额
func (m *MemberAccountModel) ConsumeMoney(memberID int64, amount float64) error {
	if amount <= 0 {
		return errors.New("消费金额必须大于0")
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		var account MemberAccount
		if err := tx.Where("member_id = ?", memberID).First(&account).Error; err != nil {
			return fmt.Errorf("账户不存在: %v", err)
		}

		// 检查余额是否足够
		availableMoney := account.UserMoney - account.FrozenMoney
		if availableMoney < amount {
			return fmt.Errorf("余额不足，可用余额：%.2f，需要消费：%.2f", availableMoney, amount)
		}

		// 扣减余额，增加消费记录
		account.UserMoney -= amount
		account.ConsumeMoney += amount

		return tx.Save(&account).Error
	})
}

// FreezeMoney 冻结资金
func (m *MemberAccountModel) FreezeMoney(memberID int64, amount float64) error {
	if amount <= 0 {
		return errors.New("冻结金额必须大于0")
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		var account MemberAccount
		if err := tx.Where("member_id = ?", memberID).First(&account).Error; err != nil {
			return fmt.Errorf("账户不存在: %v", err)
		}

		// 检查可用余额是否足够冻结
		availableMoney := account.UserMoney - account.FrozenMoney
		if availableMoney < amount {
			return fmt.Errorf("可用余额不足，无法冻结，可用余额：%.2f", availableMoney)
		}

		account.FrozenMoney += amount
		return tx.Save(&account).Error
	})
}

// UnfreezeMoney 解冻资金
func (m *MemberAccountModel) UnfreezeMoney(memberID int64, amount float64) error {
	if amount <= 0 {
		return errors.New("解冻金额必须大于0")
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		var account MemberAccount
		if err := tx.Where("member_id = ?", memberID).First(&account).Error; err != nil {
			return fmt.Errorf("账户不存在: %v", err)
		}

		// 检查冻结金额是否足够
		if account.FrozenMoney < amount {
			return fmt.Errorf("冻结金额不足，无法解冻，冻结余额：%.2f", account.FrozenMoney)
		}

		account.FrozenMoney -= amount
		return tx.Save(&account).Error
	})
}

// Withdraw 提现
func (m *MemberAccountModel) Withdraw(memberID int64, amount float64) error {
	if amount <= 0 {
		return errors.New("提现金额必须大于0")
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		var account MemberAccount
		if err := tx.Where("member_id = ?", memberID).First(&account).Error; err != nil {
			return fmt.Errorf("账户不存在: %v", err)
		}

		// 检查可用余额
		availableMoney := account.UserMoney - account.FrozenMoney
		if availableMoney < amount {
			return fmt.Errorf("可用余额不足，无法提现，可用余额：%.2f", availableMoney)
		}

		// 扣减余额，记录提现
		account.UserMoney -= amount
		account.AccumulateDrawnMoney += amount

		return tx.Save(&account).Error
	})
}

// ===== 积分操作 =====

// AddIntegral 增加积分
func (m *MemberAccountModel) AddIntegral(memberID int64, integral int64, isGive bool) error {
	if integral <= 0 {
		return errors.New("积分必须大于0")
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		var account MemberAccount
		if err := tx.Where("member_id = ?", memberID).First(&account).Error; err != nil {
			return fmt.Errorf("账户不存在: %v", err)
		}

		account.UserIntegral += integral
		account.AccumulateIntegral += integral

		if isGive {
			account.GiveIntegral += integral
		}

		return tx.Save(&account).Error
	})
}

// ConsumeIntegral 消费积分
func (m *MemberAccountModel) ConsumeIntegral(memberID int64, integral int64) error {
	if integral <= 0 {
		return errors.New("消费积分必须大于0")
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		var account MemberAccount
		if err := tx.Where("member_id = ?", memberID).First(&account).Error; err != nil {
			return fmt.Errorf("账户不存在: %v", err)
		}

		// 检查积分是否足够
		availableIntegral := account.UserIntegral - account.FrozenIntegral
		if availableIntegral < integral {
			return fmt.Errorf("积分不足，可用积分：%d，需要消费：%d", availableIntegral, integral)
		}

		account.UserIntegral -= integral
		account.ConsumeIntegral += float64(integral)

		return tx.Save(&account).Error
	})
}

// ===== 成长值操作 =====

// AddGrowth 增加成长值
func (m *MemberAccountModel) AddGrowth(memberID int64, growth int64) error {
	if growth <= 0 {
		return errors.New("成长值必须大于0")
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		var account MemberAccount
		if err := tx.Where("member_id = ?", memberID).First(&account).Error; err != nil {
			return fmt.Errorf("账户不存在: %v", err)
		}

		account.UserGrowth += growth
		account.AccumulateGrowth += growth

		return tx.Save(&account).Error
	})
}

// ===== 查询操作 =====

// GetBalance 获取余额信息
func (m *MemberAccountModel) GetBalance(memberID int64) (total, available, frozen float64, err error) {
	var account MemberAccount
	if err = m.db.Where("member_id = ?", memberID).First(&account).Error; err != nil {
		return 0, 0, 0, fmt.Errorf("账户不存在: %v", err)
	}

	total = account.UserMoney
	frozen = account.FrozenMoney
	available = total - frozen

	return total, available, frozen, nil
}

// GetIntegralInfo 获取积分信息
func (m *MemberAccountModel) GetIntegralInfo(memberID int64) (total, available, frozen int64, err error) {
	var account MemberAccount
	if err = m.db.Where("member_id = ?", memberID).First(&account).Error; err != nil {
		return 0, 0, 0, fmt.Errorf("账户不存在: %v", err)
	}

	total = account.UserIntegral
	frozen = account.FrozenIntegral
	available = total - frozen

	return total, available, frozen, nil
}

// GetGrowthInfo 获取成长值信息
func (m *MemberAccountModel) GetGrowthInfo(memberID int64) (current, accumulate int64, err error) {
	var account MemberAccount
	if err = m.db.Where("member_id = ?", memberID).First(&account).Error; err != nil {
		return 0, 0, fmt.Errorf("账户不存在: %v", err)
	}

	current = account.UserGrowth
	accumulate = account.AccumulateGrowth

	return current, accumulate, nil
}

// ===== 列表查询 =====

// FindByMerchantID 根据商户ID查询账户列表
func (m *MemberAccountModel) FindByMerchantID(merchantID int64, page, pageSize int) ([]*MemberAccount, int64, error) {
	var accounts []*MemberAccount
	var total int64

	db := m.db.Where("merchant_id = ? AND status != -1", merchantID)

	// 计算总数
	if err := db.Model(&MemberAccount{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := db.Offset(offset).Limit(pageSize).Find(&accounts).Error; err != nil {
		return nil, 0, err
	}

	return accounts, total, nil
}

// CreateDefaultAccount 为新会员创建默认账户
func (m *MemberAccountModel) CreateDefaultAccount(memberID, merchantID, storeID int64, memberType int32) error {
	account := &MemberAccount{
		MerchantID: merchantID,
		StoreID:    storeID,
		MemberID:   memberID,
		MemberType: memberType,
		Status:     1, // 启用状态
	}

	return m.Create(account)
} 