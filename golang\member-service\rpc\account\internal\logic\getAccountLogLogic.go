package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAccountLogLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetAccountLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAccountLogLogic {
	return &GetAccountLogLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetAccountLogLogic) GetAccountLog(in *account.GetAccountLogReq) (*account.GetAccountLogResp, error) {
	// todo: add your logic here and delete this line

	return &account.GetAccountLogResp{}, nil
}
