// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.0
// source: pb/member.proto

package member

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MemberService_Register_FullMethodName                    = "/member.MemberService/Register"
	MemberService_Login_FullMethodName                       = "/member.MemberService/Login"
	MemberService_Logout_FullMethodName                      = "/member.MemberService/Logout"
	MemberService_RefreshToken_FullMethodName                = "/member.MemberService/RefreshToken"
	MemberService_ResetPassword_FullMethodName               = "/member.MemberService/ResetPassword"
	MemberService_ValidateToken_FullMethodName               = "/member.MemberService/ValidateToken"
	MemberService_GetMemberInfo_FullMethodName               = "/member.MemberService/GetMemberInfo"
	MemberService_UpdateMemberInfo_FullMethodName            = "/member.MemberService/UpdateMemberInfo"
	MemberService_GetMemberList_FullMethodName               = "/member.MemberService/GetMemberList"
	MemberService_DeleteMember_FullMethodName                = "/member.MemberService/DeleteMember"
	MemberService_CreateMember_FullMethodName                = "/member.MemberService/CreateMember"
	MemberService_GetCertificationInfo_FullMethodName        = "/member.MemberService/GetCertificationInfo"
	MemberService_SubmitCertification_FullMethodName         = "/member.MemberService/SubmitCertification"
	MemberService_AuditCertification_FullMethodName          = "/member.MemberService/AuditCertification"
	MemberService_GetPendingCertificationList_FullMethodName = "/member.MemberService/GetPendingCertificationList"
	MemberService_GetMemberStat_FullMethodName               = "/member.MemberService/GetMemberStat"
	MemberService_UpdateMemberStat_FullMethodName            = "/member.MemberService/UpdateMemberStat"
	MemberService_SubmitCancelApplication_FullMethodName     = "/member.MemberService/SubmitCancelApplication"
	MemberService_GetCancelStatus_FullMethodName             = "/member.MemberService/GetCancelStatus"
	MemberService_AuditCancelApplication_FullMethodName      = "/member.MemberService/AuditCancelApplication"
	MemberService_GetPendingCancelList_FullMethodName        = "/member.MemberService/GetPendingCancelList"
)

// MemberServiceClient is the client API for MemberService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ===== Member服务定义 =====
type MemberServiceClient interface {
	// 用户认证相关
	Register(ctx context.Context, in *RegisterReq, opts ...grpc.CallOption) (*RegisterResp, error)
	Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error)
	Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error)
	RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenResp, error)
	ResetPassword(ctx context.Context, in *ResetPasswordReq, opts ...grpc.CallOption) (*CommonResp, error)
	ValidateToken(ctx context.Context, in *ValidateTokenReq, opts ...grpc.CallOption) (*ValidateTokenResp, error)
	// 用户信息管理
	GetMemberInfo(ctx context.Context, in *GetMemberInfoReq, opts ...grpc.CallOption) (*GetMemberInfoResp, error)
	UpdateMemberInfo(ctx context.Context, in *UpdateMemberInfoReq, opts ...grpc.CallOption) (*CommonResp, error)
	GetMemberList(ctx context.Context, in *GetMemberListReq, opts ...grpc.CallOption) (*GetMemberListResp, error)
	DeleteMember(ctx context.Context, in *DeleteMemberReq, opts ...grpc.CallOption) (*CommonResp, error)
	CreateMember(ctx context.Context, in *CreateMemberReq, opts ...grpc.CallOption) (*CreateMemberResp, error)
	// 实名认证管理
	GetCertificationInfo(ctx context.Context, in *GetCertificationInfoReq, opts ...grpc.CallOption) (*GetCertificationInfoResp, error)
	SubmitCertification(ctx context.Context, in *SubmitCertificationReq, opts ...grpc.CallOption) (*CommonResp, error)
	AuditCertification(ctx context.Context, in *AuditCertificationReq, opts ...grpc.CallOption) (*CommonResp, error)
	GetPendingCertificationList(ctx context.Context, in *GetPendingCertificationListReq, opts ...grpc.CallOption) (*GetPendingCertificationListResp, error)
	// 用户统计
	GetMemberStat(ctx context.Context, in *GetMemberStatReq, opts ...grpc.CallOption) (*GetMemberStatResp, error)
	UpdateMemberStat(ctx context.Context, in *UpdateMemberStatReq, opts ...grpc.CallOption) (*CommonResp, error)
	// 注销申请
	SubmitCancelApplication(ctx context.Context, in *SubmitCancelApplicationReq, opts ...grpc.CallOption) (*CommonResp, error)
	GetCancelStatus(ctx context.Context, in *GetCancelStatusReq, opts ...grpc.CallOption) (*GetCancelStatusResp, error)
	AuditCancelApplication(ctx context.Context, in *AuditCancelApplicationReq, opts ...grpc.CallOption) (*CommonResp, error)
	GetPendingCancelList(ctx context.Context, in *GetPendingCancelListReq, opts ...grpc.CallOption) (*GetPendingCancelListResp, error)
}

type memberServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMemberServiceClient(cc grpc.ClientConnInterface) MemberServiceClient {
	return &memberServiceClient{cc}
}

func (c *memberServiceClient) Register(ctx context.Context, in *RegisterReq, opts ...grpc.CallOption) (*RegisterResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterResp)
	err := c.cc.Invoke(ctx, MemberService_Register_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoginResp)
	err := c.cc.Invoke(ctx, MemberService_Login_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogoutResp)
	err := c.cc.Invoke(ctx, MemberService_Logout_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefreshTokenResp)
	err := c.cc.Invoke(ctx, MemberService_RefreshToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) ResetPassword(ctx context.Context, in *ResetPasswordReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, MemberService_ResetPassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) ValidateToken(ctx context.Context, in *ValidateTokenReq, opts ...grpc.CallOption) (*ValidateTokenResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidateTokenResp)
	err := c.cc.Invoke(ctx, MemberService_ValidateToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) GetMemberInfo(ctx context.Context, in *GetMemberInfoReq, opts ...grpc.CallOption) (*GetMemberInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMemberInfoResp)
	err := c.cc.Invoke(ctx, MemberService_GetMemberInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) UpdateMemberInfo(ctx context.Context, in *UpdateMemberInfoReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, MemberService_UpdateMemberInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) GetMemberList(ctx context.Context, in *GetMemberListReq, opts ...grpc.CallOption) (*GetMemberListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMemberListResp)
	err := c.cc.Invoke(ctx, MemberService_GetMemberList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) DeleteMember(ctx context.Context, in *DeleteMemberReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, MemberService_DeleteMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) CreateMember(ctx context.Context, in *CreateMemberReq, opts ...grpc.CallOption) (*CreateMemberResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateMemberResp)
	err := c.cc.Invoke(ctx, MemberService_CreateMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) GetCertificationInfo(ctx context.Context, in *GetCertificationInfoReq, opts ...grpc.CallOption) (*GetCertificationInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCertificationInfoResp)
	err := c.cc.Invoke(ctx, MemberService_GetCertificationInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) SubmitCertification(ctx context.Context, in *SubmitCertificationReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, MemberService_SubmitCertification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) AuditCertification(ctx context.Context, in *AuditCertificationReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, MemberService_AuditCertification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) GetPendingCertificationList(ctx context.Context, in *GetPendingCertificationListReq, opts ...grpc.CallOption) (*GetPendingCertificationListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPendingCertificationListResp)
	err := c.cc.Invoke(ctx, MemberService_GetPendingCertificationList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) GetMemberStat(ctx context.Context, in *GetMemberStatReq, opts ...grpc.CallOption) (*GetMemberStatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMemberStatResp)
	err := c.cc.Invoke(ctx, MemberService_GetMemberStat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) UpdateMemberStat(ctx context.Context, in *UpdateMemberStatReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, MemberService_UpdateMemberStat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) SubmitCancelApplication(ctx context.Context, in *SubmitCancelApplicationReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, MemberService_SubmitCancelApplication_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) GetCancelStatus(ctx context.Context, in *GetCancelStatusReq, opts ...grpc.CallOption) (*GetCancelStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCancelStatusResp)
	err := c.cc.Invoke(ctx, MemberService_GetCancelStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) AuditCancelApplication(ctx context.Context, in *AuditCancelApplicationReq, opts ...grpc.CallOption) (*CommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonResp)
	err := c.cc.Invoke(ctx, MemberService_AuditCancelApplication_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *memberServiceClient) GetPendingCancelList(ctx context.Context, in *GetPendingCancelListReq, opts ...grpc.CallOption) (*GetPendingCancelListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPendingCancelListResp)
	err := c.cc.Invoke(ctx, MemberService_GetPendingCancelList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MemberServiceServer is the server API for MemberService service.
// All implementations must embed UnimplementedMemberServiceServer
// for forward compatibility.
//
// ===== Member服务定义 =====
type MemberServiceServer interface {
	// 用户认证相关
	Register(context.Context, *RegisterReq) (*RegisterResp, error)
	Login(context.Context, *LoginReq) (*LoginResp, error)
	Logout(context.Context, *LogoutReq) (*LogoutResp, error)
	RefreshToken(context.Context, *RefreshTokenReq) (*RefreshTokenResp, error)
	ResetPassword(context.Context, *ResetPasswordReq) (*CommonResp, error)
	ValidateToken(context.Context, *ValidateTokenReq) (*ValidateTokenResp, error)
	// 用户信息管理
	GetMemberInfo(context.Context, *GetMemberInfoReq) (*GetMemberInfoResp, error)
	UpdateMemberInfo(context.Context, *UpdateMemberInfoReq) (*CommonResp, error)
	GetMemberList(context.Context, *GetMemberListReq) (*GetMemberListResp, error)
	DeleteMember(context.Context, *DeleteMemberReq) (*CommonResp, error)
	CreateMember(context.Context, *CreateMemberReq) (*CreateMemberResp, error)
	// 实名认证管理
	GetCertificationInfo(context.Context, *GetCertificationInfoReq) (*GetCertificationInfoResp, error)
	SubmitCertification(context.Context, *SubmitCertificationReq) (*CommonResp, error)
	AuditCertification(context.Context, *AuditCertificationReq) (*CommonResp, error)
	GetPendingCertificationList(context.Context, *GetPendingCertificationListReq) (*GetPendingCertificationListResp, error)
	// 用户统计
	GetMemberStat(context.Context, *GetMemberStatReq) (*GetMemberStatResp, error)
	UpdateMemberStat(context.Context, *UpdateMemberStatReq) (*CommonResp, error)
	// 注销申请
	SubmitCancelApplication(context.Context, *SubmitCancelApplicationReq) (*CommonResp, error)
	GetCancelStatus(context.Context, *GetCancelStatusReq) (*GetCancelStatusResp, error)
	AuditCancelApplication(context.Context, *AuditCancelApplicationReq) (*CommonResp, error)
	GetPendingCancelList(context.Context, *GetPendingCancelListReq) (*GetPendingCancelListResp, error)
	mustEmbedUnimplementedMemberServiceServer()
}

// UnimplementedMemberServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMemberServiceServer struct{}

func (UnimplementedMemberServiceServer) Register(context.Context, *RegisterReq) (*RegisterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Register not implemented")
}
func (UnimplementedMemberServiceServer) Login(context.Context, *LoginReq) (*LoginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedMemberServiceServer) Logout(context.Context, *LogoutReq) (*LogoutResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedMemberServiceServer) RefreshToken(context.Context, *RefreshTokenReq) (*RefreshTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedMemberServiceServer) ResetPassword(context.Context, *ResetPasswordReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetPassword not implemented")
}
func (UnimplementedMemberServiceServer) ValidateToken(context.Context, *ValidateTokenReq) (*ValidateTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateToken not implemented")
}
func (UnimplementedMemberServiceServer) GetMemberInfo(context.Context, *GetMemberInfoReq) (*GetMemberInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberInfo not implemented")
}
func (UnimplementedMemberServiceServer) UpdateMemberInfo(context.Context, *UpdateMemberInfoReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMemberInfo not implemented")
}
func (UnimplementedMemberServiceServer) GetMemberList(context.Context, *GetMemberListReq) (*GetMemberListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberList not implemented")
}
func (UnimplementedMemberServiceServer) DeleteMember(context.Context, *DeleteMemberReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMember not implemented")
}
func (UnimplementedMemberServiceServer) CreateMember(context.Context, *CreateMemberReq) (*CreateMemberResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMember not implemented")
}
func (UnimplementedMemberServiceServer) GetCertificationInfo(context.Context, *GetCertificationInfoReq) (*GetCertificationInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCertificationInfo not implemented")
}
func (UnimplementedMemberServiceServer) SubmitCertification(context.Context, *SubmitCertificationReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitCertification not implemented")
}
func (UnimplementedMemberServiceServer) AuditCertification(context.Context, *AuditCertificationReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuditCertification not implemented")
}
func (UnimplementedMemberServiceServer) GetPendingCertificationList(context.Context, *GetPendingCertificationListReq) (*GetPendingCertificationListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPendingCertificationList not implemented")
}
func (UnimplementedMemberServiceServer) GetMemberStat(context.Context, *GetMemberStatReq) (*GetMemberStatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMemberStat not implemented")
}
func (UnimplementedMemberServiceServer) UpdateMemberStat(context.Context, *UpdateMemberStatReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMemberStat not implemented")
}
func (UnimplementedMemberServiceServer) SubmitCancelApplication(context.Context, *SubmitCancelApplicationReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitCancelApplication not implemented")
}
func (UnimplementedMemberServiceServer) GetCancelStatus(context.Context, *GetCancelStatusReq) (*GetCancelStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCancelStatus not implemented")
}
func (UnimplementedMemberServiceServer) AuditCancelApplication(context.Context, *AuditCancelApplicationReq) (*CommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuditCancelApplication not implemented")
}
func (UnimplementedMemberServiceServer) GetPendingCancelList(context.Context, *GetPendingCancelListReq) (*GetPendingCancelListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPendingCancelList not implemented")
}
func (UnimplementedMemberServiceServer) mustEmbedUnimplementedMemberServiceServer() {}
func (UnimplementedMemberServiceServer) testEmbeddedByValue()                       {}

// UnsafeMemberServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MemberServiceServer will
// result in compilation errors.
type UnsafeMemberServiceServer interface {
	mustEmbedUnimplementedMemberServiceServer()
}

func RegisterMemberServiceServer(s grpc.ServiceRegistrar, srv MemberServiceServer) {
	// If the following call pancis, it indicates UnimplementedMemberServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MemberService_ServiceDesc, srv)
}

func _MemberService_Register_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).Register(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_Register_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).Register(ctx, req.(*RegisterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).Login(ctx, req.(*LoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_Logout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).Logout(ctx, req.(*LogoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_RefreshToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).RefreshToken(ctx, req.(*RefreshTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_ResetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetPasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).ResetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_ResetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).ResetPassword(ctx, req.(*ResetPasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_ValidateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).ValidateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_ValidateToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).ValidateToken(ctx, req.(*ValidateTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_GetMemberInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).GetMemberInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_GetMemberInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).GetMemberInfo(ctx, req.(*GetMemberInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_UpdateMemberInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMemberInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).UpdateMemberInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_UpdateMemberInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).UpdateMemberInfo(ctx, req.(*UpdateMemberInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_GetMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).GetMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_GetMemberList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).GetMemberList(ctx, req.(*GetMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_DeleteMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).DeleteMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_DeleteMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).DeleteMember(ctx, req.(*DeleteMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_CreateMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).CreateMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_CreateMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).CreateMember(ctx, req.(*CreateMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_GetCertificationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCertificationInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).GetCertificationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_GetCertificationInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).GetCertificationInfo(ctx, req.(*GetCertificationInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_SubmitCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).SubmitCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_SubmitCertification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).SubmitCertification(ctx, req.(*SubmitCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_AuditCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuditCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).AuditCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_AuditCertification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).AuditCertification(ctx, req.(*AuditCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_GetPendingCertificationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPendingCertificationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).GetPendingCertificationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_GetPendingCertificationList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).GetPendingCertificationList(ctx, req.(*GetPendingCertificationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_GetMemberStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).GetMemberStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_GetMemberStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).GetMemberStat(ctx, req.(*GetMemberStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_UpdateMemberStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMemberStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).UpdateMemberStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_UpdateMemberStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).UpdateMemberStat(ctx, req.(*UpdateMemberStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_SubmitCancelApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitCancelApplicationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).SubmitCancelApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_SubmitCancelApplication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).SubmitCancelApplication(ctx, req.(*SubmitCancelApplicationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_GetCancelStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCancelStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).GetCancelStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_GetCancelStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).GetCancelStatus(ctx, req.(*GetCancelStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_AuditCancelApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuditCancelApplicationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).AuditCancelApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_AuditCancelApplication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).AuditCancelApplication(ctx, req.(*AuditCancelApplicationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MemberService_GetPendingCancelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPendingCancelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MemberServiceServer).GetPendingCancelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MemberService_GetPendingCancelList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MemberServiceServer).GetPendingCancelList(ctx, req.(*GetPendingCancelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// MemberService_ServiceDesc is the grpc.ServiceDesc for MemberService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MemberService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "member.MemberService",
	HandlerType: (*MemberServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Register",
			Handler:    _MemberService_Register_Handler,
		},
		{
			MethodName: "Login",
			Handler:    _MemberService_Login_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _MemberService_Logout_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _MemberService_RefreshToken_Handler,
		},
		{
			MethodName: "ResetPassword",
			Handler:    _MemberService_ResetPassword_Handler,
		},
		{
			MethodName: "ValidateToken",
			Handler:    _MemberService_ValidateToken_Handler,
		},
		{
			MethodName: "GetMemberInfo",
			Handler:    _MemberService_GetMemberInfo_Handler,
		},
		{
			MethodName: "UpdateMemberInfo",
			Handler:    _MemberService_UpdateMemberInfo_Handler,
		},
		{
			MethodName: "GetMemberList",
			Handler:    _MemberService_GetMemberList_Handler,
		},
		{
			MethodName: "DeleteMember",
			Handler:    _MemberService_DeleteMember_Handler,
		},
		{
			MethodName: "CreateMember",
			Handler:    _MemberService_CreateMember_Handler,
		},
		{
			MethodName: "GetCertificationInfo",
			Handler:    _MemberService_GetCertificationInfo_Handler,
		},
		{
			MethodName: "SubmitCertification",
			Handler:    _MemberService_SubmitCertification_Handler,
		},
		{
			MethodName: "AuditCertification",
			Handler:    _MemberService_AuditCertification_Handler,
		},
		{
			MethodName: "GetPendingCertificationList",
			Handler:    _MemberService_GetPendingCertificationList_Handler,
		},
		{
			MethodName: "GetMemberStat",
			Handler:    _MemberService_GetMemberStat_Handler,
		},
		{
			MethodName: "UpdateMemberStat",
			Handler:    _MemberService_UpdateMemberStat_Handler,
		},
		{
			MethodName: "SubmitCancelApplication",
			Handler:    _MemberService_SubmitCancelApplication_Handler,
		},
		{
			MethodName: "GetCancelStatus",
			Handler:    _MemberService_GetCancelStatus_Handler,
		},
		{
			MethodName: "AuditCancelApplication",
			Handler:    _MemberService_AuditCancelApplication_Handler,
		},
		{
			MethodName: "GetPendingCancelList",
			Handler:    _MemberService_GetPendingCancelList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/member.proto",
}
