syntax = "proto3";

package member;

option go_package = "./member";

// ===== Member服务定义 =====
service MemberService {
  // 用户认证相关
  rpc Register(RegisterReq) returns (RegisterResp);
  rpc Login(LoginReq) returns (LoginResp);
  rpc Logout(LogoutReq) returns (LogoutResp);
  rpc RefreshToken(RefreshTokenReq) returns (RefreshTokenResp);
  rpc ResetPassword(ResetPasswordReq) returns (CommonResp);
  rpc ValidateToken(ValidateTokenReq) returns (ValidateTokenResp);

  // 用户信息管理
  rpc GetMemberInfo(GetMemberInfoReq) returns (GetMemberInfoResp);
  rpc UpdateMemberInfo(UpdateMemberInfoReq) returns (CommonResp);
  rpc GetMemberList(GetMemberListReq) returns (GetMemberListResp);
  rpc DeleteMember(DeleteMemberReq) returns (CommonResp);
  rpc CreateMember(CreateMemberReq) returns (CreateMemberResp);

  // 实名认证管理
  rpc GetCertificationInfo(GetCertificationInfoReq) returns (GetCertificationInfoResp);
  rpc SubmitCertification(SubmitCertificationReq) returns (CommonResp);
  rpc AuditCertification(AuditCertificationReq) returns (CommonResp);
  rpc GetPendingCertificationList(GetPendingCertificationListReq) returns (GetPendingCertificationListResp);

  // 用户统计
  rpc GetMemberStat(GetMemberStatReq) returns (GetMemberStatResp);
  rpc UpdateMemberStat(UpdateMemberStatReq) returns (CommonResp);

  // 注销申请
  rpc SubmitCancelApplication(SubmitCancelApplicationReq) returns (CommonResp);
  rpc GetCancelStatus(GetCancelStatusReq) returns (GetCancelStatusResp);
  rpc AuditCancelApplication(AuditCancelApplicationReq) returns (CommonResp);
  rpc GetPendingCancelList(GetPendingCancelListReq) returns (GetPendingCancelListResp);
}

// ===== 通用响应 =====
message CommonResp {
  int32 code = 1;
  string message = 2;
}

// ===== 分页信息 =====
message PageInfo {
  int32 page = 1;
  int32 page_size = 2;
  int64 total = 3;
}

// ===== 用户信息（基于member表结构） =====
message Member {
  int64 id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  string username = 4;
  string password_hash = 5;           // 密码哈希
  string auth_key = 6;                // 授权令牌
  string password_reset_token = 7;    // 密码重置令牌
  string mobile_reset_token = 8;      // 手机重置令牌
  int32 type = 9; // 1:会员 2:后台管理员 3:商家管理员
  string realname = 10;
  string nickname = 11;
  string head_portrait = 12;
  int32 gender = 13; // 0:未知 1:男 2:女
  string qq = 14;
  string email = 15;
  string birthday = 16;
  int64 province_id = 17;
  int64 city_id = 18;
  int64 area_id = 19;
  string address = 20;
  string mobile = 21;
  string tel_no = 22;
  string bg_image = 23;
  string description = 24;
  int32 visit_count = 25;
  int64 last_time = 26;
  string last_ip = 27;
  int32 role = 28;
  int32 current_level = 29;
  int64 level_expiration_time = 30;
  int32 level_buy_type = 31;
  int64 pid = 32;
  int32 level = 33;
  string tree = 34;
  string promoter_code = 35;
  int32 certification_type = 36;
  string source = 37;
  int32 status = 38; // -1:删除 0:禁用 1:启用
  int64 created_at = 39;
  int64 updated_at = 40;
  int64 region_id = 41;
}

// ===== 用户公开信息（不包含敏感字段） =====
message MemberPublic {
  int64 id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  string username = 4;
  int32 type = 5; // 1:会员 2:后台管理员 3:商家管理员
  string realname = 6;
  string nickname = 7;
  string head_portrait = 8;
  int32 gender = 9; // 0:未知 1:男 2:女
  string qq = 10;
  string email = 11;
  string birthday = 12;
  int64 province_id = 13;
  int64 city_id = 14;
  int64 area_id = 15;
  string address = 16;
  string mobile = 17;
  string tel_no = 18;
  string bg_image = 19;
  string description = 20;
  int32 visit_count = 21;
  int64 last_time = 22;
  string last_ip = 23;
  int32 role = 24;
  int32 current_level = 25;
  int64 level_expiration_time = 26;
  int32 level_buy_type = 27;
  int64 pid = 28;
  int32 level = 29;
  string tree = 30;
  string promoter_code = 31;
  int32 certification_type = 32;
  string source = 33;
  int32 status = 34; // -1:删除 0:禁用 1:启用
  int64 created_at = 35;
  int64 updated_at = 36;
  int64 region_id = 37;
}

// ===== 实名认证信息（基于member_certification表） =====
message MemberCertification {
  int64 id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  int64 member_id = 4;
  int32 member_type = 5;
  string realname = 6;
  string identity_card = 7;
  string identity_card_front = 8;
  string identity_card_back = 9;
  string gender = 10;
  string birthday = 11;
  int32 front_is_fake = 12;
  int32 back_is_fake = 13;
  string nationality = 14;
  string address = 15;
  string start_date = 16;
  string end_date = 17;
  string issue = 18;
  int32 is_self = 19;
  int32 status = 20;
  int64 created_at = 21;
  int64 updated_at = 22;
}

// ===== 用户统计信息（基于member_stat表） =====
message MemberStat {
  int64 id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  int64 member_id = 4;
  int32 member_type = 5;
  int64 nice_num = 6;      // 点赞数量
  int64 disagree_num = 7;  // 不赞同数量
  int64 transmit_num = 8;  // 转发数量
  int64 comment_num = 9;   // 评论数量
  int64 collect_num = 10;  // 收藏数量
  int64 report_num = 11;   // 举报数量
  int64 recommend_num = 12; // 推荐数量
  int64 follow_num = 13;   // 关注人数
  int64 allowed_num = 14;  // 被关注人数
  int64 view = 15;         // 浏览量
  int32 status = 16;
  int64 created_at = 17;
  int64 updated_at = 18;
}

// ===== 注销申请信息（基于member_cancel表） =====
message MemberCancel {
  int64 id = 1;
  int64 merchant_id = 2;
  int64 store_id = 3;
  int64 member_id = 4;
  string content = 5;        // 申请内容
  int32 audit_status = 6;    // 审核状态 0:申请 1:通过 -1:失败
  int64 audit_time = 7;      // 审核时间
  string refusal_cause = 8;  // 拒绝原因
  int32 is_addon = 9;
  string addon_name = 10;
  int32 status = 11;
  int64 created_at = 12;
  int64 updated_at = 13;
}

// ===== 用户注册 =====
message RegisterReq {
  string username = 1;
  string password = 2;
  string mobile = 3;
  string email = 4;
  string nickname = 5;
  string source = 6;
  int64 merchant_id = 7;
  int64 store_id = 8;
}

message RegisterResp {
  int32 code = 1;
  string message = 2;
  int64 member_id = 3;
  string access_token = 4;
  string refresh_token = 5;
}

// ===== 用户登录 =====
message LoginReq {
  string username = 1;
  string password = 2;
  string mobile = 3;
  int32 login_type = 4; // 1:用户名 2:手机号
}

message LoginResp {
  int32 code = 1;
  string message = 2;
  Member member_info = 3;
  string access_token = 4;
  string refresh_token = 5;
}

// ===== 用户登出 =====
message LogoutReq {
  int64 member_id = 1;
  string token = 2;
}

message LogoutResp {
  int32 code = 1;
  string message = 2;
}

// ===== 刷新Token =====
message RefreshTokenReq {
  string refresh_token = 1;
}

message RefreshTokenResp {
  int32 code = 1;
  string message = 2;
  string access_token = 3;
  string refresh_token = 4;
}

// ===== 重置密码 =====
message ResetPasswordReq {
  string mobile = 1;
  string new_password = 2;
  string verification_code = 3;
}

// ===== Token验证 =====
message ValidateTokenReq {
  string token = 1;
}

message ValidateTokenResp {
  int32 code = 1;
  string message = 2;
  int64 member_id = 3;
  Member member_info = 4;
}

// ===== 获取用户信息 =====
message GetMemberInfoReq {
  int64 member_id = 1;
}

message GetMemberInfoResp {
  int32 code = 1;
  string message = 2;
  Member data = 3;
}

// ===== 更新用户信息 =====
message UpdateMemberInfoReq {
  int64 member_id = 1;
  string realname = 2;
  string nickname = 3;
  string head_portrait = 4;
  int32 gender = 5;
  string qq = 6;
  string email = 7;
  string birthday = 8;
  int64 province_id = 9;
  int64 city_id = 10;
  int64 area_id = 11;
  string address = 12;
  string tel_no = 13;
  string bg_image = 14;
  string description = 15;
}

// ===== 获取用户列表 =====
message GetMemberListReq {
  int32 page = 1;
  int32 page_size = 2;
  string keyword = 3;
  int32 status = 4;
  int32 type = 5;
}

message GetMemberListResp {
  int32 code = 1;
  string message = 2;
  repeated Member list = 3;
  PageInfo page_info = 4;
}

// ===== 删除用户 =====
message DeleteMemberReq {
  int64 member_id = 1;
}

// ===== 创建用户 =====
message CreateMemberReq {
  int64 merchant_id = 1;
  int64 store_id = 2;
  string username = 3;
  string password = 4;
  string mobile = 5;
  string email = 6;
  string nickname = 7;
  int32 type = 8;
  string source = 9;
}

message CreateMemberResp {
  int32 code = 1;
  string message = 2;
  int64 member_id = 3;
}

// ===== 实名认证相关 =====
message GetCertificationInfoReq {
  int64 member_id = 1;
}

message GetCertificationInfoResp {
  int32 code = 1;
  string message = 2;
  MemberCertification data = 3;
}

message SubmitCertificationReq {
  int64 member_id = 1;
  string realname = 2;
  string identity_card = 3;
  string identity_card_front = 4;
  string identity_card_back = 5;
  string address = 6;
}

message AuditCertificationReq {
  int64 certification_id = 1;
  int32 audit_result = 2; // 1:通过 -1:拒绝
  string refusal_reason = 3;
  int64 auditor_id = 4;
}

message GetPendingCertificationListReq {
  int32 page = 1;
  int32 page_size = 2;
}

message GetPendingCertificationListResp {
  int32 code = 1;
  string message = 2;
  repeated MemberCertification list = 3;
  PageInfo page_info = 4;
}

// ===== 用户统计相关 =====
message GetMemberStatReq {
  int64 member_id = 1;
}

message GetMemberStatResp {
  int32 code = 1;
  string message = 2;
  MemberStat data = 3;
}

message UpdateMemberStatReq {
  int64 member_id = 1;
  int64 nice_num = 2;
  int64 collect_num = 3;
  int64 follow_num = 4;
  int64 view = 5;
}

// ===== 注销申请相关 =====
message SubmitCancelApplicationReq {
  int64 member_id = 1;
  string content = 2;
}

message GetCancelStatusReq {
  int64 member_id = 1;
}

message GetCancelStatusResp {
  int32 code = 1;
  string message = 2;
  MemberCancel data = 3;
}

message AuditCancelApplicationReq {
  int64 cancel_id = 1;
  int32 audit_result = 2; // 1:通过 -1:拒绝
  string refusal_reason = 3;
  int64 auditor_id = 4;
}

message GetPendingCancelListReq {
  int32 page = 1;
  int32 page_size = 2;
}

message GetPendingCancelListResp {
  int32 code = 1;
  string message = 2;
  repeated MemberCancel list = 3;
  PageInfo page_info = 4;
} 