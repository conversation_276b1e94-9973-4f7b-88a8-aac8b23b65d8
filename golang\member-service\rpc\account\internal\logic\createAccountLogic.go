package logic

import (
	"context"
	"fmt"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateAccountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAccountLogic {
	return &CreateAccountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateAccountLogic) CreateAccount(in *account.CreateAccountReq) (*account.CreateAccountResp, error) {
	// 参数验证
	if in.MemberId <= 0 {
		return &account.CreateAccountResp{
			Code:    400,
			Message: "会员ID不能为空",
		}, nil
	}

	if in.MerchantId <= 0 {
		return &account.CreateAccountResp{
			Code:    400,
			Message: "商户ID不能为空",
		}, nil
	}

	// 检查账户是否已存在
	existingAccount, err := l.svcCtx.MemberAccountModel.FindByMemberID(in.MemberId)
	if err == nil && existingAccount != nil {
		return &account.CreateAccountResp{
			Code:      400,
			Message:   "该会员账户已存在",
			AccountId: existingAccount.ID,
		}, nil
	}

	// 创建账户
	err = l.svcCtx.MemberAccountModel.CreateDefaultAccount(in.MemberId, in.MerchantId, in.StoreId, in.MemberType)
	if err != nil {
		l.Logger.Errorf("创建账户失败: member_id=%d, merchant_id=%d, error=%v", in.MemberId, in.MerchantId, err)
		return &account.CreateAccountResp{
			Code:    500,
			Message: fmt.Sprintf("创建账户失败: %v", err),
		}, nil
	}

	// 获取创建的账户信息
	newAccount, err := l.svcCtx.MemberAccountModel.FindByMemberID(in.MemberId)
	if err != nil {
		l.Logger.Errorf("获取新创建的账户失败: member_id=%d, error=%v", in.MemberId, err)
		return &account.CreateAccountResp{
			Code:    500,
			Message: "创建账户成功，但获取账户信息失败",
		}, nil
	}

	l.Logger.Infof("创建账户成功: member_id=%d, account_id=%d", in.MemberId, newAccount.ID)

	return &account.CreateAccountResp{
		Code:      200,
		Message:   "创建账户成功",
		AccountId: newAccount.ID,
	}, nil
}
