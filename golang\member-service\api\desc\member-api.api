syntax = "v1"

info (
	title:   "会员服务API"
	desc:    "基于SQL表结构的会员管理系统API"
	author:  "system"
	email:   "<EMAIL>"
	version: "v1.0.0"
)

import "../types/common.api"
import "../types/member.api"
import "../types/account.api"
import "../types/certification.api"
import "../types/stat.api"
import "../types/cancel.api"

// ===== 用户认证服务（无需JWT） =====
@server (
	group:  auth
	prefix: /api/v1/auth
)
service member-api {
	@doc "用户注册"
	@handler Register
	post /register (RegisterReq) returns (RegisterResp)

	@doc "用户登录"
	@handler Login
	post /login (LoginReq) returns (LoginResp)

	@doc "重置密码"
	@handler ResetPassword
	post /reset-password (ResetPasswordReq) returns (CommonResp)

	@doc "刷新token"
	@handler RefreshToken
	post /refresh (RefreshTokenReq) returns (RefreshTokenResp)
}

// ===== 用户管理服务（需要JWT） =====
@server (
	group:  member
	prefix: /api/v1/member
	jwt:    JwtAuth
)
service member-api {
	@doc "获取用户信息"
	@handler GetMemberInfo
	get /info (GetMemberInfoReq) returns (GetMemberInfoResp)

	@doc "更新用户信息"
	@handler UpdateMemberInfo
	put /info (UpdateMemberInfoReq) returns (CommonResp)

	// @doc "用户登出"
	// @handler Logout
	// post /logout (LogoutReq) returns (CommonResp)

	@doc "获取账户信息"
	@handler GetAccountInfo
	get /account (GetAccountInfoReq) returns (GetAccountInfoResp)

	@doc "充值"
	@handler Recharge
	post /account/recharge (RechargeReq) returns (RechargeResp)

	@doc "提现"
	@handler Withdraw
	post /account/withdraw (WithdrawReq) returns (WithdrawResp)

	@doc "获取实名认证信息"
	@handler GetCertificationInfo
	get /certification (GetCertificationInfoReq) returns (GetCertificationInfoResp)

	@doc "提交实名认证"
	@handler SubmitCertification
	post /certification (SubmitCertificationReq) returns (CommonResp)

	@doc "获取统计信息"
	@handler GetMemberStat
	get /stat (GetMemberStatReq) returns (GetMemberStatResp)

	@doc "提交注销申请"
	@handler SubmitCancelApplication
	post /cancel (SubmitCancelReq) returns (CommonResp)

	@doc "获取注销状态"
	@handler GetCancelStatus
	get /cancel/status (GetCancelStatusReq) returns (GetCancelStatusResp)
}

// ===== 管理员服务（需要JWT和管理员权限） =====
@server (
	group:      admin
	prefix:     /api/v1/admin
	jwt:        JwtAuth
	middleware: AdminAuth
)
service member-api {
	@doc "获取用户列表"
	@handler GetMemberList
	get /members (GetMemberListReq) returns (GetMemberListResp)

	@doc "获取用户详情"
	@handler GetMemberDetail
	get /members/:id (GetMemberDetailReq) returns (GetMemberDetailResp)

	@doc "删除用户"
	@handler DeleteMember
	delete /members/:id (DeleteMemberReq) returns (CommonResp)

	@doc "冻结用户资金"
	@handler FreezeMoney
	post /members/:id/freeze (FreezeMoneyReq) returns (CommonResp)

	@doc "解冻用户资金"
	@handler UnfreezeMoney
	post /members/:id/unfreeze (UnfreezeMoneyReq) returns (CommonResp)

	@doc "审核实名认证"
	@handler AuditCertification
	post /certifications/:id/audit (AuditCertificationReq) returns (CommonResp)

	@doc "获取待审核认证列表"
	@handler GetPendingCertificationList
	get /certifications/pending (GetPendingListReq) returns (GetPendingListResp)

	@doc "审核注销申请"
	@handler AuditCancelApplication
	post /cancels/:id/audit (AuditCancelReq) returns (CommonResp)

	@doc "获取待审核注销列表"
	@handler GetPendingCancelList
	get /cancels/pending (GetPendingCancelListReq) returns (GetPendingCancelListResp)

	@doc "获取用户统计报表"
	@handler GetMemberStatReport
	get /reports/stats (GetStatReportReq) returns (GetStatReportResp)
}

