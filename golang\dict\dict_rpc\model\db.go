﻿package model

import (
	"fmt"
	"time"

	"gorm.io/gorm/logger"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func NewDb(dsn string) *gorm.DB {
	db, err := gorm.Open(mysql.New(mysql.Config{
		DriverName:                "mysql", // 用于指定数据库类型（如 MySQLPostgreSQL）
		DSN:                       dsn,
		DefaultStringSize:         256,   // string 类型字段的默认长度
		DisableDatetimePrecision:  true,  // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
		DontSupportRenameIndex:    true,  // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
		DontSupportRenameColumn:   true,  // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列  使用修改语句修改列名
		SkipInitializeWithVersion: false, // 根据当前 MySQL 版本自动配置
	}), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info), // 设置为 LogModeSQL 以出输出 SQL 语句
	})
	if err != nil {
		// 修改这里，不返回nil而是直接panic，这样在服务启动时就会发现问题
		panic(fmt.Sprintf("数据库初始化失败: %v", err))
	}

	// 添加日志，显示开始迁移表结构
	fmt.Println("开始迁移表结构...")

	// 自动迁移表结构
	err = db.AutoMigrate(&Dict{})
	if err != nil {
		panic(fmt.Sprintf("表结构迁移失败: %v", err))
	}

	// 添加日志，显示表结构迁移成功
	fmt.Println("表结构迁移成功!")

	sqlDB, err := db.DB()
	if err != nil {
		panic(fmt.Sprintf("获取数据库连接失败: %v", err))
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Minute * 5)
	return db
}

