// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: account.proto

package accountservice

import (
	"context"

	"account/account"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AccountLog          = account.AccountLog
	AddGrowthReq        = account.AddGrowthReq
	AddIntegralLogReq   = account.AddIntegralLogReq
	AddIntegralReq      = account.AddIntegralReq
	AddMoneyLogReq      = account.AddMoneyLogReq
	CommonResp          = account.CommonResp
	ConsumeIntegralReq  = account.ConsumeIntegralReq
	CreateAccountReq    = account.CreateAccountReq
	CreateAccountResp   = account.CreateAccountResp
	FreezeMoneyReq      = account.FreezeMoneyReq
	GetAccountInfoReq   = account.GetAccountInfoReq
	GetAccountInfoResp  = account.GetAccountInfoResp
	GetAccountListReq   = account.GetAccountListReq
	GetAccountListResp  = account.GetAccountListResp
	GetAccountLogReq    = account.GetAccountLogReq
	GetAccountLogResp   = account.GetAccountLogResp
	GetBalanceReq       = account.GetBalanceReq
	GetBalanceResp      = account.GetBalanceResp
	GetGrowthInfoReq    = account.GetGrowthInfoReq
	GetGrowthInfoResp   = account.GetGrowthInfoResp
	GetIntegralInfoReq  = account.GetIntegralInfoReq
	GetIntegralInfoResp = account.GetIntegralInfoResp
	MemberAccount       = account.MemberAccount
	PageInfo            = account.PageInfo
	RechargeReq         = account.RechargeReq
	RechargeResp        = account.RechargeResp
	UnfreezeMoneyReq    = account.UnfreezeMoneyReq
	WithdrawReq         = account.WithdrawReq
	WithdrawResp        = account.WithdrawResp

	AccountService interface {
		// 账户信息管理
		CreateAccount(ctx context.Context, in *CreateAccountReq, opts ...grpc.CallOption) (*CreateAccountResp, error)
		GetAccountInfo(ctx context.Context, in *GetAccountInfoReq, opts ...grpc.CallOption) (*GetAccountInfoResp, error)
		GetAccountList(ctx context.Context, in *GetAccountListReq, opts ...grpc.CallOption) (*GetAccountListResp, error)
		// 余额操作
		GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceResp, error)
		Recharge(ctx context.Context, in *RechargeReq, opts ...grpc.CallOption) (*RechargeResp, error)
		Withdraw(ctx context.Context, in *WithdrawReq, opts ...grpc.CallOption) (*WithdrawResp, error)
		FreezeMoney(ctx context.Context, in *FreezeMoneyReq, opts ...grpc.CallOption) (*CommonResp, error)
		UnfreezeMoney(ctx context.Context, in *UnfreezeMoneyReq, opts ...grpc.CallOption) (*CommonResp, error)
		// 积分操作
		GetIntegralInfo(ctx context.Context, in *GetIntegralInfoReq, opts ...grpc.CallOption) (*GetIntegralInfoResp, error)
		AddIntegral(ctx context.Context, in *AddIntegralReq, opts ...grpc.CallOption) (*CommonResp, error)
		ConsumeIntegral(ctx context.Context, in *ConsumeIntegralReq, opts ...grpc.CallOption) (*CommonResp, error)
		// 成长值操作
		GetGrowthInfo(ctx context.Context, in *GetGrowthInfoReq, opts ...grpc.CallOption) (*GetGrowthInfoResp, error)
		AddGrowth(ctx context.Context, in *AddGrowthReq, opts ...grpc.CallOption) (*CommonResp, error)
		// 账户日志
		AddMoneyLog(ctx context.Context, in *AddMoneyLogReq, opts ...grpc.CallOption) (*CommonResp, error)
		AddIntegralLog(ctx context.Context, in *AddIntegralLogReq, opts ...grpc.CallOption) (*CommonResp, error)
		GetAccountLog(ctx context.Context, in *GetAccountLogReq, opts ...grpc.CallOption) (*GetAccountLogResp, error)
	}

	defaultAccountService struct {
		cli zrpc.Client
	}
)

func NewAccountService(cli zrpc.Client) AccountService {
	return &defaultAccountService{
		cli: cli,
	}
}

// 账户信息管理
func (m *defaultAccountService) CreateAccount(ctx context.Context, in *CreateAccountReq, opts ...grpc.CallOption) (*CreateAccountResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.CreateAccount(ctx, in, opts...)
}

func (m *defaultAccountService) GetAccountInfo(ctx context.Context, in *GetAccountInfoReq, opts ...grpc.CallOption) (*GetAccountInfoResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.GetAccountInfo(ctx, in, opts...)
}

func (m *defaultAccountService) GetAccountList(ctx context.Context, in *GetAccountListReq, opts ...grpc.CallOption) (*GetAccountListResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.GetAccountList(ctx, in, opts...)
}

// 余额操作
func (m *defaultAccountService) GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.GetBalance(ctx, in, opts...)
}

func (m *defaultAccountService) Recharge(ctx context.Context, in *RechargeReq, opts ...grpc.CallOption) (*RechargeResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.Recharge(ctx, in, opts...)
}

func (m *defaultAccountService) Withdraw(ctx context.Context, in *WithdrawReq, opts ...grpc.CallOption) (*WithdrawResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.Withdraw(ctx, in, opts...)
}

func (m *defaultAccountService) FreezeMoney(ctx context.Context, in *FreezeMoneyReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.FreezeMoney(ctx, in, opts...)
}

func (m *defaultAccountService) UnfreezeMoney(ctx context.Context, in *UnfreezeMoneyReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.UnfreezeMoney(ctx, in, opts...)
}

// 积分操作
func (m *defaultAccountService) GetIntegralInfo(ctx context.Context, in *GetIntegralInfoReq, opts ...grpc.CallOption) (*GetIntegralInfoResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.GetIntegralInfo(ctx, in, opts...)
}

func (m *defaultAccountService) AddIntegral(ctx context.Context, in *AddIntegralReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.AddIntegral(ctx, in, opts...)
}

func (m *defaultAccountService) ConsumeIntegral(ctx context.Context, in *ConsumeIntegralReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.ConsumeIntegral(ctx, in, opts...)
}

// 成长值操作
func (m *defaultAccountService) GetGrowthInfo(ctx context.Context, in *GetGrowthInfoReq, opts ...grpc.CallOption) (*GetGrowthInfoResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.GetGrowthInfo(ctx, in, opts...)
}

func (m *defaultAccountService) AddGrowth(ctx context.Context, in *AddGrowthReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.AddGrowth(ctx, in, opts...)
}

// 账户日志
func (m *defaultAccountService) AddMoneyLog(ctx context.Context, in *AddMoneyLogReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.AddMoneyLog(ctx, in, opts...)
}

func (m *defaultAccountService) AddIntegralLog(ctx context.Context, in *AddIntegralLogReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.AddIntegralLog(ctx, in, opts...)
}

func (m *defaultAccountService) GetAccountLog(ctx context.Context, in *GetAccountLogReq, opts ...grpc.CallOption) (*GetAccountLogResp, error) {
	client := account.NewAccountServiceClient(m.cli.Conn())
	return client.GetAccountLog(ctx, in, opts...)
}
