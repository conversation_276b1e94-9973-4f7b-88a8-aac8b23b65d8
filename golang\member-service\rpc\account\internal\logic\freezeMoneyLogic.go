package logic

import (
	"context"
	"fmt"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type FreezeMoneyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewFreezeMoneyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FreezeMoneyLogic {
	return &FreezeMoneyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *FreezeMoneyLogic) FreezeMoney(in *account.FreezeMoneyReq) (*account.CommonResp, error) {
	// 参数验证
	if in.MemberId <= 0 {
		return &account.CommonResp{
			Code:    400,
			Message: "会员ID不能为空",
		}, nil
	}

	if in.Amount <= 0 {
		return &account.CommonResp{
			Code:    400,
			Message: "冻结金额必须大于0",
		}, nil
	}

	// 执行冻结操作
	err := l.svcCtx.MemberAccountModel.FreezeMoney(in.MemberId, in.Amount)
	if err != nil {
		l.Logger.Errorf("冻结资金失败: member_id=%d, amount=%.2f, reason=%s, error=%v", 
			in.MemberId, in.Amount, in.Reason, err)
		return &account.CommonResp{
			Code:    500,
			Message: fmt.Sprintf("冻结资金失败: %v", err),
		}, nil
	}

	l.Logger.Infof("冻结资金成功: member_id=%d, amount=%.2f, reason=%s, operator_id=%d", 
		in.MemberId, in.Amount, in.Reason, in.OperatorId)

	return &account.CommonResp{
		Code:    200,
		Message: "冻结资金成功",
	}, nil
}
