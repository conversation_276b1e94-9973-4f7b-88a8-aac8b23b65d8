﻿// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: dict.proto

package server

import (
	"context"

	"dict_rpc/dict"
	"dict_rpc/internal/logic"
	"dict_rpc/internal/svc"
)

type DictServiceServer struct {
	svcCtx *svc.ServiceContext
	dict.UnimplementedDictServiceServer
}

func NewDictServiceServer(svcCtx *svc.ServiceContext) *DictServiceServer {
	return &DictServiceServer{
		svcCtx: svcCtx,
	}
}

// 创建字典
func (s *DictServiceServer) CreateDict(ctx context.Context, in *dict.CreateDictReq) (*dict.CreateDictResp, error) {
	l := logic.NewCreateDictLogic(ctx, s.svcCtx)
	return l.CreateDict(in)
}

// 更新字典项
func (s *DictServiceServer) UpdateDict(ctx context.Context, in *dict.UpdateDictReq) (*dict.UpdateDictResp, error) {
	l := logic.NewUpdateDictLogic(ctx, s.svcCtx)
	return l.UpdateDict(in)
}

// 删除字典
func (s *DictServiceServer) DeleteDict(ctx context.Context, in *dict.DeleteDictReq) (*dict.DeleteDictResp, error) {
	l := logic.NewDeleteDictLogic(ctx, s.svcCtx)
	return l.DeleteDict(in)
}

// 获取字典详情
func (s *DictServiceServer) GetDict(ctx context.Context, in *dict.GetDictReq) (*dict.GetDictResp, error) {
	l := logic.NewGetDictLogic(ctx, s.svcCtx)
	return l.GetDict(in)
}

// 字典列表
func (s *DictServiceServer) ListDict(ctx context.Context, in *dict.ListDictReq) (*dict.ListDictResp, error) {
	l := logic.NewListDictLogic(ctx, s.svcCtx)
	return l.ListDict(in)
}

