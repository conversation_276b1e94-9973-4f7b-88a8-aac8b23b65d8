# Member Service - 会员管理系统

基于Go-Zero框架的微服务架构会员管理系统，严格按照SQL数据库表结构设计，提供用户管理、账户管理、实名认证、统计分析和注销等功能。

## 📁 项目结构

```
member-service/
├── README.md                 # 项目说明文档
├── Taskfile.yml             # 任务自动化配置
├── api/                     # API Gateway服务
│   ├── desc/                # API定义文件目录
│   │   └── member-api.api   # API定义文件
│   ├── types/               # API类型定义
│   │   ├── common.api       # 通用类型定义
│   │   ├── member.api       # 用户相关类型
│   │   ├── account.api      # 账户相关类型
│   │   ├── certification.api # 认证相关类型
│   │   ├── stat.api         # 统计相关类型
│   │   └── cancel.api       # 注销相关类型
│   ├── internal/            # 生成的代码
│   ├── etc/                # 配置文件
│   └── member.go           # 服务入口
└── rpc/                    # RPC服务
    ├── member/             # 用户RPC服务
    │   ├── pb/             # Proto定义文件目录
    │   │   └── member.proto # Proto定义文件
    │   ├── internal/       # 生成的代码
    │   ├── etc/           # 配置文件
    │   └── member.go      # 服务入口
    └── account/           # 账户RPC服务
        ├── pb/            # Proto定义文件目录
        │   └── account.proto # Proto定义文件
        ├── internal/      # 生成的代码
        ├── etc/          # 配置文件
        └── account.go    # 服务入口
```

## 🏗️ 架构设计

### 数据库表映射
基于以下5个核心表进行设计：

| 表名 | 用途 | 对应服务 |
|-----|------|---------|
| `member` | 用户基础信息表 | MemberService |
| `member_account` | 用户账户信息表 | AccountService |
| `member_certification` | 实名认证信息表 | MemberService |
| `member_stat` | 用户统计信息表 | MemberService |
| `member_cancel` | 注销申请信息表 | MemberService |

### 服务架构
- **API Gateway (HTTP)**: 统一的HTTP接口层，处理前端请求
- **Member RPC**: 用户管理、认证、统计相关的gRPC服务
- **Account RPC**: 账户管理、余额、积分、成长值相关的gRPC服务

## 🌐 API分组策略

### HTTP API分组（3个分组）
1. **auth** - 用户认证（无需JWT）
   - 用户注册、登录、重置密码、刷新Token
   
2. **member** - 用户服务（需要JWT）
   - 用户信息管理、账户操作、实名认证、统计查询、注销申请
   
3. **admin** - 管理员服务（需要JWT + AdminAuth）
   - 用户管理、资金冻结、认证审核、注销审核、统计报表

### RPC服务分组（2个服务）
1. **MemberService** - 用户管理服务
   - 用户认证、信息管理、实名认证、统计、注销
   
2. **AccountService** - 账户管理服务
   - 余额、积分、成长值管理及相关日志

## 📋 API端点

### 认证服务 (无需JWT)
```
POST /api/v1/auth/register      - 用户注册
POST /api/v1/auth/login         - 用户登录
POST /api/v1/auth/reset-password - 重置密码
POST /api/v1/auth/refresh       - 刷新Token
```

### 用户服务 (需要JWT)
```bash
# 用户信息
GET  /api/v1/member/info        - 获取用户信息
PUT  /api/v1/member/info        - 更新用户信息
POST /api/v1/member/logout      - 用户登出

# 账户管理
GET  /api/v1/member/account     - 获取账户信息
POST /api/v1/member/account/recharge - 充值
POST /api/v1/member/account/withdraw - 提现

# 实名认证
GET  /api/v1/member/certification - 获取认证信息
POST /api/v1/member/certification - 提交实名认证

# 统计信息
GET  /api/v1/member/stat        - 获取统计信息

# 注销申请
POST /api/v1/member/cancel      - 提交注销申请
GET  /api/v1/member/cancel/status - 获取注销状态
```

### 管理员服务 (需要JWT + AdminAuth)
```bash
# 用户管理
GET    /api/v1/admin/members         - 获取用户列表
GET    /api/v1/admin/members/:id     - 获取用户详情
DELETE /api/v1/admin/members/:id     - 删除用户

# 资金管理
POST /api/v1/admin/members/:id/freeze   - 冻结用户资金
POST /api/v1/admin/members/:id/unfreeze - 解冻用户资金

# 认证审核
GET  /api/v1/admin/certifications/pending  - 获取待审核认证列表
POST /api/v1/admin/certifications/:id/audit - 审核实名认证

# 注销审核
GET  /api/v1/admin/cancels/pending      - 获取待审核注销列表
POST /api/v1/admin/cancels/:id/audit    - 审核注销申请

# 统计报表
GET  /api/v1/admin/reports/stats        - 获取用户统计报表
```

## 🔗 RPC服务详情

### MemberService (member.proto)
```protobuf
// 用户认证
rpc Register(RegisterReq) returns (RegisterResp);
rpc Login(LoginReq) returns (LoginResp);
rpc Logout(LogoutReq) returns (LogoutResp);
rpc RefreshToken(RefreshTokenReq) returns (RefreshTokenResp);
rpc ResetPassword(ResetPasswordReq) returns (CommonResp);
rpc ValidateToken(ValidateTokenReq) returns (ValidateTokenResp);

// 用户信息管理
rpc GetMemberInfo(GetMemberInfoReq) returns (GetMemberInfoResp);
rpc UpdateMemberInfo(UpdateMemberInfoReq) returns (CommonResp);
rpc GetMemberList(GetMemberListReq) returns (GetMemberListResp);
rpc DeleteMember(DeleteMemberReq) returns (CommonResp);
rpc CreateMember(CreateMemberReq) returns (CreateMemberResp);

// 实名认证管理
rpc GetCertificationInfo(GetCertificationInfoReq) returns (GetCertificationInfoResp);
rpc SubmitCertification(SubmitCertificationReq) returns (CommonResp);
rpc AuditCertification(AuditCertificationReq) returns (CommonResp);
rpc GetPendingCertificationList(GetPendingCertificationListReq) returns (GetPendingCertificationListResp);

// 用户统计
rpc GetMemberStat(GetMemberStatReq) returns (GetMemberStatResp);
rpc UpdateMemberStat(UpdateMemberStatReq) returns (CommonResp);

// 注销申请
rpc SubmitCancelApplication(SubmitCancelApplicationReq) returns (CommonResp);
rpc GetCancelStatus(GetCancelStatusReq) returns (GetCancelStatusResp);
rpc AuditCancelApplication(AuditCancelApplicationReq) returns (CommonResp);
rpc GetPendingCancelList(GetPendingCancelListReq) returns (GetPendingCancelListResp);
```

### AccountService (account.proto)
```protobuf
// 账户信息管理
rpc CreateAccount(CreateAccountReq) returns (CreateAccountResp);
rpc GetAccountInfo(GetAccountInfoReq) returns (GetAccountInfoResp);
rpc GetAccountList(GetAccountListReq) returns (GetAccountListResp);

// 余额操作
rpc GetBalance(GetBalanceReq) returns (GetBalanceResp);
rpc Recharge(RechargeReq) returns (RechargeResp);
rpc Withdraw(WithdrawReq) returns (WithdrawResp);
rpc FreezeMoney(FreezeMoneyReq) returns (CommonResp);
rpc UnfreezeMoney(UnfreezeMoneyReq) returns (CommonResp);

// 积分操作
rpc GetIntegralInfo(GetIntegralInfoReq) returns (GetIntegralInfoResp);
rpc AddIntegral(AddIntegralReq) returns (CommonResp);
rpc ConsumeIntegral(ConsumeIntegralReq) returns (CommonResp);

// 成长值操作
rpc GetGrowthInfo(GetGrowthInfoReq) returns (CommonResp);
rpc AddGrowth(AddGrowthReq) returns (CommonResp);

// 账户日志
rpc AddMoneyLog(AddMoneyLogReq) returns (CommonResp);
rpc AddIntegralLog(AddIntegralLogReq) returns (CommonResp);
rpc GetAccountLog(GetAccountLogReq) returns (GetAccountLogResp);
```

## 🚀 快速开始

### 环境要求
- Go 1.19+
- Go-Zero框架
- goctl工具
- Task工具 (可选)

### 使用Task命令

```bash
# 生成所有服务代码
task gen:all

# 生成单个服务
task gen:api              # 生成API服务
task gen:rpc:member       # 生成Member RPC服务
task gen:rpc:account      # 生成Account RPC服务

# 运行服务
task run:api              # 运行API服务 (端口8888)
task run:rpc:member       # 运行Member RPC服务 (端口8080)
task run:rpc:account      # 运行Account RPC服务 (端口8081)

# 构建服务
task build:all            # 构建所有服务

# 模块管理
task mod:tidy             # 整理模块依赖
task mod:download         # 下载依赖

# 清理文件
task clean:all            # 清理所有生成的文件

# 开发工具
task fmt                  # 格式化代码
task vet                  # 代码检查
task test                 # 运行测试
```

### 手动生成代码

```bash
# 生成API服务代码
cd api
goctl api go -api desc/member-api.api -dir . --style=goZero

# 生成Member RPC服务代码
cd rpc/member
goctl rpc protoc pb/member.proto --go_out=. --go-grpc_out=. --zrpc_out=. --style=goZero

# 生成Account RPC服务代码
cd rpc/account
goctl rpc protoc pb/account.proto --go_out=. --go-grpc_out=. --zrpc_out=. --style=goZero
```

## 🗄️ 数据库表结构对应

### Member表字段映射
```go
type Member struct {
    Id                  int64   // 用户ID
    MerchantId         int64   // 商户ID
    StoreId            int64   // 店铺ID
    Username           string  // 账号
    Type               int32   // 用户类型 1:会员 2:后台管理员 3:商家管理员
    Realname           string  // 真实姓名
    Nickname           string  // 昵称
    HeadPortrait       string  // 头像
    Gender             int32   // 性别 0:未知 1:男 2:女
    Mobile             string  // 手机号码
    Email              string  // 邮箱
    // ... 其他字段与数据库表一一对应
}
```

### MemberAccount表字段映射
```go
type MemberAccount struct {
    Id                    int64   // 账户ID
    MemberId             int64   // 用户ID
    UserMoney            float64 // 当前余额
    AccumulateMoney      float64 // 累计余额
    FrozenMoney          float64 // 冻结金额
    UserIntegral         int64   // 当前积分
    AccumulateIntegral   int64   // 累计积分
    UserGrowth           int64   // 当前成长值
    AccumulateGrowth     int64   // 累计成长值
    // ... 其他字段与数据库表一一对应
}
```

## ⚙️ 配置说明

### API服务配置 (api/etc/member-api.yaml)
```yaml
Name: member-api
Host: 0.0.0.0
Port: 8888
```

### RPC服务配置
- Member RPC: `rpc/member/etc/member.yaml` (端口8080)
- Account RPC: `rpc/account/etc/account.yaml` (端口8081)

## 🛠️ 开发指南

1. **修改API定义**: 编辑 `api/member-api.api` 或 `api/types/*.api`
2. **重新生成代码**: 运行 `task gen:api`
3. **实现业务逻辑**: 在 `api/internal/logic/` 中实现具体逻辑
4. **修改RPC定义**: 编辑对应的 `.proto` 文件
5. **重新生成RPC代码**: 运行对应的 `task gen:rpc:*` 命令
6. **实现RPC逻辑**: 在 `rpc/*/internal/logic/` 中实现具体逻辑

## 📝 设计原则

1. **基于SQL表结构**: 所有数据结构严格按照数据库表字段设计
2. **简化分组策略**: 只保留必要的3个API分组和2个RPC服务
3. **职责分离**: Member服务负责用户信息，Account服务负责账户操作
4. **遵循RESTful**: API设计遵循RESTful规范
5. **统一响应格式**: 所有接口使用统一的响应结构
6. **类型安全**: 强类型定义，避免类型错误

## 🔧 技术特性

- **微服务架构**: API Gateway + 2个RPC服务
- **JWT认证**: 基于Token的用户认证
- **权限控制**: 管理员权限中间件
- **分层设计**: Handler -> Logic -> RPC调用
- **代码生成**: 基于goctl自动生成代码
- **数据库映射**: 直接映射SQL表结构 