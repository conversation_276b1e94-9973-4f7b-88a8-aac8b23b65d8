# 🚀 **项目接口文档总览**

本文档详细列出了整个项目中所有的API接口和RPC方法的实现状态。

---

## 📊 **实现状态总览**

| 服务 | 总接口数 | 已实现 | 未实现 | 实现率 |
|------|----------|--------|--------|--------|
| **Member-Service** | **61** | **10** | **51** | **16.4%** |
| - HTTP API | 27 | 4 | 23 | 14.8% |
| - Member RPC | 19 | 6 | 13 | 31.6% |
| - Account RPC | 15 | 0 | 15 | 0% |
| **Dict-Service** | **45** | **25** | **20** | **55.6%** |
| - Dict API | 5 | 0 | 5 | 0% |
| - Dict RPC | 5 | 5 | 0 | 100% |
| - Dict Category API | 5 | 0 | 5 | 0% |
| - Dict Category RPC | 5 | 5 | 0 | 100% |
| - Dict Item API | 5 | 5 | 0 | 100% |
| - Dict Item RPC | 5 | 5 | 0 | 100% |
| **总计** | **106** | **35** | **71** | **33.0%** |

---

## 🏢 **Member-Service (会员服务)**

### **🌐 HTTP API 接口 (27个)**

#### **1. 认证服务 (无需JWT) - 4个接口**

| 接口名称 | 方法 | 路径 | 实现状态 | 说明 |
|----------|------|------|----------|------|
| 用户注册 | `POST` | `/api/v1/auth/register` | ✅ **已实现** | 支持用户名/手机号注册 |
| 用户登录 | `POST` | `/api/v1/auth/login` | ✅ **已实现** | 支持用户名/手机号登录 |
| 重置密码 | `POST` | `/api/v1/auth/reset-password` | ✅ **已实现** | 密码重置功能 |
| 刷新Token | `POST` | `/api/v1/auth/refresh` | ✅ **已实现** | JWT Token刷新 |

#### **2. 用户服务 (需要JWT) - 11个接口**

| 接口名称 | 方法 | 路径 | 实现状态 | 说明 |
|----------|------|------|----------|------|
| 获取用户信息 | `GET` | `/api/v1/member/info` | ✅ **已实现** | 获取当前用户详细信息 |
| 更新用户信息 | `PUT` | `/api/v1/member/info` | ❌ **TODO** | 更新用户基本信息 |
| 用户登出 | `POST` | `/api/v1/member/logout` | ❌ **TODO** | 用户登出处理 |
| 获取账户信息 | `GET` | `/api/v1/member/account` | ❌ **TODO** | 获取用户账户余额等信息 |
| 充值 | `POST` | `/api/v1/member/account/recharge` | ❌ **TODO** | 用户账户充值 |
| 提现 | `POST` | `/api/v1/member/account/withdraw` | ❌ **TODO** | 用户账户提现 |
| 获取实名认证信息 | `GET` | `/api/v1/member/certification` | ❌ **TODO** | 获取用户实名认证状态 |
| 提交实名认证 | `POST` | `/api/v1/member/certification` | ❌ **TODO** | 提交实名认证申请 |
| 获取统计信息 | `GET` | `/api/v1/member/stat` | ❌ **TODO** | 获取用户统计数据 |
| 提交注销申请 | `POST` | `/api/v1/member/cancel` | ❌ **TODO** | 提交账户注销申请 |
| 获取注销状态 | `GET` | `/api/v1/member/cancel/status` | ❌ **TODO** | 查询注销申请状态 |

#### **3. 管理员服务 (需要JWT+AdminAuth) - 10个接口**

| 接口名称 | 方法 | 路径 | 实现状态 | 说明 |
|----------|------|------|----------|------|
| 获取用户列表 | `GET` | `/api/v1/admin/members` | ❌ **TODO** | 分页获取用户列表 |
| 获取用户详情 | `GET` | `/api/v1/admin/members/:id` | ❌ **TODO** | 获取指定用户详细信息 |
| 删除用户 | `DELETE` | `/api/v1/admin/members/:id` | ❌ **TODO** | 删除指定用户 |
| 冻结用户资金 | `POST` | `/api/v1/admin/members/:id/freeze` | ❌ **TODO** | 冻结用户账户资金 |
| 解冻用户资金 | `POST` | `/api/v1/admin/members/:id/unfreeze` | ❌ **TODO** | 解冻用户账户资金 |
| 审核实名认证 | `POST` | `/api/v1/admin/certifications/:id/audit` | ❌ **TODO** | 审核用户实名认证申请 |
| 获取待审核认证列表 | `GET` | `/api/v1/admin/certifications/pending` | ❌ **TODO** | 获取待审核的实名认证列表 |
| 审核注销申请 | `POST` | `/api/v1/admin/cancels/:id/audit` | ❌ **TODO** | 审核用户注销申请 |
| 获取待审核注销列表 | `GET` | `/api/v1/admin/cancels/pending` | ❌ **TODO** | 获取待审核的注销申请列表 |
| 获取用户统计报表 | `GET` | `/api/v1/admin/reports/stats` | ❌ **TODO** | 获取用户相关统计报表 |

### **🔌 RPC 接口**

#### **Member RPC Service - 19个方法**

| 分类 | RPC方法 | 实现状态 | 说明 |
|------|---------|----------|------|
| **用户认证** | `Register` | ✅ **已实现** | 用户注册，创建member和member_account记录 |
| | `Login` | ✅ **已实现** | 用户登录，生成JWT Token |
| | `Logout` | ❌ **TODO** | 用户登出处理 |
| | `RefreshToken` | ✅ **已实现** | 刷新JWT Token |
| | `ResetPassword` | ✅ **已实现** | 重置用户密码 |
| | `ValidateToken` | ✅ **已实现** | 验证JWT Token有效性 |
| **用户信息管理** | `GetMemberInfo` | ✅ **已实现** | 获取用户详细信息 |
| | `UpdateMemberInfo` | ❌ **TODO** | 更新用户信息 |
| | `GetMemberList` | ❌ **TODO** | 获取用户列表 |
| | `DeleteMember` | ❌ **TODO** | 删除用户 |
| | `CreateMember` | ❌ **TODO** | 创建用户 |
| **实名认证** | `GetCertificationInfo` | ❌ **TODO** | 获取实名认证信息 |
| | `SubmitCertification` | ❌ **TODO** | 提交实名认证 |
| | `AuditCertification` | ❌ **TODO** | 审核实名认证 |
| | `GetPendingCertificationList` | ❌ **TODO** | 获取待审核认证列表 |
| **用户统计** | `GetMemberStat` | ❌ **TODO** | 获取用户统计信息 |
| | `UpdateMemberStat` | ❌ **TODO** | 更新用户统计信息 |
| **注销申请** | `SubmitCancelApplication` | ❌ **TODO** | 提交注销申请 |
| | `GetCancelStatus` | ❌ **TODO** | 获取注销状态 |
| | `AuditCancelApplication` | ❌ **TODO** | 审核注销申请 |
| | `GetPendingCancelList` | ❌ **TODO** | 获取待审核注销列表 |

#### **Account RPC Service - 15个方法**

| 分类 | RPC方法 | 实现状态 | 说明 |
|------|---------|----------|------|
| **账户管理** | `GetAccountInfo` | ❌ **TODO** | 获取账户信息 |
| | `CreateAccount` | ❌ **TODO** | 创建账户 |
| | `GetAccountList` | ❌ **TODO** | 获取账户列表 |
| | `GetBalance` | ❌ **TODO** | 获取账户余额 |
| **资金操作** | `Recharge` | ❌ **TODO** | 账户充值 |
| | `Withdraw` | ❌ **TODO** | 账户提现 |
| | `FreezeMoney` | ❌ **TODO** | 冻结资金 |
| | `UnfreezeMoney` | ❌ **TODO** | 解冻资金 |
| **积分管理** | `GetIntegralInfo` | ❌ **TODO** | 获取积分信息 |
| | `AddIntegral` | ❌ **TODO** | 增加积分 |
| | `ConsumeIntegral` | ❌ **TODO** | 消费积分 |
| **成长值** | `GetGrowthInfo` | ❌ **TODO** | 获取成长值信息 |
| | `AddGrowth` | ❌ **TODO** | 增加成长值 |
| **日志记录** | `AddMoneyLog` | ❌ **TODO** | 添加资金日志 |
| | `AddIntegralLog` | ❌ **TODO** | 添加积分日志 |
| | `GetAccountLog` | ❌ **TODO** | 获取账户日志 |

---

## 📚 **Dict-Service (字典服务)**

### **🌐 HTTP API 接口 (15个)**

#### **1. 字典管理 API - 5个接口**

| 接口名称 | 方法 | 路径 | 实现状态 | 说明 |
|----------|------|------|----------|------|
| 创建字典 | `POST` | `/api/dict/create` | ❌ **TODO** | 创建新的字典 |
| 更新字典 | `POST` | `/api/dict/update` | ❌ **TODO** | 更新字典信息 |
| 删除字典 | `POST` | `/api/dict/delete` | ❌ **TODO** | 删除字典 |
| 获取字典详情 | `POST` | `/api/dict/get` | ❌ **TODO** | 获取字典详细信息 |
| 字典列表 | `POST` | `/api/dict/list` | ❌ **TODO** | 分页获取字典列表 |

#### **2. 字典分类管理 API - 5个接口**

| 接口名称 | 方法 | 路径 | 实现状态 | 说明 |
|----------|------|------|----------|------|
| 创建字典分类 | `POST` | `/api/dict/category/create` | ❌ **TODO** | 创建新的字典分类 |
| 更新字典分类 | `POST` | `/api/dict/category/update` | ❌ **TODO** | 更新字典分类信息 |
| 删除字典分类 | `POST` | `/api/dict/category/delete` | ❌ **TODO** | 删除字典分类 |
| 获取字典分类详情 | `POST` | `/api/dict/category/get` | ❌ **TODO** | 获取字典分类详细信息 |
| 字典分类列表 | `POST` | `/api/dict/category/list` | ❌ **TODO** | 分页获取字典分类列表 |

#### **3. 字典项管理 API - 5个接口**

| 接口名称 | 方法 | 路径 | 实现状态 | 说明 |
|----------|------|------|----------|------|
| 创建字典项 | `POST` | `/api/dictitem/create` | ✅ **已实现** | 创建新的字典项 |
| 更新字典项 | `POST` | `/api/dictitem/update` | ✅ **已实现** | 更新字典项信息 |
| 删除字典项 | `POST` | `/api/dictitem/delete` | ✅ **已实现** | 删除字典项 |
| 获取字典项详情 | `POST` | `/api/dictitem/get` | ✅ **已实现** | 获取字典项详细信息 |
| 字典项列表 | `POST` | `/api/dictitem/list` | ✅ **已实现** | 分页获取字典项列表 |

### **🔌 RPC 接口 (15个)**

#### **Dict RPC Service - 5个方法**

| RPC方法 | 实现状态 | 说明 |
|---------|----------|------|
| `CreateDict` | ✅ **已实现** | 创建字典 |
| `UpdateDict` | ✅ **已实现** | 更新字典 |
| `DeleteDict` | ✅ **已实现** | 删除字典 |
| `GetDict` | ✅ **已实现** | 获取字典详情 |
| `ListDict` | ✅ **已实现** | 获取字典列表 |

#### **Dict Category RPC Service - 5个方法**

| RPC方法 | 实现状态 | 说明 |
|---------|----------|------|
| `CreateDictCategory` | ✅ **已实现** | 创建字典分类 |
| `UpdateDictCategory` | ✅ **已实现** | 更新字典分类 |
| `DeleteDictCategory` | ✅ **已实现** | 删除字典分类 |
| `GetDictCategory` | ✅ **已实现** | 获取字典分类详情 |
| `ListDictCategory` | ✅ **已实现** | 获取字典分类列表 |

#### **Dict Item RPC Service - 5个方法**

| RPC方法 | 实现状态 | 说明 |
|---------|----------|------|
| `CreateDictItem` | ✅ **已实现** | 创建字典项 |
| `UpdateDictItem` | ✅ **已实现** | 更新字典项 |
| `DeleteDictItem` | ✅ **已实现** | 删除字典项 |
| `GetDictItem` | ✅ **已实现** | 获取字典项详情 |
| `ListDictItem` | ✅ **已实现** | 获取字典项列表 |

---

## 🎯 **开发优先级建议**

### **🔥 高优先级 (核心业务流程)**

1. **Member-Service Account相关接口 (15个)**
   - 账户管理是用户系统的核心功能
   - 充值、提现、余额查询等基础功能

2. **Dict-Service API层业务逻辑 (10个)**
   - Dict API: 5个接口的业务逻辑实现
   - Dict Category API: 5个接口的业务逻辑实现

### **🚀 中优先级 (用户管理功能)**

3. **Member-Service 用户管理接口 (11个)**
   - 用户信息更新、登出等基础功能
   - 实名认证相关接口

### **⭐ 低优先级 (管理功能)**

4. **Member-Service 管理员接口 (10个)**
   - 后台管理功能
   - 审核、统计等高级功能

---

## 📋 **接口测试清单**

### **已实现接口测试**

#### **Member-Service**
- [ ] `POST /api/v1/auth/register` - 用户注册
- [ ] `POST /api/v1/auth/login` - 用户登录  
- [ ] `POST /api/v1/auth/reset-password` - 重置密码
- [ ] `POST /api/v1/auth/refresh` - 刷新Token
- [ ] `GET /api/v1/member/info` - 获取用户信息

#### **Dict-Service**
- [ ] `POST /api/dictitem/create` - 创建字典项
- [ ] `POST /api/dictitem/update` - 更新字典项
- [ ] `POST /api/dictitem/delete` - 删除字典项
- [ ] `POST /api/dictitem/get` - 获取字典项详情
- [ ] `POST /api/dictitem/list` - 字典项列表

---

## 🔧 **技术说明**

### **认证机制**
- **JWT认证**: 大部分接口需要JWT Token
- **管理员权限**: 管理员接口需要额外的AdminAuth中间件
- **Token刷新**: 支持Access Token和Refresh Token机制

### **数据库设计**
- **Member相关表**: member, member_account, member_certification, member_stat, member_cancel
- **Dict相关表**: dict, dict_category, dict_item
- **多租户支持**: 通过tenant_id字段实现
- **软删除**: 通过status字段实现逻辑删除

### **服务架构**
- **API Gateway**: HTTP接口层，处理前端请求
- **RPC服务**: gRPC服务，处理内部服务调用
- **服务发现**: 通过etcd实现服务注册与发现
- **数据存储**: MySQL + Redis

---

## 📝 **更新日志**

- **2024-01-XX**: 初始版本，完成接口梳理
- **待更新**: 根据开发进度持续更新实现状态

---

*本文档会随着开发进度持续更新，请关注最新版本。* 