// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: member.proto

package memberservice

import (
	"context"

	"member/member"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AuditCancelApplicationReq       = member.AuditCancelApplicationReq
	AuditCertificationReq           = member.AuditCertificationReq
	CommonResp                      = member.CommonResp
	CreateMemberReq                 = member.CreateMemberReq
	CreateMemberResp                = member.CreateMemberResp
	DeleteMemberReq                 = member.DeleteMemberReq
	GetCancelStatusReq              = member.GetCancelStatusReq
	GetCancelStatusResp             = member.GetCancelStatusResp
	GetCertificationInfoReq         = member.GetCertificationInfoReq
	GetCertificationInfoResp        = member.GetCertificationInfoResp
	GetMemberInfoReq                = member.GetMemberInfoReq
	GetMemberInfoResp               = member.GetMemberInfoResp
	GetMemberListReq                = member.GetMemberListReq
	GetMemberListResp               = member.GetMemberListResp
	GetMemberStatReq                = member.GetMemberStatReq
	GetMemberStatResp               = member.GetMemberStatResp
	GetPendingCancelListReq         = member.GetPendingCancelListReq
	GetPendingCancelListResp        = member.GetPendingCancelListResp
	GetPendingCertificationListReq  = member.GetPendingCertificationListReq
	GetPendingCertificationListResp = member.GetPendingCertificationListResp
	LoginReq                        = member.LoginReq
	LoginResp                       = member.LoginResp
	LogoutReq                       = member.LogoutReq
	LogoutResp                      = member.LogoutResp
	Member                          = member.Member
	MemberCancel                    = member.MemberCancel
	MemberCertification             = member.MemberCertification
	MemberPublic                    = member.MemberPublic
	MemberStat                      = member.MemberStat
	PageInfo                        = member.PageInfo
	RefreshTokenReq                 = member.RefreshTokenReq
	RefreshTokenResp                = member.RefreshTokenResp
	RegisterReq                     = member.RegisterReq
	RegisterResp                    = member.RegisterResp
	ResetPasswordReq                = member.ResetPasswordReq
	SubmitCancelApplicationReq      = member.SubmitCancelApplicationReq
	SubmitCertificationReq          = member.SubmitCertificationReq
	UpdateMemberInfoReq             = member.UpdateMemberInfoReq
	UpdateMemberStatReq             = member.UpdateMemberStatReq
	ValidateTokenReq                = member.ValidateTokenReq
	ValidateTokenResp               = member.ValidateTokenResp

	MemberService interface {
		// 用户认证相关
		Register(ctx context.Context, in *RegisterReq, opts ...grpc.CallOption) (*RegisterResp, error)
		Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error)
		Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error)
		RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenResp, error)
		ResetPassword(ctx context.Context, in *ResetPasswordReq, opts ...grpc.CallOption) (*CommonResp, error)
		ValidateToken(ctx context.Context, in *ValidateTokenReq, opts ...grpc.CallOption) (*ValidateTokenResp, error)
		// 用户信息管理
		GetMemberInfo(ctx context.Context, in *GetMemberInfoReq, opts ...grpc.CallOption) (*GetMemberInfoResp, error)
		UpdateMemberInfo(ctx context.Context, in *UpdateMemberInfoReq, opts ...grpc.CallOption) (*CommonResp, error)
		GetMemberList(ctx context.Context, in *GetMemberListReq, opts ...grpc.CallOption) (*GetMemberListResp, error)
		DeleteMember(ctx context.Context, in *DeleteMemberReq, opts ...grpc.CallOption) (*CommonResp, error)
		CreateMember(ctx context.Context, in *CreateMemberReq, opts ...grpc.CallOption) (*CreateMemberResp, error)
		// 实名认证管理
		GetCertificationInfo(ctx context.Context, in *GetCertificationInfoReq, opts ...grpc.CallOption) (*GetCertificationInfoResp, error)
		SubmitCertification(ctx context.Context, in *SubmitCertificationReq, opts ...grpc.CallOption) (*CommonResp, error)
		AuditCertification(ctx context.Context, in *AuditCertificationReq, opts ...grpc.CallOption) (*CommonResp, error)
		GetPendingCertificationList(ctx context.Context, in *GetPendingCertificationListReq, opts ...grpc.CallOption) (*GetPendingCertificationListResp, error)
		// 用户统计
		GetMemberStat(ctx context.Context, in *GetMemberStatReq, opts ...grpc.CallOption) (*GetMemberStatResp, error)
		UpdateMemberStat(ctx context.Context, in *UpdateMemberStatReq, opts ...grpc.CallOption) (*CommonResp, error)
		// 注销申请
		SubmitCancelApplication(ctx context.Context, in *SubmitCancelApplicationReq, opts ...grpc.CallOption) (*CommonResp, error)
		GetCancelStatus(ctx context.Context, in *GetCancelStatusReq, opts ...grpc.CallOption) (*GetCancelStatusResp, error)
		AuditCancelApplication(ctx context.Context, in *AuditCancelApplicationReq, opts ...grpc.CallOption) (*CommonResp, error)
		GetPendingCancelList(ctx context.Context, in *GetPendingCancelListReq, opts ...grpc.CallOption) (*GetPendingCancelListResp, error)
	}

	defaultMemberService struct {
		cli zrpc.Client
	}
)

func NewMemberService(cli zrpc.Client) MemberService {
	return &defaultMemberService{
		cli: cli,
	}
}

// 用户认证相关
func (m *defaultMemberService) Register(ctx context.Context, in *RegisterReq, opts ...grpc.CallOption) (*RegisterResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.Register(ctx, in, opts...)
}

func (m *defaultMemberService) Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.Login(ctx, in, opts...)
}

func (m *defaultMemberService) Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.Logout(ctx, in, opts...)
}

func (m *defaultMemberService) RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.RefreshToken(ctx, in, opts...)
}

func (m *defaultMemberService) ResetPassword(ctx context.Context, in *ResetPasswordReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.ResetPassword(ctx, in, opts...)
}

func (m *defaultMemberService) ValidateToken(ctx context.Context, in *ValidateTokenReq, opts ...grpc.CallOption) (*ValidateTokenResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.ValidateToken(ctx, in, opts...)
}

// 用户信息管理
func (m *defaultMemberService) GetMemberInfo(ctx context.Context, in *GetMemberInfoReq, opts ...grpc.CallOption) (*GetMemberInfoResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.GetMemberInfo(ctx, in, opts...)
}

func (m *defaultMemberService) UpdateMemberInfo(ctx context.Context, in *UpdateMemberInfoReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.UpdateMemberInfo(ctx, in, opts...)
}

func (m *defaultMemberService) GetMemberList(ctx context.Context, in *GetMemberListReq, opts ...grpc.CallOption) (*GetMemberListResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.GetMemberList(ctx, in, opts...)
}

func (m *defaultMemberService) DeleteMember(ctx context.Context, in *DeleteMemberReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.DeleteMember(ctx, in, opts...)
}

func (m *defaultMemberService) CreateMember(ctx context.Context, in *CreateMemberReq, opts ...grpc.CallOption) (*CreateMemberResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.CreateMember(ctx, in, opts...)
}

// 实名认证管理
func (m *defaultMemberService) GetCertificationInfo(ctx context.Context, in *GetCertificationInfoReq, opts ...grpc.CallOption) (*GetCertificationInfoResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.GetCertificationInfo(ctx, in, opts...)
}

func (m *defaultMemberService) SubmitCertification(ctx context.Context, in *SubmitCertificationReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.SubmitCertification(ctx, in, opts...)
}

func (m *defaultMemberService) AuditCertification(ctx context.Context, in *AuditCertificationReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.AuditCertification(ctx, in, opts...)
}

func (m *defaultMemberService) GetPendingCertificationList(ctx context.Context, in *GetPendingCertificationListReq, opts ...grpc.CallOption) (*GetPendingCertificationListResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.GetPendingCertificationList(ctx, in, opts...)
}

// 用户统计
func (m *defaultMemberService) GetMemberStat(ctx context.Context, in *GetMemberStatReq, opts ...grpc.CallOption) (*GetMemberStatResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.GetMemberStat(ctx, in, opts...)
}

func (m *defaultMemberService) UpdateMemberStat(ctx context.Context, in *UpdateMemberStatReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.UpdateMemberStat(ctx, in, opts...)
}

// 注销申请
func (m *defaultMemberService) SubmitCancelApplication(ctx context.Context, in *SubmitCancelApplicationReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.SubmitCancelApplication(ctx, in, opts...)
}

func (m *defaultMemberService) GetCancelStatus(ctx context.Context, in *GetCancelStatusReq, opts ...grpc.CallOption) (*GetCancelStatusResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.GetCancelStatus(ctx, in, opts...)
}

func (m *defaultMemberService) AuditCancelApplication(ctx context.Context, in *AuditCancelApplicationReq, opts ...grpc.CallOption) (*CommonResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.AuditCancelApplication(ctx, in, opts...)
}

func (m *defaultMemberService) GetPendingCancelList(ctx context.Context, in *GetPendingCancelListReq, opts ...grpc.CallOption) (*GetPendingCancelListResp, error) {
	client := member.NewMemberServiceClient(m.cli.Conn())
	return client.GetPendingCancelList(ctx, in, opts...)
}
