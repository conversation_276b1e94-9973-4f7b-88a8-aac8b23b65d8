﻿package model

import (
	"errors"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// DictItem 字典项模型
type DictItem struct {
	ID          int64          `gorm:"primaryKey;autoIncrement"`
	DictID      int64          `gorm:"not null;index"`
	CategoryID  int64          `gorm:"not null;index"`
	Code        string         `gorm:"type:varchar(50);not null"`
	Name        string         `gorm:"type:varchar(100);not null"`
	Status      int32          `gorm:"type:tinyint;not null;default:1;comment:'状态 0:禁用 1:启用'"`
	CreatedTime time.Time      `gorm:"not null;autoCreateTime"`
	UpdatedTime time.Time      `gorm:"not null;autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `gorm:"index"`
}

// TableName 设置表名
func (DictItem) TableName() string {
	return "dict_item"
}

// DictItemModel 字典项模型结构
type DictItemModel struct {
	db *gorm.DB
}

// NewDictItemModel 创建字典项模型
func NewDictItemModel(db *gorm.DB) *DictItemModel {
	return &DictItemModel{
		db: db,
	}
}

// FindByID 根据ID查询字典项
func (m *DictItemModel) FindByID(id int64) (*DictItem, error) {
	var item DictItem
	result := m.db.First(&item, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典项id=" + strconv.Itoa(int(id)) + "不存在")
		}
		return nil, result.Error
	}
	return &item, nil
}

// FindByDictID 根据字典ID查询字典项列表
func (m *DictItemModel) FindByDictID(dictID int64) ([]*DictItem, error) {
	var items []*DictItem
	result := m.db.Where("dict_id = ? AND status = 1", dictID).Order("created_time ASC").Find(&items)
	if result.Error != nil {
		return nil, result.Error
	}
	return items, nil
}

// FindByCategoryID 根据分类ID查询字典项列表
func (m *DictItemModel) FindByCategoryID(categoryID int64) ([]*DictItem, error) {
	var items []*DictItem
	result := m.db.Where("category_id = ? AND status = 1", categoryID).Order("created_time ASC").Find(&items)
	if result.Error != nil {
		return nil, result.Error
	}
	return items, nil
}

// FindByDictCodeAndItemCode 根据字典编码和项编码查询字典项
func (m *DictItemModel) FindByDictCodeAndItemCode(dictCode, itemCode string) (*DictItem, error) {
	var item DictItem
	result := m.db.Table("dict_item di").
		Joins("JOIN dict d ON di.dict_id = d.id").
		Where("d.code = ? AND di.code = ? AND di.status = 1 AND d.status = 1", dictCode, itemCode).
		First(&item)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典项不存在")
		}
		return nil, result.Error
	}
	return &item, nil
}

// Create 创建字典项
func (m *DictItemModel) Create(item *DictItem) error {
	return m.db.Create(item).Error
}

// Update 更新字典项
func (m *DictItemModel) Update(item *DictItem) error {
	return m.db.Save(item).Error
}

// Delete 删除字典项
func (m *DictItemModel) Delete(id int64) error {
	return m.db.Delete(&DictItem{}, id).Error
}

// List 分页查询字典项列表
func (m *DictItemModel) List(page, pageSize int32, dictID, categoryID int64, code, name string, status int32) ([]*DictItem, int64, error) {
	var items []*DictItem
	var total int64

	query := m.db.Model(&DictItem{})

	// 条件查询
	if dictID > 0 {
		query = query.Where("dict_id = ?", dictID)
	}
	if categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}
	if code != "" {
		query = query.Where("code LIKE ?", "%"+code+"%")
	}
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if status != -1 {
		query = query.Where("status = ?", status)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := int((page - 1) * pageSize)
	if err := query.Offset(offset).Limit(int(pageSize)).Order("created_time DESC").Find(&items).Error; err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

// CheckCodeExists 检查字典项编码在指定字典和分类下是否存在
func (m *DictItemModel) CheckCodeExists(dictID, categoryID int64, code string, excludeID int64) (bool, error) {
	var count int64
	query := m.db.Model(&DictItem{}).Where("dict_id = ? AND category_id = ? AND code = ?", dictID, categoryID, code)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	if err := query.Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// 获取获取最后一条记录
func (m *DictItemModel) GetLast() (*DictItem, error) {
	var item DictItem
	result := m.db.Last(&item)
	if result.Error != nil {
		return nil, result.Error
	}
	return &item, nil
}
