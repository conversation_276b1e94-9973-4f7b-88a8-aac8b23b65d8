package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddIntegralLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddIntegralLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddIntegralLogic {
	return &AddIntegralLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *AddIntegralLogic) AddIntegral(in *account.AddIntegralReq) (*account.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &account.CommonResp{}, nil
}
