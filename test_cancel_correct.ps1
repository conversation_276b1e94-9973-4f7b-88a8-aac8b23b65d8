$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTYyOTM1NjMsImlhdCI6MTc1NTY4ODc2MywibWVtYmVyX2lkIjozLCJ0eXBlIjoxLCJ1c2VybmFtZSI6Inl5In0.kRJUsnqeTk54jWiWL0Qjy0zFTihMHgj68Lz9vvw3AWk'
    'Content-Type' = 'application/json'
}

$body = @{
    'content' = '由于个人原因，申请注销账户'
} | ConvertTo-Json

Write-Host "测试注销申请接口..." -ForegroundColor Yellow
Write-Host "请求URL: http://*************:8005/api/v1/member/cancel" -ForegroundColor Cyan
Write-Host "请求Body: $body" -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri 'http://*************:8005/api/v1/member/cancel' -Method Post -Headers $headers -Body $body -UseBasicParsing
    Write-Host "请求成功!" -ForegroundColor Green
    Write-Host "响应内容:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "请求失败:" -ForegroundColor Red
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "响应内容: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "无法读取响应内容" -ForegroundColor Red
        }
    }
} 