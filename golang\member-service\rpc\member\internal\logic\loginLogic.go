package logic

import (
	"context"
	"member/middleware/JWT"

	"github.com/duke-git/lancet/v2/cryptor"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type LoginLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginLogic {
	return &LoginLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LoginLogic) Login(in *member.LoginReq) (*member.LoginResp, error) {
	// 1) 支持用户名/手机号
	var m *member.Member
	if in.LoginType == 2 && in.Mobile != "" { // 2: 手机登录
		mm, _ := l.svcCtx.MemberModel.FindByMobile(in.Mobile)
		if mm != nil {
			m = &member.Member{
				Id: mm.ID, 
				Username: mm.Username, 
				Mobile: mm.Mobile, 
				Email: mm.Email, 
				Nickname: mm.Nickname, 
				HeadPortrait: mm.HeadPortrait, 
				Type: mm.Type,  // 添加用户类型
				Status: mm.Status, 
				CreatedAt: mm.CreatedAt, 
				UpdatedAt: mm.UpdatedAt,
			}
			// 密码校验
			if cryptor.HmacSha256(in.Password, l.svcCtx.Config.JwtAuth.AccessSecret) != mm.PasswordHash {
				return &member.LoginResp{Code: 401, Message: "账号或密码错误"}, nil
			}
		} else {
			return &member.LoginResp{Code: 404, Message: "账号不存在"}, nil
		}
	} else { // 默认用户名登录
		mm, _ := l.svcCtx.MemberModel.FindByUsername(in.Username)
		if mm != nil {
			m = &member.Member{
				Id: mm.ID, 
				Username: mm.Username, 
				Mobile: mm.Mobile, 
				Email: mm.Email, 
				Nickname: mm.Nickname, 
				HeadPortrait: mm.HeadPortrait, 
				Type: mm.Type,  // 添加用户类型
				Status: mm.Status, 
				CreatedAt: mm.CreatedAt, 
				UpdatedAt: mm.UpdatedAt,
			}
			if cryptor.HmacSha256(in.Password, l.svcCtx.Config.JwtAuth.AccessSecret) != mm.PasswordHash {
				return &member.LoginResp{Code: 401, Message: "账号或密码错误"}, nil
			}
		} else {
			return &member.LoginResp{Code: 404, Message: "账号不存在"}, nil
		}
	}

	// 2) 使用简化的JWT生成函数
	userInfo := JWT.UserInfo{
		MemberID:   m.Id,
		Username:   m.Username,
		MemberType: m.Type,
	}

	accessToken, refreshToken, err := JWT.GenerateTokenPair(
		l.svcCtx.Config.JwtAuth.AccessSecret,
		l.svcCtx.Config.JwtAuth.AccessExpire,
		userInfo,
	)
	if err != nil {
		return &member.LoginResp{Code: 500, Message: "签发令牌失败"}, nil
	}

	return &member.LoginResp{
		Code:         200,
		Message:      "登录成功",
		MemberInfo:   m,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	}, nil
}
