package logic

import (
	"context"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuditCertificationLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAuditCertificationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuditCertificationLogic {
	return &AuditCertificationLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *AuditCertificationLogic) AuditCertification(in *member.AuditCertificationReq) (*member.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &member.CommonResp{}, nil
}
