package logic

import (
	"context"

	"member/internal/svc"
	"member/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPendingCancelListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetPendingCancelListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPendingCancelListLogic {
	return &GetPendingCancelListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetPendingCancelListLogic) GetPendingCancelList(in *member.GetPendingCancelListReq) (*member.GetPendingCancelListResp, error) {
	// todo: add your logic here and delete this line

	return &member.GetPendingCancelListResp{}, nil
}
