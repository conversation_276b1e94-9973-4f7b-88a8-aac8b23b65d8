package logic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
)

type GetMemberInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMemberInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberInfoLogic {
	return &GetMemberInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户信息
func (l *GetMemberInfoLogic) GetMemberInfo(in *member.GetMemberInfoReq) (*member.GetMemberInfoResp, error) {
	// 参数校验
	if in.MemberId <= 0 {
		return &member.GetMemberInfoResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	// 查询用户信息
	memberData, err := l.svcCtx.MemberModel.FindByID(in.MemberId)
	if err != nil {
		logx.Errorf("查询用户信息失败: %v", err)
		return &member.GetMemberInfoResp{
			Code:    500,
			Message: "查询用户信息失败",
		}, nil
	}

	if memberData == nil {
		return &member.GetMemberInfoResp{
			Code:    404,
			Message: "用户不存在",
		}, nil
	}

	// 检查用户状态
	if memberData.Status == -1 {
		return &member.GetMemberInfoResp{
			Code:    404,
			Message: "用户已被删除",
		}, nil
	}

	// 构造响应数据（不返回敏感信息）
	memberInfo := &member.Member{
		Id:         memberData.ID,
		MerchantId: memberData.MerchantID,
		StoreId:    memberData.StoreID,
		Username:   memberData.Username,
		// 敏感字段不返回
		// PasswordHash:        "",
		// AuthKey:             "",
		// PasswordResetToken:  "",
		// MobileResetToken:    "",
		Type:                memberData.Type,
		Realname:            memberData.Realname,
		Nickname:            memberData.Nickname,
		HeadPortrait:        memberData.HeadPortrait,
		Gender:              memberData.Gender,
		Qq:                  memberData.QQ,
		Email:               memberData.Email,
		Birthday:            func() string {
			if memberData.Birthday != nil {
				return *memberData.Birthday
			}
			return ""
		}(),
		ProvinceId:          memberData.ProvinceID,
		CityId:              memberData.CityID,
		AreaId:              memberData.AreaID,
		Address:             memberData.Address,
		Mobile:              memberData.Mobile,
		TelNo:               memberData.TelNo,
		BgImage:             memberData.BGImage,
		Description:         memberData.Description,
		VisitCount:          memberData.VisitCount,
		LastTime:            memberData.LastTime,
		LastIp:              memberData.LastIP,
		Role:                memberData.Role,
		CurrentLevel:        memberData.CurrentLevel,
		LevelExpirationTime: memberData.LevelExpireTime,
		LevelBuyType:        memberData.LevelBuyType,
		Pid:                 memberData.PID,
		Level:               memberData.Level,
		Tree:                memberData.Tree,
		PromoterCode:        memberData.PromoterCode,
		CertificationType:   memberData.CertificationType,
		Source:              memberData.Source,
		Status:              memberData.Status,
		CreatedAt:           memberData.CreatedAt,
		UpdatedAt:           memberData.UpdatedAt,
		RegionId:            memberData.RegionID,
	}

	return &member.GetMemberInfoResp{
		Code:    200,
		Message: "获取用户信息成功",
		Data:    memberInfo,
	}, nil
}
