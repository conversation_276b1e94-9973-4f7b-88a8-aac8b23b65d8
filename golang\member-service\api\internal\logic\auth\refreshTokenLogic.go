package auth

import (
	"context"
	"fmt"
	memberpb "member/member"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RefreshTokenLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 刷新token
func NewRefreshTokenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RefreshTokenLogic {
	return &RefreshTokenLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RefreshTokenLogic) RefreshToken(req *types.RefreshTokenReq) (resp *types.RefreshTokenResp, err error) {

	// 调用 Member RPC 刷新token 避免重复登录
	rpcResp, err := l.svcCtx.MemberRpc.RefreshToken(l.ctx, &memberpb.RefreshTokenReq{
		RefreshToken: req.RefreshToken,
	})
	if err != nil {
		return nil, fmt.Errorf("refresh token rpc failed: %w", err)
	}

	return &types.RefreshTokenResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
		Data: types.RefreshTokenData{
			AccessToken:  rpcResp.AccessToken,
			RefreshToken: rpcResp.RefreshToken,
			ExpiresIn:    l.svcCtx.Config.JwtAuth.AccessExpire,
		},
	}, nil
}
