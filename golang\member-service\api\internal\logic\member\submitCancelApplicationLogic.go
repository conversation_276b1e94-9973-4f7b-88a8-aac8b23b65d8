package member

import (
	"context"
	"encoding/json"
	"strconv"
	memberpb "member/member"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SubmitCancelApplicationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 提交注销申请
func NewSubmitCancelApplicationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubmitCancelApplicationLogic {
	return &SubmitCancelApplicationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SubmitCancelApplicationLogic) SubmitCancelApplication(req *types.SubmitCancelReq) (resp *types.CommonResp, err error) {
	// 从JWT token中获取当前用户ID
	var memberId int64
	memberIdvalue := l.ctx.Value("member_id")
	logx.Infof("JWT解析结果 - member_id value: %v, type: %T", memberIdvalue, memberIdvalue)
	
	if memberIdvalue == nil {
		logx.Errorf("JWT token中未找到member_id")
		return &types.CommonResp{
			Code:    401,
			Message: "无法获取用户身份信息",
		}, nil
	}

	// 处理不同类型的member_id值
	switch v := memberIdvalue.(type) {
	case int64:
		memberId = v
		logx.Infof("member_id解析为int64: %d", memberId)
	case float64:
		memberId = int64(v)
		logx.Infof("member_id解析为float64转int64: %d", memberId)
	case string:
		if parsed, parseErr := strconv.ParseInt(v, 10, 64); parseErr == nil {
			memberId = parsed
			logx.Infof("member_id解析为string转int64: %d", memberId)
		} else {
			logx.Errorf("用户身份信息格式错误: %v", parseErr)
			return &types.CommonResp{
				Code:    400,
				Message: "用户身份信息格式错误",
			}, nil
		}
	case json.Number:
		if parsed, parseErr := v.Int64(); parseErr == nil {
			memberId = parsed
			logx.Infof("member_id解析为json.Number转int64: %d", memberId)
		} else {
			logx.Errorf("用户身份信息格式错误: %v", parseErr)
			return &types.CommonResp{
				Code:    400,
				Message: "用户身份信息格式错误",
			}, nil
		}
	default:
		logx.Errorf("未知的member_id类型: %T, 值: %v", memberIdvalue, memberIdvalue)
		return &types.CommonResp{
			Code:    400,
			Message: "用户身份信息类型错误",
		}, nil
	}

	logx.Infof("开始处理用户ID=%d的注销申请，内容: %s", memberId, req.Content)

	// 调用RPC服务提交注销申请
	rpcResp, err := l.svcCtx.MemberRpc.SubmitCancelApplication(l.ctx, &memberpb.SubmitCancelApplicationReq{
		MemberId: memberId,
		Content:  req.Content,
	})

	if err != nil {
		logx.Errorf("调用RPC服务失败: %v", err)
		return &types.CommonResp{
			Code:    500,
			Message: "提交注销申请失败",
		}, nil
	}

	logx.Infof("RPC调用成功，响应: code=%d, message=%s", rpcResp.Code, rpcResp.Message)

	return &types.CommonResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
	}, nil
}
