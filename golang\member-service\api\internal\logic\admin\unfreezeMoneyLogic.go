package admin

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UnfreezeMoneyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 解冻用户资金
func NewUnfreezeMoneyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UnfreezeMoneyLogic {
	return &UnfreezeMoneyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UnfreezeMoneyLogic) UnfreezeMoney(req *types.UnfreezeMoneyReq) (resp *types.CommonResp, err error) {
	// todo: add your logic here and delete this line

	return
}
