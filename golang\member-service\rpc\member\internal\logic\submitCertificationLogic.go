package logic

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
	"member/model"
)

type SubmitCertificationLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSubmitCertificationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SubmitCertificationLogic {
	return &SubmitCertificationLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SubmitCertificationLogic) SubmitCertification(in *member.SubmitCertificationReq) (*member.CommonResp, error) {

	// 参数校验
	if in.MemberId <= 0 {
		return nil, fmt.Errorf("用户身份校验失败,请重新上传文件")
	}

	// 检查该用户是否已经有认证申请
	existingCert, err := l.svcCtx.MemberCertificationModel.FindByMemberID(in.MemberId)
	if err == nil && existingCert != nil {
		// 如果已存在认证申请，更新现有记录
		err = l.svcCtx.MemberCertificationModel.Update(existingCert.ID, &model.MemberCertification{
			Realname:     in.Realname,
			IdentityCard: in.IdentityCard,
			FrontImage:   in.IdentityCardFront,
			BackImage:    in.IdentityCardBack,
			Address:      in.Address,
			Status:       0, // 重新提交，状态重置为待审核
		})
	} else {
		// 如果不存在，创建新的认证申请
		err = l.svcCtx.MemberCertificationModel.Create(&model.MemberCertification{
			MemberID:     in.MemberId,
			Realname:     in.Realname,
			IdentityCard: in.IdentityCard,
			FrontImage:   in.IdentityCardFront,
			BackImage:    in.IdentityCardBack,
			Address:      in.Address,
			Status:       0, // 待审核
		})
	}
	if err != nil {
		return &member.CommonResp{
			Code:    500,
			Message: "提交实名认证信息失败,请稍后重新尝试",
		}, nil
	}
	return &member.CommonResp{
		Code:    200,
		Message: "提交实名认证信息成功",
	}, nil
}
