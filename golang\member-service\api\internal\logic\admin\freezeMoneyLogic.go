package admin

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type FreezeMoneyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 冻结用户资金
func NewFreezeMoneyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FreezeMoneyLogic {
	return &FreezeMoneyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FreezeMoneyLogic) FreezeMoney(req *types.FreezeMoneyReq) (resp *types.CommonResp, err error) {
	// todo: add your logic here and delete this line

	return
}
