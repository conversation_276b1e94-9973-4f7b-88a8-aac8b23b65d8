package logic

import (
	"context"

	"account/account"
	"account/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UnfreezeMoneyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUnfreezeMoneyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UnfreezeMoneyLogic {
	return &UnfreezeMoneyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UnfreezeMoneyLogic) UnfreezeMoney(in *account.UnfreezeMoneyReq) (*account.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &account.CommonResp{}, nil
}
