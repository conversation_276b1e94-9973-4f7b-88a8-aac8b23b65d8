package logic

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
	"member/model"
)

type GetMemberStatLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMemberStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberStatLogic {
	return &GetMemberStatLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 用户统计
func (l *GetMemberStatLogic) GetMemberStat(in *member.GetMemberStatReq) (*member.GetMemberStatResp, error) {
	// 参数验证
	if in.MemberId <= 0 {
		return &member.GetMemberStatResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	// 查询用户统计信息
	memberStat, err := l.svcCtx.MemberStatModel.FindByMemberID(in.MemberId)
	if err != nil {
		// 如果用户统计记录不存在，创建一个默认的统计记录
		if err.Error() == fmt.Sprintf("会员ID=%d的统计不存在", in.MemberId) {
			// 检查用户是否存在
			memberInfo, memberErr := l.svcCtx.MemberModel.FindByID(in.MemberId)
			if memberErr != nil {
				return &member.GetMemberStatResp{
					Code:    404,
					Message: "用户不存在",
				}, nil
			}

			// 创建默认统计记录
			newStat := &model.MemberStat{
				MemberID:     in.MemberId,
				MerchantID:   memberInfo.MerchantID,
				StoreID:      memberInfo.StoreID,
				MemberType:   memberInfo.Type,
				NiceNum:      0,
				DisagreeNum:  0,
				TransmitNum:  0,
				CommentNum:   0,
				CollectNum:   0,
				ReportNum:    0,
				RecommendNum: 0,
				FollowNum:    0,
				AllowedNum:   0,
				View:         0,
				Status:       1,
				CreatedAt:    time.Now().Unix(),
				UpdatedAt:    time.Now().Unix(),
			}

			createErr := l.svcCtx.MemberStatModel.Create(newStat)
			if createErr != nil {
				logx.Errorf("创建用户统计记录失败: %v", createErr)
				return &member.GetMemberStatResp{
					Code:    500,
					Message: "创建用户统计记录失败",
				}, nil
			}

			memberStat = newStat
		} else {
			logx.Errorf("查询用户统计信息失败: %v", err)
			return &member.GetMemberStatResp{
				Code:    500,
				Message: "查询用户统计信息失败",
			}, nil
		}
	}

	// 返回统计信息
	return &member.GetMemberStatResp{
		Code:    200,
		Message: "获取用户统计信息成功",
		Data: &member.MemberStat{
			Id:           memberStat.ID,
			MerchantId:   memberStat.MerchantID,
			StoreId:      memberStat.StoreID,
			MemberId:     memberStat.MemberID,
			MemberType:   memberStat.MemberType,
			NiceNum:      memberStat.NiceNum,
			DisagreeNum:  memberStat.DisagreeNum,
			TransmitNum:  memberStat.TransmitNum,
			CommentNum:   memberStat.CommentNum,
			CollectNum:   memberStat.CollectNum,
			ReportNum:    memberStat.ReportNum,
			RecommendNum: memberStat.RecommendNum,
			FollowNum:    memberStat.FollowNum,
			AllowedNum:   memberStat.AllowedNum,
			View:         memberStat.View,
			Status:       memberStat.Status,
			CreatedAt:    memberStat.CreatedAt,
			UpdatedAt:    memberStat.UpdatedAt,
		},
	}, nil
}
