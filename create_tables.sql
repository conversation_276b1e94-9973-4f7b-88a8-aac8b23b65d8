-- 创建会员服务需要的基本表
USE mp_db;

-- 会员表
CREATE TABLE IF NOT EXISTS `member` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) DEFAULT '0' COMMENT '商户ID',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `username` varchar(20) NOT NULL DEFAULT '' COMMENT '账号',
  `password_hash` varchar(150) NOT NULL DEFAULT '' COMMENT '密码',
  `auth_key` varchar(32) NOT NULL DEFAULT '' COMMENT '授权令牌',
  `password_reset_token` varchar(150) DEFAULT '' COMMENT '密码重置令牌',
  `mobile_reset_token` varchar(150) DEFAULT '' COMMENT '手机号码重置令牌',
  `type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `realname` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `nickname` varchar(60) DEFAULT '' COMMENT '昵称',
  `head_portrait` char(150) DEFAULT '' COMMENT '头像',
  `gender` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '性别[0:未知;1:男;2:女]',
  `qq` varchar(20) DEFAULT '' COMMENT 'qq',
  `email` varchar(60) DEFAULT '' COMMENT '邮箱',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `province_id` int(11) DEFAULT '0' COMMENT '省',
  `city_id` int(11) DEFAULT '0' COMMENT '城市',
  `area_id` int(11) DEFAULT '0' COMMENT '地区',
  `address` varchar(100) DEFAULT '' COMMENT '默认地址',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号码',
  `tel_no` varchar(20) DEFAULT '' COMMENT '电话号码',
  `bg_image` varchar(200) DEFAULT '' COMMENT '个人背景图',
  `description` varchar(200) DEFAULT '' COMMENT '个人说明',
  `visit_count` smallint(5) UNSIGNED DEFAULT '0' COMMENT '访问次数',
  `last_time` int(11) DEFAULT '0' COMMENT '最后一次登录时间',
  `last_ip` varchar(40) DEFAULT '' COMMENT '最后一次登录ip',
  `role` smallint(6) DEFAULT '10' COMMENT '权限',
  `current_level` tinyint(4) DEFAULT '1' COMMENT '当前级别',
  `level_expiration_time` int(11) DEFAULT '0' COMMENT '等级到期时间',
  `level_buy_type` tinyint(4) DEFAULT '1' COMMENT '1:赠送;2:购买',
  `pid` int(10) UNSIGNED DEFAULT '0' COMMENT '上级id',
  `level` tinyint(3) UNSIGNED DEFAULT '1' COMMENT '级别',
  `tree` varchar(2000) DEFAULT '' COMMENT '树',
  `promoter_code` varchar(50) DEFAULT '' COMMENT '推广码',
  `certification_type` tinyint(4) DEFAULT '0' COMMENT '认证类型',
  `source` varchar(50) DEFAULT '' COMMENT '注册来源',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间',
  `region_id` int(11) DEFAULT '0' COMMENT '数据权限',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `mobile` (`mobile`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员表';

-- 会员账户表
CREATE TABLE IF NOT EXISTS `member_account` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `user_money` decimal(10,2) DEFAULT '0.00' COMMENT '当前余额',
  `accumulate_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计余额',
  `give_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计赠送余额',
  `consume_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计消费金额',
  `frozen_money` decimal(10,2) DEFAULT '0.00' COMMENT '冻结金额',
  `user_integral` int(11) DEFAULT '0' COMMENT '当前积分',
  `accumulate_integral` int(11) DEFAULT '0' COMMENT '累计积分',
  `give_integral` int(11) DEFAULT '0' COMMENT '累计赠送积分',
  `consume_integral` decimal(10,2) DEFAULT '0.00' COMMENT '累计消费积分',
  `frozen_integral` int(11) DEFAULT '0' COMMENT '冻结积分',
  `user_growth` int(11) DEFAULT '0' COMMENT '当前成长值',
  `accumulate_growth` int(11) DEFAULT '0' COMMENT '累计成长值',
  `consume_growth` int(11) DEFAULT '0' COMMENT '累计消费成长值',
  `frozen_growth` int(11) DEFAULT '0' COMMENT '冻结成长值',
  `economize_money` decimal(10,2) DEFAULT '0.00' COMMENT '已节约金额',
  `accumulate_drawn_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计提现',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_账户表';

-- 会员实名认证表
CREATE TABLE IF NOT EXISTS `member_certification` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '用户类型',
  `realname` varchar(100) DEFAULT '' COMMENT '真实姓名',
  `identity_card` varchar(50) DEFAULT '' COMMENT '身份证号码',
  `identity_card_front` varchar(200) DEFAULT '' COMMENT '身份证国徽面',
  `identity_card_back` varchar(200) DEFAULT '' COMMENT '身份证人像面',
  `gender` varchar(10) DEFAULT '' COMMENT '性别',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `front_is_fake` tinyint(4) DEFAULT '0' COMMENT '正面是否是复印件',
  `back_is_fake` tinyint(4) DEFAULT '0' COMMENT '背面是否是复印件',
  `nationality` varchar(100) DEFAULT '' COMMENT '民族',
  `address` varchar(255) DEFAULT '' COMMENT '地址',
  `start_date` date DEFAULT NULL COMMENT '有效期起始时间',
  `end_date` date DEFAULT NULL COMMENT '有效期结束时间',
  `issue` varchar(200) DEFAULT '' COMMENT '签发机关',
  `is_self` tinyint(4) DEFAULT '0' COMMENT '自己认证',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_实名认证';

-- 会员注销申请表
CREATE TABLE IF NOT EXISTS `member_cancel` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '会员id',
  `content` text COMMENT '申请内容',
  `audit_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审核状态[0:申请;1通过;-1失败]',
  `audit_time` int(10) UNSIGNED DEFAULT '0' COMMENT '审核时间',
  `refusal_cause` varchar(200) DEFAULT '' COMMENT '拒绝原因',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态',
  `created_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_注销申请';

-- 会员积分等变动表
CREATE TABLE IF NOT EXISTS `member_credits_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(50) DEFAULT '' COMMENT '应用id',
  `merchant_id` int(10) UNSIGNED DEFAULT '0' COMMENT '商户id',
  `store_id` int(10) UNSIGNED DEFAULT '0' COMMENT '店铺ID',
  `member_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户id',
  `member_type` tinyint(4) DEFAULT '1' COMMENT '1:会员;2:后台管理员;3:商家管理员',
  `pay_type` tinyint(4) DEFAULT '0' COMMENT '支付类型',
  `type` varchar(50) NOT NULL DEFAULT '' COMMENT '变动类型[integral:积分;money:余额]',
  `group` varchar(50) DEFAULT '' COMMENT '变动的组别',
  `old_num` decimal(10,2) DEFAULT '0.00' COMMENT '之前的数据',
  `new_num` decimal(10,2) DEFAULT '0.00' COMMENT '变动后的数据',
  `num` decimal(10,2) DEFAULT '0.00' COMMENT '变动的数据',
  `remark` varchar(200) DEFAULT '' COMMENT '备注',
  `ip` varchar(50) DEFAULT '' COMMENT 'ip地址',
  `map_id` int(11) DEFAULT '0' COMMENT '关联id',
  `is_addon` tinyint(3) UNSIGNED DEFAULT '0' COMMENT '是否插件',
  `addon_name` varchar(200) DEFAULT '' COMMENT '插件名称',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态[-1:删除;0:禁用;1启用]',
  `created_at` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
  `updated_at` int(10) UNSIGNED DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员_积分等变动表';
