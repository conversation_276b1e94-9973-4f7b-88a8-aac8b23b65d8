// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type AuditCancelReq struct {
	Id           int64  `json:"id"`
	AuditStatus  int    `json:"audit_status"`           // 1:通过 -1:拒绝
	RefusalCause string `json:"refusal_cause,optional"` // 拒绝原因
	Remark       string `json:"remark,optional"`        // 审核备注
}

type AuditCertificationReq struct {
	Id           int64  `json:"id"`
	Status       int    `json:"status"`                 // 1:通过 -1:拒绝
	RejectReason string `json:"reject_reason,optional"` // 拒绝原因
	Remark       string `json:"remark,optional"`
}

type BalanceData struct {
	UserMoney   float64 `json:"user_money"`
	FrozenMoney float64 `json:"frozen_money"`
	TotalMoney  float64 `json:"total_money"`
}

type BasePageReq struct {
	Page     int `form:"page,default=1"`
	PageSize int `form:"page_size,default=10"`
}

type BaseUserInfo struct {
	Id           int64  `json:"id"`
	Username     string `json:"username"`
	Nickname     string `json:"nickname"`
	Realname     string `json:"realname"`
	Mobile       string `json:"mobile"`
	Email        string `json:"email"`
	HeadPortrait string `json:"head_portrait"`
	Gender       int    `json:"gender"` // 0:未知 1:男 2:女
	Status       int    `json:"status"`
	CreatedAt    int64  `json:"created_at"`
	UpdatedAt    int64  `json:"updated_at"`
}

type BatchAuditCancelReq struct {
	Ids          []int64 `json:"ids"`
	AuditStatus  int     `json:"audit_status"`           // 1:通过 -1:拒绝
	RefusalCause string  `json:"refusal_cause,optional"` // 拒绝原因
	Remark       string  `json:"remark,optional"`        // 审核备注
}

type BatchAuditReq struct {
	Ids          []int64 `json:"ids"`
	Status       int     `json:"status"`                 // 1:通过 -1:拒绝
	RejectReason string  `json:"reject_reason,optional"` // 拒绝原因
	Remark       string  `json:"remark,optional"`
}

type CancelAuditStatus struct {
	Pending  int `json:"pending"`  // 0:申请中
	Approved int `json:"approved"` // 1:已通过
	Rejected int `json:"rejected"` // -1:已拒绝
}

type CancelDetail struct {
	Id            int64   `json:"id"`
	MemberId      int64   `json:"member_id"`
	Username      string  `json:"username"`
	Nickname      string  `json:"nickname"`
	Mobile        string  `json:"mobile"`
	Email         string  `json:"email"`
	RegisterTime  int64   `json:"register_time"`
	LastLoginTime int64   `json:"last_login_time"`
	Content       string  `json:"content"`
	SubmitTime    int64   `json:"submit_time"`
	AuditStatus   int     `json:"audit_status"`
	AuditTime     int64   `json:"audit_time"`
	RefusalCause  string  `json:"refusal_cause"`
	Balance       float64 `json:"balance"`
	Integral      int64   `json:"integral"`
	Growth        int64   `json:"growth"`
	OrderCount    int64   `json:"order_count"`
	ArticleCount  int64   `json:"article_count"`
	CommentCount  int64   `json:"comment_count"`
}

type CancelStatusData struct {
	HasApplication bool   `json:"has_application"` // 是否有申请记录
	AuditStatus    int    `json:"audit_status"`    // 审核状态
	StatusText     string `json:"status_text"`     // 状态描述
	Content        string `json:"content"`         // 申请内容
	SubmitTime     int64  `json:"submit_time"`     // 提交时间
	AuditTime      int64  `json:"audit_time"`      // 审核时间
	RefusalCause   string `json:"refusal_cause"`   // 拒绝原因
}

type CertificationStatus struct {
	Pending  int `json:"pending"`  // 0:待审核
	Approved int `json:"approved"` // 1:已通过
	Rejected int `json:"rejected"` // -1:已拒绝
}

type CertificationStatusData struct {
	Status       int    `json:"status"`        // 认证状态
	StatusText   string `json:"status_text"`   // 状态描述
	RejectReason string `json:"reject_reason"` // 拒绝原因
	SubmitTime   int64  `json:"submit_time"`   // 提交时间
	AuditTime    int64  `json:"audit_time"`    // 审核时间
}

type CommonResp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type DeleteMemberReq struct {
	Id int64 `path:"id"`
}

type ExchangeIntegralReq struct {
	Integral int64  `json:"integral"`
	GoodsId  int64  `json:"goods_id,optional"`
	Remark   string `json:"remark,optional"`
}

type FreezeMoneyReq struct {
	MemberId int64   `json:"member_id"`
	Amount   float64 `json:"amount"`
	Reason   string  `json:"reason"`
}

type Gender struct {
	Unknown int `json:"unknown"` // 0:未知
	Male    int `json:"male"`    // 1:男
	Female  int `json:"female"`  // 2:女
}

type GetAccountInfoReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetAccountInfoResp struct {
	Code    int           `json:"code"`
	Message string        `json:"message"`
	Data    MemberAccount `json:"data"`
}

type GetBalanceReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetBalanceResp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    BalanceData `json:"data"`
}

type GetCancelDetailReq struct {
	Id int64 `path:"id"`
}

type GetCancelDetailResp struct {
	Code    int          `json:"code"`
	Message string       `json:"message"`
	Data    CancelDetail `json:"data"`
}

type GetCancelStatusReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetCancelStatusResp struct {
	Code    int              `json:"code"`
	Message string           `json:"message"`
	Data    CancelStatusData `json:"data"`
}

type GetCertificationInfoReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetCertificationInfoResp struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    MemberCertification `json:"data"`
}

type GetCertificationStatusReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetCertificationStatusResp struct {
	Code    int                     `json:"code"`
	Message string                  `json:"message"`
	Data    CertificationStatusData `json:"data"`
}

type GetGrowthInfoReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetGrowthInfoResp struct {
	Code    int        `json:"code"`
	Message string     `json:"message"`
	Data    GrowthData `json:"data"`
}

type GetIntegralInfoReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetIntegralInfoResp struct {
	Code    int          `json:"code"`
	Message string       `json:"message"`
	Data    IntegralData `json:"data"`
}

type GetMemberDetailReq struct {
	Id int64 `path:"id"`
}

type GetMemberDetailResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    Member `json:"data"`
}

type GetMemberInfoReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetMemberInfoResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    Member `json:"data"`
}

type GetMemberListReq struct {
	BasePageReq
	Username string `form:"username,optional"`
	Mobile   string `form:"mobile,optional"`
	Status   int    `form:"status,optional"`
	Type     int    `form:"type,optional"`
}

type GetMemberListResp struct {
	Code    int            `json:"code"`
	Message string         `json:"message"`
	Data    MemberListData `json:"data"`
}

type GetMemberStatReq struct {
	MemberId int64 `form:"member_id,optional"`
}

type GetMemberStatResp struct {
	Code    int        `json:"code"`
	Message string     `json:"message"`
	Data    MemberStat `json:"data"`
}

type GetPendingCancelListReq struct {
	BasePageReq
	MemberId  int64  `form:"member_id,optional"`
	Username  string `form:"username,optional"`
	StartTime int64  `form:"start_time,optional"`
	EndTime   int64  `form:"end_time,optional"`
}

type GetPendingCancelListResp struct {
	Code    int                   `json:"code"`
	Message string                `json:"message"`
	Data    PendingCancelListData `json:"data"`
}

type GetPendingListReq struct {
	BasePageReq
	MemberId  int64  `form:"member_id,optional"`
	Realname  string `form:"realname,optional"`
	StartTime int64  `form:"start_time,optional"`
	EndTime   int64  `form:"end_time,optional"`
}

type GetPendingListResp struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    PendingListData `json:"data"`
}

type GetStatReportReq struct {
	StartDate string `form:"start_date,optional"`
	EndDate   string `form:"end_date,optional"`
	Page      int    `form:"page,default=1"`
	PageSize  int    `form:"page_size,default=10"`
}

type GetStatReportResp struct {
	Code    int            `json:"code"`
	Message string         `json:"message"`
	Data    StatReportData `json:"data"`
}

type GrowthData struct {
	UserGrowth       int64 `json:"user_growth"`
	AccumulateGrowth int64 `json:"accumulate_growth"`
	ConsumeGrowth    int64 `json:"consume_growth"`
	FrozenGrowth     int64 `json:"frozen_growth"`
}

type IncreaseStatReq struct {
	MemberId int64 `json:"member_id"`
	Count    int64 `json:"count,default=1"`
}

type IntegralData struct {
	UserIntegral       int64   `json:"user_integral"`
	AccumulateIntegral int64   `json:"accumulate_integral"`
	ConsumeIntegral    float64 `json:"consume_integral"`
	FrozenIntegral     int64   `json:"frozen_integral"`
}

type JwtAuth struct {
	AccessToken  string `header:"Authorization"`
	RefreshToken string `header:"X-Refresh-Token,optional"`
}

type LoginData struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
	UserInfo     Member `json:"user_info,omitempty"`
}

type LoginReq struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	LoginType  string `json:"login_type"` // username, mobile, email
	VerifyCode string `json:"verify_code,optional"`
}

type LoginResp struct {
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Data    LoginData `json:"data"`
}

type LogoutReq struct {
	Token string `json:"token,optional"`
}

type Member struct {
	Id                  int64  `json:"id"`
	MerchantId          int64  `json:"merchant_id"`
	StoreId             int64  `json:"store_id"`
	Username            string `json:"username"`
	PasswordHash        string `json:"-"` // 密码哈希（不返回给前端）
	AuthKey             string `json:"-"` // 授权令牌（不返回给前端）
	PasswordResetToken  string `json:"-"` // 密码重置令牌（不返回给前端）
	MobileResetToken    string `json:"-"` // 手机重置令牌（不返回给前端）
	Type                int    `json:"type"`
	Realname            string `json:"realname"`
	Nickname            string `json:"nickname"`
	HeadPortrait        string `json:"head_portrait"`
	Gender              int    `json:"gender"`
	Qq                  string `json:"qq"`
	Email               string `json:"email"`
	Birthday            string `json:"birthday"`
	ProvinceId          int64  `json:"province_id"`
	CityId              int64  `json:"city_id"`
	AreaId              int64  `json:"area_id"`
	Address             string `json:"address"`
	Mobile              string `json:"mobile"`
	TelNo               string `json:"tel_no"`
	BgImage             string `json:"bg_image"`
	Description         string `json:"description"`
	VisitCount          int    `json:"visit_count"`
	LastTime            int64  `json:"last_time"`
	LastIp              string `json:"last_ip"`
	Role                int    `json:"role"`
	CurrentLevel        int    `json:"current_level"`
	LevelExpirationTime int64  `json:"level_expiration_time"`
	LevelBuyType        int    `json:"level_buy_type"`
	Pid                 int64  `json:"pid"`
	Level               int    `json:"level"`
	Tree                string `json:"tree"`
	PromoterCode        string `json:"promoter_code"`
	CertificationType   int    `json:"certification_type"`
	Source              string `json:"source"`
	Status              int    `json:"status"`
	CreatedAt           int64  `json:"created_at"`
	UpdatedAt           int64  `json:"updated_at"`
	RegionId            int64  `json:"region_id"`
}

type MemberAccount struct {
	Id                   int64   `json:"id"`
	MerchantId           int64   `json:"merchant_id"`
	StoreId              int64   `json:"store_id"`
	MemberId             int64   `json:"member_id"`
	MemberType           int     `json:"member_type"`
	UserMoney            float64 `json:"user_money"`             // 当前余额
	AccumulateMoney      float64 `json:"accumulate_money"`       // 累计余额
	GiveMoney            float64 `json:"give_money"`             // 累计赠送余额
	ConsumeMoney         float64 `json:"consume_money"`          // 累计消费金额
	FrozenMoney          float64 `json:"frozen_money"`           // 冻结金额
	UserIntegral         int64   `json:"user_integral"`          // 当前积分
	AccumulateIntegral   int64   `json:"accumulate_integral"`    // 累计积分
	GiveIntegral         int64   `json:"give_integral"`          // 累计赠送积分
	ConsumeIntegral      float64 `json:"consume_integral"`       // 累计消费积分
	FrozenIntegral       int64   `json:"frozen_integral"`        // 冻结积分
	UserGrowth           int64   `json:"user_growth"`            // 当前成长值
	AccumulateGrowth     int64   `json:"accumulate_growth"`      // 累计成长值
	ConsumeGrowth        int64   `json:"consume_growth"`         // 累计消费成长值
	FrozenGrowth         int64   `json:"frozen_growth"`          // 冻结成长值
	EconomizeMoney       float64 `json:"economize_money"`        // 已节约金额
	AccumulateDrawnMoney float64 `json:"accumulate_drawn_money"` // 累计提现
	Status               int     `json:"status"`                 // 状态 -1:删除 0:禁用 1:启用
	CreatedAt            int64   `json:"created_at"`             // 创建时间
	UpdatedAt            int64   `json:"updated_at"`             // 更新时间
}

type MemberCancel struct {
	Id           int64  `json:"id"`
	MerchantId   int64  `json:"merchant_id"`
	StoreId      int64  `json:"store_id"`
	MemberId     int64  `json:"member_id"`
	Content      string `json:"content"`       // 申请内容
	AuditStatus  int    `json:"audit_status"`  // 审核状态 0:申请 1:通过 -1:失败
	AuditTime    int64  `json:"audit_time"`    // 审核时间
	RefusalCause string `json:"refusal_cause"` // 拒绝原因
	IsAddon      int    `json:"is_addon"`
	AddonName    string `json:"addon_name"`
	Status       int    `json:"status"`
	CreatedAt    int64  `json:"created_at"`
	UpdatedAt    int64  `json:"updated_at"`
}

type MemberCertification struct {
	Id                int64  `json:"id"`
	MerchantId        int64  `json:"merchant_id"`
	StoreId           int64  `json:"store_id"`
	MemberId          int64  `json:"member_id"`
	MemberType        int    `json:"member_type"`
	Realname          string `json:"realname"`
	IdentityCard      string `json:"identity_card"`
	IdentityCardFront string `json:"identity_card_front"`
	IdentityCardBack  string `json:"identity_card_back"`
	Gender            string `json:"gender"`
	Birthday          string `json:"birthday"`
	FrontIsFake       int    `json:"front_is_fake"`
	BackIsFake        int    `json:"back_is_fake"`
	Nationality       string `json:"nationality"`
	Address           string `json:"address"`
	StartDate         string `json:"start_date"`
	EndDate           string `json:"end_date"`
	Issue             string `json:"issue"`
	IsSelf            int    `json:"is_self"`
	Status            int    `json:"status"`
	CreatedAt         int64  `json:"created_at"`
	UpdatedAt         int64  `json:"updated_at"`
}

type MemberListData struct {
	List     []Member `json:"list"`
	PageInfo PageInfo `json:"page_info"`
}

type MemberStat struct {
	Id           int64 `json:"id"`
	MerchantId   int64 `json:"merchant_id"`
	StoreId      int64 `json:"store_id"`
	MemberId     int64 `json:"member_id"`
	MemberType   int   `json:"member_type"`
	NiceNum      int64 `json:"nice_num"`      // 点赞数量
	DisagreeNum  int64 `json:"disagree_num"`  // 不赞同数量
	TransmitNum  int64 `json:"transmit_num"`  // 转发数量
	CommentNum   int64 `json:"comment_num"`   // 评论数量
	CollectNum   int64 `json:"collect_num"`   // 收藏数量
	ReportNum    int64 `json:"report_num"`    // 举报数量
	RecommendNum int64 `json:"recommend_num"` // 推荐数量
	FollowNum    int64 `json:"follow_num"`    // 关注人数
	AllowedNum   int64 `json:"allowed_num"`   // 被关注人数
	View         int64 `json:"view"`          // 浏览量
	Status       int   `json:"status"`
	CreatedAt    int64 `json:"created_at"`
	UpdatedAt    int64 `json:"updated_at"`
}

type MemberType struct {
	Member   int `json:"member"`   // 1:会员
	Admin    int `json:"admin"`    // 2:后台管理员
	Merchant int `json:"merchant"` // 3:商家管理员
}

type PageInfo struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Total    int `json:"total"`
}

type PendingCancelItem struct {
	Id           int64  `json:"id"`
	MemberId     int64  `json:"member_id"`
	Username     string `json:"username"`
	Nickname     string `json:"nickname"`
	Mobile       string `json:"mobile"`
	Content      string `json:"content"`
	SubmitTime   int64  `json:"submit_time"`
	AuditStatus  int    `json:"audit_status"`
	RefusalCause string `json:"refusal_cause"`
}

type PendingCancelListData struct {
	List     []PendingCancelItem `json:"list"`
	PageInfo PageInfo            `json:"page_info"`
}

type PendingCertification struct {
	Id           int64  `json:"id"`
	MemberId     int64  `json:"member_id"`
	Username     string `json:"username"`
	Mobile       string `json:"mobile"`
	Realname     string `json:"realname"`
	IdentityCard string `json:"identity_card"`
	SubmitTime   int64  `json:"submit_time"`
	Status       int    `json:"status"`
}

type PendingListData struct {
	List     []PendingCertification `json:"list"`
	PageInfo PageInfo               `json:"page_info"`
}

type RechargeData struct {
	OrderNo string  `json:"order_no"`
	Amount  float64 `json:"amount"`
	PayUrl  string  `json:"pay_url,optional"`
	QrCode  string  `json:"qr_code,optional"`
}

type RechargeReq struct {
	Amount      float64 `json:"amount"`
	PaymentType string  `json:"payment_type"` // alipay, wechat, bank
	Remark      string  `json:"remark,optional"`
}

type RechargeResp struct {
	Code    int          `json:"code"`
	Message string       `json:"message"`
	Data    RechargeData `json:"data"`
}

type RefreshTokenData struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

type RefreshTokenReq struct {
	RefreshToken string `json:"refresh_token"`
}

type RefreshTokenResp struct {
	Code    int              `json:"code"`
	Message string           `json:"message"`
	Data    RefreshTokenData `json:"data"`
}

type RegisterData struct {
	MemberId int64 `json:"member_id"`
}

type RegisterReq struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	Mobile     string `json:"mobile"`
	Email      string `json:"email,optional"`
	Nickname   string `json:"nickname,optional"`
	VerifyCode string `json:"verify_code"`
	Source     string `json:"source,optional"`
}

type RegisterResp struct {
	Code    int          `json:"code"`
	Message string       `json:"message"`
	Data    RegisterData `json:"data"`
}

type ResetPasswordReq struct {
	Mobile      string `json:"mobile"`
	NewPassword string `json:"new_password"`
	VerifyCode  string `json:"verify_code"`
}

type StatReportData struct {
	TotalMembers  int64   `json:"total_members"`  // 总用户数
	ActiveMembers int64   `json:"active_members"` // 活跃用户数
	TotalViews    int64   `json:"total_views"`    // 总浏览量
	TotalLikes    int64   `json:"total_likes"`    // 总点赞数
	TotalCollects int64   `json:"total_collects"` // 总收藏数
	TotalFollows  int64   `json:"total_follows"`  // 总关注数
	AvgActivity   float64 `json:"avg_activity"`   // 平均活跃度
}

type Status struct {
	Deleted  int `json:"deleted"`  // -1
	Disabled int `json:"disabled"` // 0
	Enabled  int `json:"enabled"`  // 1
}

type SubmitCancelReq struct {
	Content string `json:"content"` // 申请原因
}

type SubmitCertificationReq struct {
	Realname          string `json:"realname"`
	IdentityCard      string `json:"identity_card"`
	IdentityCardFront string `json:"identity_card_front"`
	IdentityCardBack  string `json:"identity_card_back"`
	Gender            string `json:"gender,optional"`
	Birthday          string `json:"birthday,optional"`
	Nationality       string `json:"nationality,optional"`
	Address           string `json:"address,optional"`
	StartDate         string `json:"start_date,optional"`
	EndDate           string `json:"end_date,optional"`
	Issue             string `json:"issue,optional"`
	IsSelf            int    `json:"is_self,optional"`
}

type UnfreezeMoneyReq struct {
	MemberId int64   `json:"member_id"`
	Amount   float64 `json:"amount"`
	Reason   string  `json:"reason"`
}

type UpdateMemberInfoReq struct {
	Realname     string `json:"realname,optional"`
	Nickname     string `json:"nickname,optional"`
	HeadPortrait string `json:"head_portrait,optional"`
	Gender       int    `json:"gender,optional"`
	Qq           string `json:"qq,optional"`
	Email        string `json:"email,optional"`
	Birthday     string `json:"birthday,optional"`
	ProvinceId   int64  `json:"province_id,optional"`
	CityId       int64  `json:"city_id,optional"`
	AreaId       int64  `json:"area_id,optional"`
	Address      string `json:"address,optional"`
	TelNo        string `json:"tel_no,optional"`
	BgImage      string `json:"bg_image,optional"`
	Description  string `json:"description,optional"`
}

type UpdateMemberStatReq struct {
	MemberId     int64 `json:"member_id"`
	NiceNum      int64 `json:"nice_num,optional"`
	DisagreeNum  int64 `json:"disagree_num,optional"`
	TransmitNum  int64 `json:"transmit_num,optional"`
	CommentNum   int64 `json:"comment_num,optional"`
	CollectNum   int64 `json:"collect_num,optional"`
	ReportNum    int64 `json:"report_num,optional"`
	RecommendNum int64 `json:"recommend_num,optional"`
	FollowNum    int64 `json:"follow_num,optional"`
	AllowedNum   int64 `json:"allowed_num,optional"`
	View         int64 `json:"view,optional"`
}

type WithdrawCancelReq struct {
	Reason string `json:"reason,optional"` // 撤销原因
}

type WithdrawData struct {
	WithdrawNo   string  `json:"withdraw_no"`
	Amount       float64 `json:"amount"`
	Fee          float64 `json:"fee"`
	ActualAmount float64 `json:"actual_amount"`
}

type WithdrawReq struct {
	Amount      float64 `json:"amount"`
	AccountType string  `json:"account_type"` // bank, alipay, wechat
	AccountNo   string  `json:"account_no"`
	AccountName string  `json:"account_name"`
	BankName    string  `json:"bank_name,optional"`
	Remark      string  `json:"remark,optional"`
}

type WithdrawResp struct {
	Code    int          `json:"code"`
	Message string       `json:"message"`
	Data    WithdrawData `json:"data"`
}
