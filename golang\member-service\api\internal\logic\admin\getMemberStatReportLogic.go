package admin

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberStatReportLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户统计报表
func NewGetMemberStatReportLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberStatReportLogic {
	return &GetMemberStatReportLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMemberStatReportLogic) GetMemberStatReport(req *types.GetStatReportReq) (resp *types.GetStatReportResp, err error) {
	// todo: add your logic here and delete this line

	return
}
