package member

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"api/internal/svc"
	"api/internal/types"
	memberpb "member/member"
)

type LogoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户登出
func NewLogoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LogoutLogic {
	return &LogoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LogoutLogic) Logout(req *types.LogoutReq) (resp *types.CommonResp, err error) {



	// 调用RPC服务处理用户登出
	rpcResp, err := l.svcCtx.MemberRpc.Logout(l.ctx, &memberpb.LogoutReq{
		Token: req.Token,
	})

	if err != nil {
		return nil, fmt.Errorf("调用RPC登出失败: %v", err)
	}

	return &types.CommonResp{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
	}, nil
}
