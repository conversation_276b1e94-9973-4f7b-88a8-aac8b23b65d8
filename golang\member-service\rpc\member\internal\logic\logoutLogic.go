package logic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
)

type LogoutLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLogoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LogoutLogic {
	return &LogoutLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LogoutLogic) Logout(in *member.LogoutReq) (*member.LogoutResp, error) {

	if in.Token == "" {
		return &member.LogoutResp{
			Code:    400,
			Message: "Token不能为空",
		}, nil
	}

	// 在JWT无状态系统中，登出主要是客户端删除Token
	// 这里只做审计日志记录
	l.Logger.Infof("用户登出 - Token: %s", in.Token[:10]+"...")

	// 可选：如果需要立即撤销Token，可以将Token加入黑名单
	// 但这会增加系统复杂性，通常不建议
	// err := l.svcCtx.Redis.Set(l.ctx, "blacklist:"+in.Token, "1", time.Until(tokenExpireTime)).Err()

	return &member.LogoutResp{
		Code:    200,
		Message: "登出成功",
	}, nil
}
