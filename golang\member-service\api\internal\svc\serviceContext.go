package svc

import (
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"

	"api/internal/config"
	"api/internal/middleware"

	"account/accountservice"
	"member/memberservice"
)

type ServiceContext struct {
	Config    config.Config
	AdminAuth rest.Middleware
	// RPC 客户端
	MemberRpc  memberservice.MemberService
	AccountRpc accountservice.AccountService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:     c,
		AdminAuth:  middleware.NewAdminAuthMiddleware(c.JwtAuth.AccessSecret).Handle,
		MemberRpc:  memberservice.NewMemberService(zrpc.MustNewClient(c.MemberRpc)),
		AccountRpc: accountservice.NewAccountService(zrpc.MustNewClient(c.AccountRpc)),
	}
}
