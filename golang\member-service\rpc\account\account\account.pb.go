// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.0
// source: pb/account.proto

package account

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ===== 通用响应 =====
type CommonResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonResp) Reset() {
	*x = CommonResp{}
	mi := &file_pb_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResp) ProtoMessage() {}

func (x *CommonResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResp.ProtoReflect.Descriptor instead.
func (*CommonResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{0}
}

func (x *CommonResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// ===== 分页信息 =====
type PageInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total         int64                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PageInfo) Reset() {
	*x = PageInfo{}
	mi := &file_pb_account_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo) ProtoMessage() {}

func (x *PageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo.ProtoReflect.Descriptor instead.
func (*PageInfo) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{1}
}

func (x *PageInfo) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageInfo) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageInfo) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// ===== 账户信息（基于member_account表结构） =====
type MemberAccount struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Id         int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MerchantId int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId    int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	MemberId   int64                  `protobuf:"varint,4,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	MemberType int32                  `protobuf:"varint,5,opt,name=member_type,json=memberType,proto3" json:"member_type,omitempty"` // 1:会员 2:后台管理员 3:商家管理员
	// 余额相关
	UserMoney       float64 `protobuf:"fixed64,6,opt,name=user_money,json=userMoney,proto3" json:"user_money,omitempty"`                   // 当前余额
	AccumulateMoney float64 `protobuf:"fixed64,7,opt,name=accumulate_money,json=accumulateMoney,proto3" json:"accumulate_money,omitempty"` // 累计余额
	GiveMoney       float64 `protobuf:"fixed64,8,opt,name=give_money,json=giveMoney,proto3" json:"give_money,omitempty"`                   // 累计赠送余额
	ConsumeMoney    float64 `protobuf:"fixed64,9,opt,name=consume_money,json=consumeMoney,proto3" json:"consume_money,omitempty"`          // 累计消费金额
	FrozenMoney     float64 `protobuf:"fixed64,10,opt,name=frozen_money,json=frozenMoney,proto3" json:"frozen_money,omitempty"`            // 冻结金额
	// 积分相关
	UserIntegral       int64   `protobuf:"varint,11,opt,name=user_integral,json=userIntegral,proto3" json:"user_integral,omitempty"`                   // 当前积分
	AccumulateIntegral int64   `protobuf:"varint,12,opt,name=accumulate_integral,json=accumulateIntegral,proto3" json:"accumulate_integral,omitempty"` // 累计积分
	GiveIntegral       int64   `protobuf:"varint,13,opt,name=give_integral,json=giveIntegral,proto3" json:"give_integral,omitempty"`                   // 累计赠送积分
	ConsumeIntegral    float64 `protobuf:"fixed64,14,opt,name=consume_integral,json=consumeIntegral,proto3" json:"consume_integral,omitempty"`         // 累计消费积分
	FrozenIntegral     int64   `protobuf:"varint,15,opt,name=frozen_integral,json=frozenIntegral,proto3" json:"frozen_integral,omitempty"`             // 冻结积分
	// 成长值相关
	UserGrowth       int64 `protobuf:"varint,16,opt,name=user_growth,json=userGrowth,proto3" json:"user_growth,omitempty"`                   // 当前成长值
	AccumulateGrowth int64 `protobuf:"varint,17,opt,name=accumulate_growth,json=accumulateGrowth,proto3" json:"accumulate_growth,omitempty"` // 累计成长值
	ConsumeGrowth    int64 `protobuf:"varint,18,opt,name=consume_growth,json=consumeGrowth,proto3" json:"consume_growth,omitempty"`          // 累计消费成长值
	FrozenGrowth     int64 `protobuf:"varint,19,opt,name=frozen_growth,json=frozenGrowth,proto3" json:"frozen_growth,omitempty"`             // 冻结成长值
	// 其他
	EconomizeMoney       float64 `protobuf:"fixed64,20,opt,name=economize_money,json=economizeMoney,proto3" json:"economize_money,omitempty"`                     // 已节约金额
	AccumulateDrawnMoney float64 `protobuf:"fixed64,21,opt,name=accumulate_drawn_money,json=accumulateDrawnMoney,proto3" json:"accumulate_drawn_money,omitempty"` // 累计提现
	Status               int32   `protobuf:"varint,22,opt,name=status,proto3" json:"status,omitempty"`                                                            // 状态 -1:删除 0:禁用 1:启用
	CreatedAt            int64   `protobuf:"varint,23,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                     // 创建时间
	UpdatedAt            int64   `protobuf:"varint,24,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                     // 更新时间
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *MemberAccount) Reset() {
	*x = MemberAccount{}
	mi := &file_pb_account_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemberAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberAccount) ProtoMessage() {}

func (x *MemberAccount) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberAccount.ProtoReflect.Descriptor instead.
func (*MemberAccount) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{2}
}

func (x *MemberAccount) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MemberAccount) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *MemberAccount) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *MemberAccount) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *MemberAccount) GetMemberType() int32 {
	if x != nil {
		return x.MemberType
	}
	return 0
}

func (x *MemberAccount) GetUserMoney() float64 {
	if x != nil {
		return x.UserMoney
	}
	return 0
}

func (x *MemberAccount) GetAccumulateMoney() float64 {
	if x != nil {
		return x.AccumulateMoney
	}
	return 0
}

func (x *MemberAccount) GetGiveMoney() float64 {
	if x != nil {
		return x.GiveMoney
	}
	return 0
}

func (x *MemberAccount) GetConsumeMoney() float64 {
	if x != nil {
		return x.ConsumeMoney
	}
	return 0
}

func (x *MemberAccount) GetFrozenMoney() float64 {
	if x != nil {
		return x.FrozenMoney
	}
	return 0
}

func (x *MemberAccount) GetUserIntegral() int64 {
	if x != nil {
		return x.UserIntegral
	}
	return 0
}

func (x *MemberAccount) GetAccumulateIntegral() int64 {
	if x != nil {
		return x.AccumulateIntegral
	}
	return 0
}

func (x *MemberAccount) GetGiveIntegral() int64 {
	if x != nil {
		return x.GiveIntegral
	}
	return 0
}

func (x *MemberAccount) GetConsumeIntegral() float64 {
	if x != nil {
		return x.ConsumeIntegral
	}
	return 0
}

func (x *MemberAccount) GetFrozenIntegral() int64 {
	if x != nil {
		return x.FrozenIntegral
	}
	return 0
}

func (x *MemberAccount) GetUserGrowth() int64 {
	if x != nil {
		return x.UserGrowth
	}
	return 0
}

func (x *MemberAccount) GetAccumulateGrowth() int64 {
	if x != nil {
		return x.AccumulateGrowth
	}
	return 0
}

func (x *MemberAccount) GetConsumeGrowth() int64 {
	if x != nil {
		return x.ConsumeGrowth
	}
	return 0
}

func (x *MemberAccount) GetFrozenGrowth() int64 {
	if x != nil {
		return x.FrozenGrowth
	}
	return 0
}

func (x *MemberAccount) GetEconomizeMoney() float64 {
	if x != nil {
		return x.EconomizeMoney
	}
	return 0
}

func (x *MemberAccount) GetAccumulateDrawnMoney() float64 {
	if x != nil {
		return x.AccumulateDrawnMoney
	}
	return 0
}

func (x *MemberAccount) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MemberAccount) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MemberAccount) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// ===== 账户日志信息 =====
type AccountLog struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MemberId      int64                  `protobuf:"varint,2,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Type          int32                  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`                                         // 操作类型 1:余额 2:积分 3:成长值
	Operation     int32                  `protobuf:"varint,4,opt,name=operation,proto3" json:"operation,omitempty"`                               // 操作 1:增加 2:减少 3:冻结 4:解冻
	Amount        float64                `protobuf:"fixed64,5,opt,name=amount,proto3" json:"amount,omitempty"`                                    // 操作金额/数量
	BalanceBefore float64                `protobuf:"fixed64,6,opt,name=balance_before,json=balanceBefore,proto3" json:"balance_before,omitempty"` // 操作前余额
	BalanceAfter  float64                `protobuf:"fixed64,7,opt,name=balance_after,json=balanceAfter,proto3" json:"balance_after,omitempty"`    // 操作后余额
	Reason        string                 `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`                                      // 操作原因
	Remark        string                 `protobuf:"bytes,9,opt,name=remark,proto3" json:"remark,omitempty"`                                      // 备注
	CreatedAt     int64                  `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountLog) Reset() {
	*x = AccountLog{}
	mi := &file_pb_account_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountLog) ProtoMessage() {}

func (x *AccountLog) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountLog.ProtoReflect.Descriptor instead.
func (*AccountLog) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{3}
}

func (x *AccountLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AccountLog) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *AccountLog) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AccountLog) GetOperation() int32 {
	if x != nil {
		return x.Operation
	}
	return 0
}

func (x *AccountLog) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AccountLog) GetBalanceBefore() float64 {
	if x != nil {
		return x.BalanceBefore
	}
	return 0
}

func (x *AccountLog) GetBalanceAfter() float64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

func (x *AccountLog) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AccountLog) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *AccountLog) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// ===== 创建账户 =====
type CreateAccountReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	MerchantId    int64                  `protobuf:"varint,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	StoreId       int64                  `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	MemberType    int32                  `protobuf:"varint,4,opt,name=member_type,json=memberType,proto3" json:"member_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountReq) Reset() {
	*x = CreateAccountReq{}
	mi := &file_pb_account_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountReq) ProtoMessage() {}

func (x *CreateAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountReq.ProtoReflect.Descriptor instead.
func (*CreateAccountReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{4}
}

func (x *CreateAccountReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *CreateAccountReq) GetMerchantId() int64 {
	if x != nil {
		return x.MerchantId
	}
	return 0
}

func (x *CreateAccountReq) GetStoreId() int64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *CreateAccountReq) GetMemberType() int32 {
	if x != nil {
		return x.MemberType
	}
	return 0
}

type CreateAccountResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	AccountId     int64                  `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountResp) Reset() {
	*x = CreateAccountResp{}
	mi := &file_pb_account_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountResp) ProtoMessage() {}

func (x *CreateAccountResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountResp.ProtoReflect.Descriptor instead.
func (*CreateAccountResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{5}
}

func (x *CreateAccountResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateAccountResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateAccountResp) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

// ===== 获取账户信息 =====
type GetAccountInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountInfoReq) Reset() {
	*x = GetAccountInfoReq{}
	mi := &file_pb_account_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountInfoReq) ProtoMessage() {}

func (x *GetAccountInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountInfoReq.ProtoReflect.Descriptor instead.
func (*GetAccountInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{6}
}

func (x *GetAccountInfoReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type GetAccountInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *MemberAccount         `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountInfoResp) Reset() {
	*x = GetAccountInfoResp{}
	mi := &file_pb_account_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountInfoResp) ProtoMessage() {}

func (x *GetAccountInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountInfoResp.ProtoReflect.Descriptor instead.
func (*GetAccountInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{7}
}

func (x *GetAccountInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAccountInfoResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetAccountInfoResp) GetData() *MemberAccount {
	if x != nil {
		return x.Data
	}
	return nil
}

// ===== 获取账户列表 =====
type GetAccountListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	MemberId      int64                  `protobuf:"varint,3,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	MemberType    int32                  `protobuf:"varint,4,opt,name=member_type,json=memberType,proto3" json:"member_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountListReq) Reset() {
	*x = GetAccountListReq{}
	mi := &file_pb_account_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountListReq) ProtoMessage() {}

func (x *GetAccountListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountListReq.ProtoReflect.Descriptor instead.
func (*GetAccountListReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{8}
}

func (x *GetAccountListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAccountListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAccountListReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *GetAccountListReq) GetMemberType() int32 {
	if x != nil {
		return x.MemberType
	}
	return 0
}

type GetAccountListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	List          []*MemberAccount       `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	PageInfo      *PageInfo              `protobuf:"bytes,4,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountListResp) Reset() {
	*x = GetAccountListResp{}
	mi := &file_pb_account_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountListResp) ProtoMessage() {}

func (x *GetAccountListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountListResp.ProtoReflect.Descriptor instead.
func (*GetAccountListResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{9}
}

func (x *GetAccountListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAccountListResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetAccountListResp) GetList() []*MemberAccount {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetAccountListResp) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

// ===== 获取余额信息 =====
type GetBalanceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBalanceReq) Reset() {
	*x = GetBalanceReq{}
	mi := &file_pb_account_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceReq) ProtoMessage() {}

func (x *GetBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceReq.ProtoReflect.Descriptor instead.
func (*GetBalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{10}
}

func (x *GetBalanceReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type GetBalanceResp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Code            int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message         string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	UserMoney       float64                `protobuf:"fixed64,3,opt,name=user_money,json=userMoney,proto3" json:"user_money,omitempty"`                   // 当前余额
	AccumulateMoney float64                `protobuf:"fixed64,4,opt,name=accumulate_money,json=accumulateMoney,proto3" json:"accumulate_money,omitempty"` // 累计余额
	FrozenMoney     float64                `protobuf:"fixed64,5,opt,name=frozen_money,json=frozenMoney,proto3" json:"frozen_money,omitempty"`             // 冻结金额
	ConsumeMoney    float64                `protobuf:"fixed64,6,opt,name=consume_money,json=consumeMoney,proto3" json:"consume_money,omitempty"`          // 累计消费
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetBalanceResp) Reset() {
	*x = GetBalanceResp{}
	mi := &file_pb_account_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceResp) ProtoMessage() {}

func (x *GetBalanceResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceResp.ProtoReflect.Descriptor instead.
func (*GetBalanceResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{11}
}

func (x *GetBalanceResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetBalanceResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetBalanceResp) GetUserMoney() float64 {
	if x != nil {
		return x.UserMoney
	}
	return 0
}

func (x *GetBalanceResp) GetAccumulateMoney() float64 {
	if x != nil {
		return x.AccumulateMoney
	}
	return 0
}

func (x *GetBalanceResp) GetFrozenMoney() float64 {
	if x != nil {
		return x.FrozenMoney
	}
	return 0
}

func (x *GetBalanceResp) GetConsumeMoney() float64 {
	if x != nil {
		return x.ConsumeMoney
	}
	return 0
}

// ===== 充值 =====
type RechargeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	PaymentMethod string                 `protobuf:"bytes,3,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"` // 支付方式
	OrderNo       string                 `protobuf:"bytes,4,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`                   // 订单号
	Remark        string                 `protobuf:"bytes,5,opt,name=remark,proto3" json:"remark,omitempty"`                                    // 备注
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RechargeReq) Reset() {
	*x = RechargeReq{}
	mi := &file_pb_account_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RechargeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeReq) ProtoMessage() {}

func (x *RechargeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeReq.ProtoReflect.Descriptor instead.
func (*RechargeReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{12}
}

func (x *RechargeReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *RechargeReq) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *RechargeReq) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *RechargeReq) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *RechargeReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type RechargeResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	OrderNo       string                 `protobuf:"bytes,3,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	BalanceAfter  float64                `protobuf:"fixed64,4,opt,name=balance_after,json=balanceAfter,proto3" json:"balance_after,omitempty"` // 充值后余额
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RechargeResp) Reset() {
	*x = RechargeResp{}
	mi := &file_pb_account_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RechargeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeResp) ProtoMessage() {}

func (x *RechargeResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeResp.ProtoReflect.Descriptor instead.
func (*RechargeResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{13}
}

func (x *RechargeResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RechargeResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RechargeResp) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *RechargeResp) GetBalanceAfter() float64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

// ===== 提现 =====
type WithdrawReq struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	MemberId        int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Amount          float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	WithdrawAccount string                 `protobuf:"bytes,3,opt,name=withdraw_account,json=withdrawAccount,proto3" json:"withdraw_account,omitempty"` // 提现账户
	Remark          string                 `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`                                          // 备注
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *WithdrawReq) Reset() {
	*x = WithdrawReq{}
	mi := &file_pb_account_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WithdrawReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithdrawReq) ProtoMessage() {}

func (x *WithdrawReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithdrawReq.ProtoReflect.Descriptor instead.
func (*WithdrawReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{14}
}

func (x *WithdrawReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *WithdrawReq) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *WithdrawReq) GetWithdrawAccount() string {
	if x != nil {
		return x.WithdrawAccount
	}
	return ""
}

func (x *WithdrawReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type WithdrawResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	WithdrawNo    string                 `protobuf:"bytes,3,opt,name=withdraw_no,json=withdrawNo,proto3" json:"withdraw_no,omitempty"`         // 提现单号
	BalanceAfter  float64                `protobuf:"fixed64,4,opt,name=balance_after,json=balanceAfter,proto3" json:"balance_after,omitempty"` // 提现后余额
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WithdrawResp) Reset() {
	*x = WithdrawResp{}
	mi := &file_pb_account_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WithdrawResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithdrawResp) ProtoMessage() {}

func (x *WithdrawResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithdrawResp.ProtoReflect.Descriptor instead.
func (*WithdrawResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{15}
}

func (x *WithdrawResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WithdrawResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *WithdrawResp) GetWithdrawNo() string {
	if x != nil {
		return x.WithdrawNo
	}
	return ""
}

func (x *WithdrawResp) GetBalanceAfter() float64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

// ===== 冻结资金 =====
type FreezeMoneyReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	OperatorId    int64                  `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FreezeMoneyReq) Reset() {
	*x = FreezeMoneyReq{}
	mi := &file_pb_account_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FreezeMoneyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FreezeMoneyReq) ProtoMessage() {}

func (x *FreezeMoneyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FreezeMoneyReq.ProtoReflect.Descriptor instead.
func (*FreezeMoneyReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{16}
}

func (x *FreezeMoneyReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *FreezeMoneyReq) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *FreezeMoneyReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *FreezeMoneyReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

// ===== 解冻资金 =====
type UnfreezeMoneyReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	OperatorId    int64                  `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnfreezeMoneyReq) Reset() {
	*x = UnfreezeMoneyReq{}
	mi := &file_pb_account_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnfreezeMoneyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfreezeMoneyReq) ProtoMessage() {}

func (x *UnfreezeMoneyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfreezeMoneyReq.ProtoReflect.Descriptor instead.
func (*UnfreezeMoneyReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{17}
}

func (x *UnfreezeMoneyReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *UnfreezeMoneyReq) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *UnfreezeMoneyReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *UnfreezeMoneyReq) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

// ===== 获取积分信息 =====
type GetIntegralInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIntegralInfoReq) Reset() {
	*x = GetIntegralInfoReq{}
	mi := &file_pb_account_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIntegralInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIntegralInfoReq) ProtoMessage() {}

func (x *GetIntegralInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIntegralInfoReq.ProtoReflect.Descriptor instead.
func (*GetIntegralInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{18}
}

func (x *GetIntegralInfoReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type GetIntegralInfoResp struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Code               int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message            string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	UserIntegral       int64                  `protobuf:"varint,3,opt,name=user_integral,json=userIntegral,proto3" json:"user_integral,omitempty"`                   // 当前积分
	AccumulateIntegral int64                  `protobuf:"varint,4,opt,name=accumulate_integral,json=accumulateIntegral,proto3" json:"accumulate_integral,omitempty"` // 累计积分
	FrozenIntegral     int64                  `protobuf:"varint,5,opt,name=frozen_integral,json=frozenIntegral,proto3" json:"frozen_integral,omitempty"`             // 冻结积分
	ConsumeIntegral    float64                `protobuf:"fixed64,6,opt,name=consume_integral,json=consumeIntegral,proto3" json:"consume_integral,omitempty"`         // 累计消费积分
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetIntegralInfoResp) Reset() {
	*x = GetIntegralInfoResp{}
	mi := &file_pb_account_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIntegralInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIntegralInfoResp) ProtoMessage() {}

func (x *GetIntegralInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIntegralInfoResp.ProtoReflect.Descriptor instead.
func (*GetIntegralInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{19}
}

func (x *GetIntegralInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetIntegralInfoResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetIntegralInfoResp) GetUserIntegral() int64 {
	if x != nil {
		return x.UserIntegral
	}
	return 0
}

func (x *GetIntegralInfoResp) GetAccumulateIntegral() int64 {
	if x != nil {
		return x.AccumulateIntegral
	}
	return 0
}

func (x *GetIntegralInfoResp) GetFrozenIntegral() int64 {
	if x != nil {
		return x.FrozenIntegral
	}
	return 0
}

func (x *GetIntegralInfoResp) GetConsumeIntegral() float64 {
	if x != nil {
		return x.ConsumeIntegral
	}
	return 0
}

// ===== 添加积分 =====
type AddIntegralReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Amount        int64                  `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	Remark        string                 `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddIntegralReq) Reset() {
	*x = AddIntegralReq{}
	mi := &file_pb_account_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddIntegralReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddIntegralReq) ProtoMessage() {}

func (x *AddIntegralReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddIntegralReq.ProtoReflect.Descriptor instead.
func (*AddIntegralReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{20}
}

func (x *AddIntegralReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *AddIntegralReq) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AddIntegralReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AddIntegralReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// ===== 消费积分 =====
type ConsumeIntegralReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Amount        int64                  `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	Remark        string                 `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeIntegralReq) Reset() {
	*x = ConsumeIntegralReq{}
	mi := &file_pb_account_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeIntegralReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeIntegralReq) ProtoMessage() {}

func (x *ConsumeIntegralReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeIntegralReq.ProtoReflect.Descriptor instead.
func (*ConsumeIntegralReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{21}
}

func (x *ConsumeIntegralReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *ConsumeIntegralReq) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ConsumeIntegralReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ConsumeIntegralReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// ===== 获取成长值信息 =====
type GetGrowthInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGrowthInfoReq) Reset() {
	*x = GetGrowthInfoReq{}
	mi := &file_pb_account_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGrowthInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrowthInfoReq) ProtoMessage() {}

func (x *GetGrowthInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrowthInfoReq.ProtoReflect.Descriptor instead.
func (*GetGrowthInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{22}
}

func (x *GetGrowthInfoReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

type GetGrowthInfoResp struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Code             int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message          string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	UserGrowth       int64                  `protobuf:"varint,3,opt,name=user_growth,json=userGrowth,proto3" json:"user_growth,omitempty"`                   // 当前成长值
	AccumulateGrowth int64                  `protobuf:"varint,4,opt,name=accumulate_growth,json=accumulateGrowth,proto3" json:"accumulate_growth,omitempty"` // 累计成长值
	ConsumeGrowth    int64                  `protobuf:"varint,5,opt,name=consume_growth,json=consumeGrowth,proto3" json:"consume_growth,omitempty"`          // 累计消费成长值
	FrozenGrowth     int64                  `protobuf:"varint,6,opt,name=frozen_growth,json=frozenGrowth,proto3" json:"frozen_growth,omitempty"`             // 冻结成长值
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetGrowthInfoResp) Reset() {
	*x = GetGrowthInfoResp{}
	mi := &file_pb_account_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGrowthInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrowthInfoResp) ProtoMessage() {}

func (x *GetGrowthInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrowthInfoResp.ProtoReflect.Descriptor instead.
func (*GetGrowthInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{23}
}

func (x *GetGrowthInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetGrowthInfoResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetGrowthInfoResp) GetUserGrowth() int64 {
	if x != nil {
		return x.UserGrowth
	}
	return 0
}

func (x *GetGrowthInfoResp) GetAccumulateGrowth() int64 {
	if x != nil {
		return x.AccumulateGrowth
	}
	return 0
}

func (x *GetGrowthInfoResp) GetConsumeGrowth() int64 {
	if x != nil {
		return x.ConsumeGrowth
	}
	return 0
}

func (x *GetGrowthInfoResp) GetFrozenGrowth() int64 {
	if x != nil {
		return x.FrozenGrowth
	}
	return 0
}

// ===== 添加成长值 =====
type AddGrowthReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Amount        int64                  `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	Remark        string                 `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddGrowthReq) Reset() {
	*x = AddGrowthReq{}
	mi := &file_pb_account_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddGrowthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddGrowthReq) ProtoMessage() {}

func (x *AddGrowthReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddGrowthReq.ProtoReflect.Descriptor instead.
func (*AddGrowthReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{24}
}

func (x *AddGrowthReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *AddGrowthReq) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AddGrowthReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AddGrowthReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// ===== 添加资金日志 =====
type AddMoneyLogReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Operation     int32                  `protobuf:"varint,2,opt,name=operation,proto3" json:"operation,omitempty"`                               // 操作类型 1:增加 2:减少
	Amount        float64                `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`                                    // 金额
	BalanceBefore float64                `protobuf:"fixed64,4,opt,name=balance_before,json=balanceBefore,proto3" json:"balance_before,omitempty"` // 操作前余额
	BalanceAfter  float64                `protobuf:"fixed64,5,opt,name=balance_after,json=balanceAfter,proto3" json:"balance_after,omitempty"`    // 操作后余额
	Reason        string                 `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`                                      // 原因
	Remark        string                 `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`                                      // 备注
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddMoneyLogReq) Reset() {
	*x = AddMoneyLogReq{}
	mi := &file_pb_account_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddMoneyLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMoneyLogReq) ProtoMessage() {}

func (x *AddMoneyLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMoneyLogReq.ProtoReflect.Descriptor instead.
func (*AddMoneyLogReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{25}
}

func (x *AddMoneyLogReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *AddMoneyLogReq) GetOperation() int32 {
	if x != nil {
		return x.Operation
	}
	return 0
}

func (x *AddMoneyLogReq) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AddMoneyLogReq) GetBalanceBefore() float64 {
	if x != nil {
		return x.BalanceBefore
	}
	return 0
}

func (x *AddMoneyLogReq) GetBalanceAfter() float64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

func (x *AddMoneyLogReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AddMoneyLogReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// ===== 添加积分日志 =====
type AddIntegralLogReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Operation     int32                  `protobuf:"varint,2,opt,name=operation,proto3" json:"operation,omitempty"`                              // 操作类型 1:增加 2:减少
	Amount        int64                  `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`                                    // 积分数量
	BalanceBefore int64                  `protobuf:"varint,4,opt,name=balance_before,json=balanceBefore,proto3" json:"balance_before,omitempty"` // 操作前积分
	BalanceAfter  int64                  `protobuf:"varint,5,opt,name=balance_after,json=balanceAfter,proto3" json:"balance_after,omitempty"`    // 操作后积分
	Reason        string                 `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`                                     // 原因
	Remark        string                 `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`                                     // 备注
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddIntegralLogReq) Reset() {
	*x = AddIntegralLogReq{}
	mi := &file_pb_account_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddIntegralLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddIntegralLogReq) ProtoMessage() {}

func (x *AddIntegralLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddIntegralLogReq.ProtoReflect.Descriptor instead.
func (*AddIntegralLogReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{26}
}

func (x *AddIntegralLogReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *AddIntegralLogReq) GetOperation() int32 {
	if x != nil {
		return x.Operation
	}
	return 0
}

func (x *AddIntegralLogReq) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AddIntegralLogReq) GetBalanceBefore() int64 {
	if x != nil {
		return x.BalanceBefore
	}
	return 0
}

func (x *AddIntegralLogReq) GetBalanceAfter() int64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

func (x *AddIntegralLogReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AddIntegralLogReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// ===== 获取账户日志 =====
type GetAccountLogReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemberId      int64                  `protobuf:"varint,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	Type          int32                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"` // 日志类型 1:余额 2:积分 3:成长值
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountLogReq) Reset() {
	*x = GetAccountLogReq{}
	mi := &file_pb_account_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountLogReq) ProtoMessage() {}

func (x *GetAccountLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountLogReq.ProtoReflect.Descriptor instead.
func (*GetAccountLogReq) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{27}
}

func (x *GetAccountLogReq) GetMemberId() int64 {
	if x != nil {
		return x.MemberId
	}
	return 0
}

func (x *GetAccountLogReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GetAccountLogReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAccountLogReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetAccountLogResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	List          []*AccountLog          `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	PageInfo      *PageInfo              `protobuf:"bytes,4,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountLogResp) Reset() {
	*x = GetAccountLogResp{}
	mi := &file_pb_account_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountLogResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountLogResp) ProtoMessage() {}

func (x *GetAccountLogResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_account_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountLogResp.ProtoReflect.Descriptor instead.
func (*GetAccountLogResp) Descriptor() ([]byte, []int) {
	return file_pb_account_proto_rawDescGZIP(), []int{28}
}

func (x *GetAccountLogResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAccountLogResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetAccountLogResp) GetList() []*AccountLog {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetAccountLogResp) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

var File_pb_account_proto protoreflect.FileDescriptor

const file_pb_account_proto_rawDesc = "" +
	"\n" +
	"\x10pb/account.proto\x12\aaccount\":\n" +
	"\n" +
	"CommonResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"Q\n" +
	"\bPageInfo\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x03R\x05total\"\xe8\x06\n" +
	"\rMemberAccount\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x1b\n" +
	"\tmember_id\x18\x04 \x01(\x03R\bmemberId\x12\x1f\n" +
	"\vmember_type\x18\x05 \x01(\x05R\n" +
	"memberType\x12\x1d\n" +
	"\n" +
	"user_money\x18\x06 \x01(\x01R\tuserMoney\x12)\n" +
	"\x10accumulate_money\x18\a \x01(\x01R\x0faccumulateMoney\x12\x1d\n" +
	"\n" +
	"give_money\x18\b \x01(\x01R\tgiveMoney\x12#\n" +
	"\rconsume_money\x18\t \x01(\x01R\fconsumeMoney\x12!\n" +
	"\ffrozen_money\x18\n" +
	" \x01(\x01R\vfrozenMoney\x12#\n" +
	"\ruser_integral\x18\v \x01(\x03R\fuserIntegral\x12/\n" +
	"\x13accumulate_integral\x18\f \x01(\x03R\x12accumulateIntegral\x12#\n" +
	"\rgive_integral\x18\r \x01(\x03R\fgiveIntegral\x12)\n" +
	"\x10consume_integral\x18\x0e \x01(\x01R\x0fconsumeIntegral\x12'\n" +
	"\x0ffrozen_integral\x18\x0f \x01(\x03R\x0efrozenIntegral\x12\x1f\n" +
	"\vuser_growth\x18\x10 \x01(\x03R\n" +
	"userGrowth\x12+\n" +
	"\x11accumulate_growth\x18\x11 \x01(\x03R\x10accumulateGrowth\x12%\n" +
	"\x0econsume_growth\x18\x12 \x01(\x03R\rconsumeGrowth\x12#\n" +
	"\rfrozen_growth\x18\x13 \x01(\x03R\ffrozenGrowth\x12'\n" +
	"\x0feconomize_money\x18\x14 \x01(\x01R\x0eeconomizeMoney\x124\n" +
	"\x16accumulate_drawn_money\x18\x15 \x01(\x01R\x14accumulateDrawnMoney\x12\x16\n" +
	"\x06status\x18\x16 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\x17 \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x18 \x01(\x03R\tupdatedAt\"\x9e\x02\n" +
	"\n" +
	"AccountLog\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tmember_id\x18\x02 \x01(\x03R\bmemberId\x12\x12\n" +
	"\x04type\x18\x03 \x01(\x05R\x04type\x12\x1c\n" +
	"\toperation\x18\x04 \x01(\x05R\toperation\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\x01R\x06amount\x12%\n" +
	"\x0ebalance_before\x18\x06 \x01(\x01R\rbalanceBefore\x12#\n" +
	"\rbalance_after\x18\a \x01(\x01R\fbalanceAfter\x12\x16\n" +
	"\x06reason\x18\b \x01(\tR\x06reason\x12\x16\n" +
	"\x06remark\x18\t \x01(\tR\x06remark\x12\x1d\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\x03R\tcreatedAt\"\x8c\x01\n" +
	"\x10CreateAccountReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x1f\n" +
	"\vmerchant_id\x18\x02 \x01(\x03R\n" +
	"merchantId\x12\x19\n" +
	"\bstore_id\x18\x03 \x01(\x03R\astoreId\x12\x1f\n" +
	"\vmember_type\x18\x04 \x01(\x05R\n" +
	"memberType\"`\n" +
	"\x11CreateAccountResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"account_id\x18\x03 \x01(\x03R\taccountId\"0\n" +
	"\x11GetAccountInfoReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"n\n" +
	"\x12GetAccountInfoResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12*\n" +
	"\x04data\x18\x03 \x01(\v2\x16.account.MemberAccountR\x04data\"\x82\x01\n" +
	"\x11GetAccountListReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x1b\n" +
	"\tmember_id\x18\x03 \x01(\x03R\bmemberId\x12\x1f\n" +
	"\vmember_type\x18\x04 \x01(\x05R\n" +
	"memberType\"\x9e\x01\n" +
	"\x12GetAccountListResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12*\n" +
	"\x04list\x18\x03 \x03(\v2\x16.account.MemberAccountR\x04list\x12.\n" +
	"\tpage_info\x18\x04 \x01(\v2\x11.account.PageInfoR\bpageInfo\",\n" +
	"\rGetBalanceReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"\xd0\x01\n" +
	"\x0eGetBalanceResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"user_money\x18\x03 \x01(\x01R\tuserMoney\x12)\n" +
	"\x10accumulate_money\x18\x04 \x01(\x01R\x0faccumulateMoney\x12!\n" +
	"\ffrozen_money\x18\x05 \x01(\x01R\vfrozenMoney\x12#\n" +
	"\rconsume_money\x18\x06 \x01(\x01R\fconsumeMoney\"\x9c\x01\n" +
	"\vRechargeReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12%\n" +
	"\x0epayment_method\x18\x03 \x01(\tR\rpaymentMethod\x12\x19\n" +
	"\border_no\x18\x04 \x01(\tR\aorderNo\x12\x16\n" +
	"\x06remark\x18\x05 \x01(\tR\x06remark\"|\n" +
	"\fRechargeResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x19\n" +
	"\border_no\x18\x03 \x01(\tR\aorderNo\x12#\n" +
	"\rbalance_after\x18\x04 \x01(\x01R\fbalanceAfter\"\x85\x01\n" +
	"\vWithdrawReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12)\n" +
	"\x10withdraw_account\x18\x03 \x01(\tR\x0fwithdrawAccount\x12\x16\n" +
	"\x06remark\x18\x04 \x01(\tR\x06remark\"\x82\x01\n" +
	"\fWithdrawResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1f\n" +
	"\vwithdraw_no\x18\x03 \x01(\tR\n" +
	"withdrawNo\x12#\n" +
	"\rbalance_after\x18\x04 \x01(\x01R\fbalanceAfter\"~\n" +
	"\x0eFreezeMoneyReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x1f\n" +
	"\voperator_id\x18\x04 \x01(\x03R\n" +
	"operatorId\"\x80\x01\n" +
	"\x10UnfreezeMoneyReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x1f\n" +
	"\voperator_id\x18\x04 \x01(\x03R\n" +
	"operatorId\"1\n" +
	"\x12GetIntegralInfoReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"\xed\x01\n" +
	"\x13GetIntegralInfoResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12#\n" +
	"\ruser_integral\x18\x03 \x01(\x03R\fuserIntegral\x12/\n" +
	"\x13accumulate_integral\x18\x04 \x01(\x03R\x12accumulateIntegral\x12'\n" +
	"\x0ffrozen_integral\x18\x05 \x01(\x03R\x0efrozenIntegral\x12)\n" +
	"\x10consume_integral\x18\x06 \x01(\x01R\x0fconsumeIntegral\"u\n" +
	"\x0eAddIntegralReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x03R\x06amount\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x16\n" +
	"\x06remark\x18\x04 \x01(\tR\x06remark\"y\n" +
	"\x12ConsumeIntegralReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x03R\x06amount\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x16\n" +
	"\x06remark\x18\x04 \x01(\tR\x06remark\"/\n" +
	"\x10GetGrowthInfoReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\"\xdb\x01\n" +
	"\x11GetGrowthInfoResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1f\n" +
	"\vuser_growth\x18\x03 \x01(\x03R\n" +
	"userGrowth\x12+\n" +
	"\x11accumulate_growth\x18\x04 \x01(\x03R\x10accumulateGrowth\x12%\n" +
	"\x0econsume_growth\x18\x05 \x01(\x03R\rconsumeGrowth\x12#\n" +
	"\rfrozen_growth\x18\x06 \x01(\x03R\ffrozenGrowth\"s\n" +
	"\fAddGrowthReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x03R\x06amount\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x16\n" +
	"\x06remark\x18\x04 \x01(\tR\x06remark\"\xdf\x01\n" +
	"\x0eAddMoneyLogReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x1c\n" +
	"\toperation\x18\x02 \x01(\x05R\toperation\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x01R\x06amount\x12%\n" +
	"\x0ebalance_before\x18\x04 \x01(\x01R\rbalanceBefore\x12#\n" +
	"\rbalance_after\x18\x05 \x01(\x01R\fbalanceAfter\x12\x16\n" +
	"\x06reason\x18\x06 \x01(\tR\x06reason\x12\x16\n" +
	"\x06remark\x18\a \x01(\tR\x06remark\"\xe2\x01\n" +
	"\x11AddIntegralLogReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x1c\n" +
	"\toperation\x18\x02 \x01(\x05R\toperation\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x03R\x06amount\x12%\n" +
	"\x0ebalance_before\x18\x04 \x01(\x03R\rbalanceBefore\x12#\n" +
	"\rbalance_after\x18\x05 \x01(\x03R\fbalanceAfter\x12\x16\n" +
	"\x06reason\x18\x06 \x01(\tR\x06reason\x12\x16\n" +
	"\x06remark\x18\a \x01(\tR\x06remark\"t\n" +
	"\x10GetAccountLogReq\x12\x1b\n" +
	"\tmember_id\x18\x01 \x01(\x03R\bmemberId\x12\x12\n" +
	"\x04type\x18\x02 \x01(\x05R\x04type\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\x9a\x01\n" +
	"\x11GetAccountLogResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12'\n" +
	"\x04list\x18\x03 \x03(\v2\x13.account.AccountLogR\x04list\x12.\n" +
	"\tpage_info\x18\x04 \x01(\v2\x11.account.PageInfoR\bpageInfo2\xb6\b\n" +
	"\x0eAccountService\x12F\n" +
	"\rCreateAccount\x12\x19.account.CreateAccountReq\x1a\x1a.account.CreateAccountResp\x12I\n" +
	"\x0eGetAccountInfo\x12\x1a.account.GetAccountInfoReq\x1a\x1b.account.GetAccountInfoResp\x12I\n" +
	"\x0eGetAccountList\x12\x1a.account.GetAccountListReq\x1a\x1b.account.GetAccountListResp\x12=\n" +
	"\n" +
	"GetBalance\x12\x16.account.GetBalanceReq\x1a\x17.account.GetBalanceResp\x127\n" +
	"\bRecharge\x12\x14.account.RechargeReq\x1a\x15.account.RechargeResp\x127\n" +
	"\bWithdraw\x12\x14.account.WithdrawReq\x1a\x15.account.WithdrawResp\x12;\n" +
	"\vFreezeMoney\x12\x17.account.FreezeMoneyReq\x1a\x13.account.CommonResp\x12?\n" +
	"\rUnfreezeMoney\x12\x19.account.UnfreezeMoneyReq\x1a\x13.account.CommonResp\x12L\n" +
	"\x0fGetIntegralInfo\x12\x1b.account.GetIntegralInfoReq\x1a\x1c.account.GetIntegralInfoResp\x12;\n" +
	"\vAddIntegral\x12\x17.account.AddIntegralReq\x1a\x13.account.CommonResp\x12C\n" +
	"\x0fConsumeIntegral\x12\x1b.account.ConsumeIntegralReq\x1a\x13.account.CommonResp\x12F\n" +
	"\rGetGrowthInfo\x12\x19.account.GetGrowthInfoReq\x1a\x1a.account.GetGrowthInfoResp\x127\n" +
	"\tAddGrowth\x12\x15.account.AddGrowthReq\x1a\x13.account.CommonResp\x12;\n" +
	"\vAddMoneyLog\x12\x17.account.AddMoneyLogReq\x1a\x13.account.CommonResp\x12A\n" +
	"\x0eAddIntegralLog\x12\x1a.account.AddIntegralLogReq\x1a\x13.account.CommonResp\x12F\n" +
	"\rGetAccountLog\x12\x19.account.GetAccountLogReq\x1a\x1a.account.GetAccountLogRespB\vZ\t./accountb\x06proto3"

var (
	file_pb_account_proto_rawDescOnce sync.Once
	file_pb_account_proto_rawDescData []byte
)

func file_pb_account_proto_rawDescGZIP() []byte {
	file_pb_account_proto_rawDescOnce.Do(func() {
		file_pb_account_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_account_proto_rawDesc), len(file_pb_account_proto_rawDesc)))
	})
	return file_pb_account_proto_rawDescData
}

var file_pb_account_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_pb_account_proto_goTypes = []any{
	(*CommonResp)(nil),          // 0: account.CommonResp
	(*PageInfo)(nil),            // 1: account.PageInfo
	(*MemberAccount)(nil),       // 2: account.MemberAccount
	(*AccountLog)(nil),          // 3: account.AccountLog
	(*CreateAccountReq)(nil),    // 4: account.CreateAccountReq
	(*CreateAccountResp)(nil),   // 5: account.CreateAccountResp
	(*GetAccountInfoReq)(nil),   // 6: account.GetAccountInfoReq
	(*GetAccountInfoResp)(nil),  // 7: account.GetAccountInfoResp
	(*GetAccountListReq)(nil),   // 8: account.GetAccountListReq
	(*GetAccountListResp)(nil),  // 9: account.GetAccountListResp
	(*GetBalanceReq)(nil),       // 10: account.GetBalanceReq
	(*GetBalanceResp)(nil),      // 11: account.GetBalanceResp
	(*RechargeReq)(nil),         // 12: account.RechargeReq
	(*RechargeResp)(nil),        // 13: account.RechargeResp
	(*WithdrawReq)(nil),         // 14: account.WithdrawReq
	(*WithdrawResp)(nil),        // 15: account.WithdrawResp
	(*FreezeMoneyReq)(nil),      // 16: account.FreezeMoneyReq
	(*UnfreezeMoneyReq)(nil),    // 17: account.UnfreezeMoneyReq
	(*GetIntegralInfoReq)(nil),  // 18: account.GetIntegralInfoReq
	(*GetIntegralInfoResp)(nil), // 19: account.GetIntegralInfoResp
	(*AddIntegralReq)(nil),      // 20: account.AddIntegralReq
	(*ConsumeIntegralReq)(nil),  // 21: account.ConsumeIntegralReq
	(*GetGrowthInfoReq)(nil),    // 22: account.GetGrowthInfoReq
	(*GetGrowthInfoResp)(nil),   // 23: account.GetGrowthInfoResp
	(*AddGrowthReq)(nil),        // 24: account.AddGrowthReq
	(*AddMoneyLogReq)(nil),      // 25: account.AddMoneyLogReq
	(*AddIntegralLogReq)(nil),   // 26: account.AddIntegralLogReq
	(*GetAccountLogReq)(nil),    // 27: account.GetAccountLogReq
	(*GetAccountLogResp)(nil),   // 28: account.GetAccountLogResp
}
var file_pb_account_proto_depIdxs = []int32{
	2,  // 0: account.GetAccountInfoResp.data:type_name -> account.MemberAccount
	2,  // 1: account.GetAccountListResp.list:type_name -> account.MemberAccount
	1,  // 2: account.GetAccountListResp.page_info:type_name -> account.PageInfo
	3,  // 3: account.GetAccountLogResp.list:type_name -> account.AccountLog
	1,  // 4: account.GetAccountLogResp.page_info:type_name -> account.PageInfo
	4,  // 5: account.AccountService.CreateAccount:input_type -> account.CreateAccountReq
	6,  // 6: account.AccountService.GetAccountInfo:input_type -> account.GetAccountInfoReq
	8,  // 7: account.AccountService.GetAccountList:input_type -> account.GetAccountListReq
	10, // 8: account.AccountService.GetBalance:input_type -> account.GetBalanceReq
	12, // 9: account.AccountService.Recharge:input_type -> account.RechargeReq
	14, // 10: account.AccountService.Withdraw:input_type -> account.WithdrawReq
	16, // 11: account.AccountService.FreezeMoney:input_type -> account.FreezeMoneyReq
	17, // 12: account.AccountService.UnfreezeMoney:input_type -> account.UnfreezeMoneyReq
	18, // 13: account.AccountService.GetIntegralInfo:input_type -> account.GetIntegralInfoReq
	20, // 14: account.AccountService.AddIntegral:input_type -> account.AddIntegralReq
	21, // 15: account.AccountService.ConsumeIntegral:input_type -> account.ConsumeIntegralReq
	22, // 16: account.AccountService.GetGrowthInfo:input_type -> account.GetGrowthInfoReq
	24, // 17: account.AccountService.AddGrowth:input_type -> account.AddGrowthReq
	25, // 18: account.AccountService.AddMoneyLog:input_type -> account.AddMoneyLogReq
	26, // 19: account.AccountService.AddIntegralLog:input_type -> account.AddIntegralLogReq
	27, // 20: account.AccountService.GetAccountLog:input_type -> account.GetAccountLogReq
	5,  // 21: account.AccountService.CreateAccount:output_type -> account.CreateAccountResp
	7,  // 22: account.AccountService.GetAccountInfo:output_type -> account.GetAccountInfoResp
	9,  // 23: account.AccountService.GetAccountList:output_type -> account.GetAccountListResp
	11, // 24: account.AccountService.GetBalance:output_type -> account.GetBalanceResp
	13, // 25: account.AccountService.Recharge:output_type -> account.RechargeResp
	15, // 26: account.AccountService.Withdraw:output_type -> account.WithdrawResp
	0,  // 27: account.AccountService.FreezeMoney:output_type -> account.CommonResp
	0,  // 28: account.AccountService.UnfreezeMoney:output_type -> account.CommonResp
	19, // 29: account.AccountService.GetIntegralInfo:output_type -> account.GetIntegralInfoResp
	0,  // 30: account.AccountService.AddIntegral:output_type -> account.CommonResp
	0,  // 31: account.AccountService.ConsumeIntegral:output_type -> account.CommonResp
	23, // 32: account.AccountService.GetGrowthInfo:output_type -> account.GetGrowthInfoResp
	0,  // 33: account.AccountService.AddGrowth:output_type -> account.CommonResp
	0,  // 34: account.AccountService.AddMoneyLog:output_type -> account.CommonResp
	0,  // 35: account.AccountService.AddIntegralLog:output_type -> account.CommonResp
	28, // 36: account.AccountService.GetAccountLog:output_type -> account.GetAccountLogResp
	21, // [21:37] is the sub-list for method output_type
	5,  // [5:21] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_pb_account_proto_init() }
func file_pb_account_proto_init() {
	if File_pb_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_account_proto_rawDesc), len(file_pb_account_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_account_proto_goTypes,
		DependencyIndexes: file_pb_account_proto_depIdxs,
		MessageInfos:      file_pb_account_proto_msgTypes,
	}.Build()
	File_pb_account_proto = out.File
	file_pb_account_proto_goTypes = nil
	file_pb_account_proto_depIdxs = nil
}
