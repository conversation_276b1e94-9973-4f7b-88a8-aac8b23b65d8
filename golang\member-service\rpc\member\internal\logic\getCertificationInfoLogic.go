package logic

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"member/internal/svc"
	"member/member"
)

type GetCertificationInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCertificationInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCertificationInfoLogic {
	return &GetCertificationInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 实名认证管理
func (l *GetCertificationInfoLogic) GetCertificationInfo(in *member.GetCertificationInfoReq) (*member.GetCertificationInfoResp, error) {

	if in.MemberId <= 0 {
		return &member.GetCertificationInfoResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	// 调用数据库开始查询认证信息
	certificationData, err := l.svcCtx.MemberCertificationModel.FindByMemberID(in.MemberId)

	if err != nil {
		// 检查是否是记录不存在的错误
		if err.Error() == fmt.Sprintf("会员ID=%d的认证不存在", in.MemberId) {
			return &member.GetCertificationInfoResp{
				Code:    404,
				Message: "该用户尚未提交实名认证信息",
			}, nil
		}
		return &member.GetCertificationInfoResp{
			Code:    500,
			Message: "查询认证信息失败",
		}, nil
	}

	return &member.GetCertificationInfoResp{
		Code:    200,
		Message: "查询认证信息成功",
		Data: &member.MemberCertification{
			Id:                certificationData.ID,
			MerchantId:        certificationData.MerchantID,
			StoreId:           certificationData.StoreID,
			MemberId:          certificationData.MemberID,
			MemberType:        certificationData.MemberType,
			Realname:          certificationData.Realname,
			IdentityCard:      certificationData.IdentityCard,
			IdentityCardFront: certificationData.FrontImage,
			IdentityCardBack:  certificationData.BackImage,
			Gender:            certificationData.Gender,
			Birthday:          safeStringValue(certificationData.Birthday),
			FrontIsFake:       certificationData.FrontIsFake,
			BackIsFake:        certificationData.BackIsFake,
			Nationality:       certificationData.Nationality,
			Address:           certificationData.Address,
			StartDate:         safeStringValue(certificationData.StartDate),
			EndDate:           safeStringValue(certificationData.EndDate),
			Issue:             certificationData.Issue,
			IsSelf:            certificationData.IsSelf,
			Status:            certificationData.Status,
			CreatedAt:         certificationData.CreatedAt,
			UpdatedAt:         certificationData.UpdatedAt,
		},
	}, nil
}

// safeStringValue 安全地获取字符串指针的值，如果为nil则返回空字符串
func safeStringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}
