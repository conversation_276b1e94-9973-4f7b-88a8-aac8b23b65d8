syntax = "v1"

// ===== 用户统计信息结构 =====
type MemberStat {
	Id           int64 `json:"id"`
	MerchantId   int64 `json:"merchant_id"`
	StoreId      int64 `json:"store_id"`
	MemberId     int64 `json:"member_id"`
	MemberType   int   `json:"member_type"`
	NiceNum      int64 `json:"nice_num"` // 点赞数量
	DisagreeNum  int64 `json:"disagree_num"` // 不赞同数量
	TransmitNum  int64 `json:"transmit_num"` // 转发数量
	CommentNum   int64 `json:"comment_num"` // 评论数量
	CollectNum   int64 `json:"collect_num"` // 收藏数量
	ReportNum    int64 `json:"report_num"` // 举报数量
	RecommendNum int64 `json:"recommend_num"` // 推荐数量
	FollowNum    int64 `json:"follow_num"` // 关注人数
	AllowedNum   int64 `json:"allowed_num"` // 被关注人数
	View         int64 `json:"view"` // 浏览量
	Status       int   `json:"status"`
	CreatedAt    int64 `json:"created_at"`
	UpdatedAt    int64 `json:"updated_at"`
}

// ===== 获取用户统计信息 =====
type GetMemberStatReq {
	MemberId int64 `form:"member_id,optional"`
}

type GetMemberStatResp {
	Code    int        `json:"code"`
	Message string     `json:"message"`
	Data    MemberStat `json:"data"`
}

// ===== 统计报表请求 =====
type GetStatReportReq {
	StartDate string `form:"start_date,optional"`
	EndDate   string `form:"end_date,optional"`
	Page      int    `form:"page,default=1"`
	PageSize  int    `form:"page_size,default=10"`
}

// ===== 统计报表数据 =====
type StatReportData {
	TotalMembers  int64   `json:"total_members"` // 总用户数
	ActiveMembers int64   `json:"active_members"` // 活跃用户数
	TotalViews    int64   `json:"total_views"` // 总浏览量
	TotalLikes    int64   `json:"total_likes"` // 总点赞数
	TotalCollects int64   `json:"total_collects"` // 总收藏数
	TotalFollows  int64   `json:"total_follows"` // 总关注数
	AvgActivity   float64 `json:"avg_activity"` // 平均活跃度
}

type GetStatReportResp {
	Code    int            `json:"code"`
	Message string         `json:"message"`
	Data    StatReportData `json:"data"`
}

// ===== 更新统计信息 =====
type UpdateMemberStatReq {
	MemberId     int64 `json:"member_id"`
	NiceNum      int64 `json:"nice_num,optional"`
	DisagreeNum  int64 `json:"disagree_num,optional"`
	TransmitNum  int64 `json:"transmit_num,optional"`
	CommentNum   int64 `json:"comment_num,optional"`
	CollectNum   int64 `json:"collect_num,optional"`
	ReportNum    int64 `json:"report_num,optional"`
	RecommendNum int64 `json:"recommend_num,optional"`
	FollowNum    int64 `json:"follow_num,optional"`
	AllowedNum   int64 `json:"allowed_num,optional"`
	View         int64 `json:"view,optional"`
}

// ===== 增加统计数据 =====
type IncreaseStatReq {
	MemberId int64 `json:"member_id"`
	Count    int64 `json:"count,default=1"`
}

// ===== 注意：排行榜、活跃度报表、导出等功能暂未实现，已移除相关类型定义 =====
// 如需要这些功能，请在实现对应API接口后重新添加类型定义
